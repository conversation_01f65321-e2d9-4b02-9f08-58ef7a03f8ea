package domain

import (
	"time"

	"gitlab.dailyyoga.com.cn/rdc/serverops/database"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
)

type CDNDomain struct {
	ID         int64  `xorm:"int(11)  autoincr pk 'id'" json:"id,omitempty"`
	Remark     string `xorm:"'remark' varchar(50)" json:"remark,omitempty"`
	Project    string `xorm:"'project' varchar(50)" json:"project,omitempty"`
	HostRecord string `xorm:"'host_record' varchar(50)" json:"host_record,omitempty"`
	DomainName string `xorm:"'domain_name' varchar(100)" json:"domain_name,omitempty"`
	RecordID   string `xorm:"'record_id' varchar(20)" json:"record_id,omitempty"`
	IsDelete   int32  `xorm:"'is_del' bool" json:"is_del,omitempty"`
	CreateTime int64  `xorm:"'create_time' int(10) " json:"create_time,omitempty"`
	UpdateTime int64  `xorm:"'update_time' int(10) " json:"update_time,omitempty"`
}

func (c *CDNDomain) TableName() string {
	return "domain_cdn"
}

func (c *CDNDomain) Update() error {
	c.UpdateTime = time.Now().Unix()
	_, err := database.GetEngine().ID(c.ID).Update(c)
	return err
}

type cdnDomain struct{}

var TbCDNDomain cdnDomain

func (c *cdnDomain) GetItemByID(id int64) *CDNDomain {
	var table CDNDomain
	ok, err := database.GetEngine().Where("id =?", id).Get(&table)
	if !ok {
		return nil
	}
	if err != nil {
		logger.Error(err)
		return nil
	}
	return &table
}

func (c *cdnDomain) GetItemByDomainName(domainName string) *CDNDomain {
	var table CDNDomain
	ok, err := database.GetEngine().Where("domain_name = ? and is_del = ?", domainName, 2).Get(&table)
	if !ok {
		return nil
	}
	if err != nil {
		logger.Error(err)
		return nil
	}
	return &table
}

func (c *cdnDomain) GetList() []*CDNDomain {
	var tables []*CDNDomain
	if err := database.GetEngine().Where(" is_del = ?", 2).Find(&tables); err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (c *cdnDomain) GetListByProject(project string) []*CDNDomain {
	var tables []*CDNDomain
	if err := database.GetEngine().Where("project = ? and is_del = ? ", project, 2).Find(&tables); err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
