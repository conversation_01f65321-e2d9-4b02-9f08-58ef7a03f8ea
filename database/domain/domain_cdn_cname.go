package domain

import (
	"time"

	"gitlab.dailyyoga.com.cn/rdc/serverops/database"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
)

type CDNDomainCname struct {
	ID          int64  `xorm:"int(11)  autoincr pk 'id'" json:"id,omitempty"`
	DomainCdnID int64  `xorm:"'domain_cdn_id' int(10)" json:"domain_cdn_id,omitempty"`
	Remark      string `xorm:"'remark' varchar(50)" json:"remark,omitempty"`
	CName       string `xorm:"'cname' varchar(100)" json:"cname,omitempty"`
	IsDelete    int32  `xorm:"'is_del' bool" json:"is_del,omitempty"`
	CreateTime  int64  `xorm:"'create_time' int(10) " json:"create_time,omitempty"`
	UpdateTime  int64  `xorm:"'update_time' int(10) " json:"update_time,omitempty"`
}

func (c *CDNDomainCname) TableName() string {
	return "domain_cdn_cname"
}

func (c *CDNDomainCname) Update() error {
	c.UpdateTime = time.Now().Unix()
	_, err := database.GetEngine().ID(c.ID).Update(c)
	return err
}

type cdnDomainCname struct{}

var TbCDNDomainCname cdnDomainCname

func (c *cdnDomainCname) GetList() []*CDNDomainCname {
	var tables []*CDNDomainCname
	if err := database.GetEngine().Where("is_del = ?", 2).Find(&tables); err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (c *cdnDomainCname) GetListByDCdnID(dCdnID int64) []*CDNDomainCname {
	var tables []*CDNDomainCname
	if err := database.GetEngine().Where(" domain_cdn_id = ? and is_del = ?", dCdnID, 2).Find(&tables); err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
