package deployment

import (
	"encoding/json"
	"time"

	"github.com/pkg/errors"
	"gitlab.dailyyoga.com.cn/rdc/serverops/database"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
)

type Mapping struct {
	ID                     int64                   `xorm:"int(11) autoincr pk 'id'" json:"id"`
	ProjectID              int                     `xorm:"int(11) 'project_id'" json:"project_id"`
	ProjectName            string                  `xorm:"varchar(100) 'project_name'" json:"project_name"`
	ProjectBranch          string                  `xorm:"varchar(100) 'project_branch'" json:"project_branch"`
	DeployChannel          DeployChannel           `xorm:"varchar(100) 'deploy_channel'" json:"deploy_channel"`
	DeployParams           string                  `xorm:"varchar(1024) 'deploy_params'" json:"-"`
	KubernetesDeployParams *KubernetesDeployParams `xorm:"-" json:"kubernetes_deploy_params,omitempty"`
	JenkinsDeployParams    *JenkinsDeployParams    `xorm:"-" json:"jenkins_deploy_params,omitempty"`
	CreateAt               int64                   `xorm:"int(11) 'created_at'" json:"-"`
	UpdateAt               int64                   `xorm:"int(11) 'updated_at'" json:"-"`
}

func (m *Mapping) TableName() string {
	return "deployment_mapping"
}

func (m *Mapping) Save() error {
	m.CreateAt = time.Now().Unix()
	m.UpdateAt = m.CreateAt

	_, err := database.GetEngine().Insert(m)
	return err
}

func (m *Mapping) Delete() error {
	if m.ID == 0 {
		return errors.New("record not found")
	}
	_, err := database.GetEngine().Delete(m)
	return err
}

func (m *Mapping) build() *Mapping {
	if m.DeployChannel == DeployKubernetesChannel {
		m.KubernetesDeployParams = &KubernetesDeployParams{}
		if err := json.Unmarshal([]byte(m.DeployParams), m.KubernetesDeployParams); err != nil {
			logger.Error(err)
			return m
		}
	}
	if m.DeployChannel == DeployJenkinsChannel {
		m.JenkinsDeployParams = &JenkinsDeployParams{}
		if err := json.Unmarshal([]byte(m.DeployParams), m.JenkinsDeployParams); err != nil {
			logger.Error(err)
			return m
		}
	}
	return m
}

type mapping struct{}

var TbMapping mapping

func (tb *mapping) GetMItem(projectID int64, projectBranch string) (*Mapping, error) {
	var table Mapping
	ok, err := database.GetEngine().Where("project_id = ?", projectID).
		Where("project_branch = ?", projectBranch).
		Limit(1).Get(&table)
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, errors.New("item not found")
	}

	return table.build(), nil
}

func (tb *mapping) GetMItemByID(id int64) (*Mapping, error) {
	var table Mapping
	ok, err := database.GetEngine().ID(id).Get(&table)
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, errors.New("item not found")
	}
	return table.build(), nil
}

// lists
type searchOpt struct {
	project string        `search_field:"project_name"`
	branch  string        `search_field:"project_branch"`
	channel DeployChannel `search_field:"deploy_channel"`
}

type ListOption func(*searchOpt)

func ListWithProject(project string) ListOption {
	return func(opt *searchOpt) {
		opt.project = project
	}
}

func ListWithBranch(branch string) ListOption {
	return func(s *searchOpt) {
		s.branch = branch
	}
}

func ListWithChannel(channel DeployChannel) ListOption {
	return func(s *searchOpt) {
		s.channel = channel
	}
}

func makeListSearchOpts(opts []ListOption) *searchOpt {
	opt := &searchOpt{}
	for _, fn := range opts {
		fn(opt)
	}
	return opt
}

func (tb *mapping) Lists(opts ...ListOption) []*Mapping {
	var tables []*Mapping

	opt := makeListSearchOpts(opts)

	session := database.GetEngine().NewSession()
	if opt.channel != "" {
		session.Where("deploy_channel = ?", opt.channel)
	}
	if opt.project != "" {
		session.Where("project_name = ?", opt.project)
	}
	if opt.branch != "" {
		session.Where("project_branch = ?", opt.branch)
	}

	if err := session.Desc("id").Find(&tables); err != nil {
		logger.Error(err)
		return nil
	}

	rets := make([]*Mapping, 0)
	for i := range tables {
		rets = append(rets, tables[i].build())
	}
	return rets
}
