package deployment

type KubernetesDeployParams struct {
	ClusterName    ClusterName         `json:"cluster_name"`
	Namespace      KubernetesNamespace `json:"namespace"`
	DeploymentName string              `json:"deployment_name"`
}

type JenkinsDeployParams struct {
	JobName string            `json:"job_name"`
	Params  map[string]string `json:"params"`
}

type DeployChannel string

const DeployJenkinsChannel = "jenkins"
const DeployKubernetesChannel = "kubernetes"
const DeployGitlabCIChannel = "gitlab-ci"

type ClusterName string

const DevCluster ClusterName = "dev-cluster"
const ProdCluster ClusterName = "prod-cluster"
const ServerlessCluster ClusterName = "serverless-cluster"

type KubernetesNamespace string

const DevNamespace KubernetesNamespace = "dailyyoga"
const DevInternalNamespace KubernetesNamespace = "internal"
const DevChildrenNamespace KubernetesNamespace = "children"
const MirrorNamespace KubernetesNamespace = "dailyyoga-mirror"
const ProductNamespace KubernetesNamespace = "dailyyoga-prod"
const InternalNamespace KubernetesNamespace = "internal"
const MirrorChildrenNamespace KubernetesNamespace = "children-mirror"
const ProductChildrenNamespace KubernetesNamespace = "children-prod"
