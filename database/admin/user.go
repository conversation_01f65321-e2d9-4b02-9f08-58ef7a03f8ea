package admin

import (
	"github.com/pkg/errors"
	"gitlab.dailyyoga.com.cn/rdc/serverops/database"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
)

type User struct {
	UID      int    `xorm:"int(11) autoincr pk 'uid'" json:"uid"`
	Nickname string `xorm:"varchar(100) 'nickname'" json:"nickname"`
	RoleID   string `xorm:"varchar(50) 'role_id'" json:"role_id"`
	Username string `xorm:"varchar(100) 'username'" json:"username"`
	Email    string `xorm:"varchar(255) 'email'" json:"email"`
	Password string `xorm:"varchar(64) 'password'" json:"-"`
}

const RoleIDReadonly = "readonly"
const RoleIDDevelop = "develop"
const RoleIDAdmin = "admin"

var RoleOperation = map[string][]string{
	RoleIDAdmin:    {"save", "delete", "update"},
	RoleIDDevelop:  {"save", "update"},
	RoleIDReadonly: {},
}

func (User) TableName() string {
	return "admin_user"
}

func (u *User) Save() error {
	_, err := database.GetEngine().Insert(u)
	return err
}

func (u *User) Update() error {
	if u.UID == 0 {
		return errors.New("user not found")
	}

	_, err := database.GetEngine().ID(u.UID).Update(u)
	return err
}

func (u *User) Delete() error {
	if u.UID == 0 {
		return errors.New("user not found")
	}
	_, err := database.GetEngine().ID(u.UID).Delete(u)
	return err
}

type user struct{}

var TbUser user

func (u *user) GetAllUsers() []User {
	var tables []User

	if err := database.GetEngine().Find(&tables); err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (u *user) GetItemByUID(uid int) *User {
	var table User

	ok, err := database.GetEngine().ID(uid).Limit(1).Get(&table)
	if err != nil {
		logger.Warn(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (u *user) CheckUser(username, password string) (*User, error) {
	if username == "" || password == "" {
		return nil, errors.New("username or password is empty")
	}

	var table User

	ok, err := database.GetEngine().Where("username = ?", username).Or("email = ?", username).
		Limit(1).Get(&table)
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, errors.New("not found")
	}

	if table.Password != password {
		return nil, errors.New("wrong password")
	}

	return &table, nil
}
