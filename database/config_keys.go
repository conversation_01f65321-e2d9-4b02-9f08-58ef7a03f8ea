package database

import (
	"time"

	"github.com/pkg/errors"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
)

type ConfigKeys struct {
	ID          int64  `xorm:"int(11) autoincr pk id" json:"id"`
	Key         string `xorm:"varchar(100) not null 'key'" json:"key"`
	Value       string `xorm:"varchar(1024) not null 'value'" json:"value"`
	ValueType   string `xorm:"varchar(20) not null 'value_type'" json:"value_type"`
	Description string `xorm:"varchar(500) not null 'description'" json:"description"`
	CreateAt    int64  `xorm:"int(11) 'created_at'" json:"create_at"`
	UpdateAt    int64  `xorm:"int(11) 'updated_at'" json:"update_at"`
}

func (c *ConfigKeys) TableName() string {
	return "config_keys"
}

func (c *ConfigKeys) Save() error {
	c.CreateAt = time.Now().Unix()
	c.UpdateAt = c.CreateAt

	_, err := GetEngine().Insert(c)
	return err
}

func (c *ConfigKeys) Update() error {
	if c.Key == "" {
		return errors.New("config not found")
	}
	_, err := GetEngine().Where("`key` = ?", c.Key).Update(c)
	return err
}

func (c *ConfigKeys) Delete() error {
	if c.Key == "" || c.ID == 0 {
		return errors.New("config not found")
	}
	_, err := GetEngine().Delete(c)
	return err
}

type configKeys struct{}

var TbConfigKeys configKeys

func (tb *configKeys) FetchAll() map[string]*ConfigKeys {
	var tables []*ConfigKeys
	if err := GetEngine().Desc("id").Find(&tables); err != nil {
		logger.Error(err)
		return nil
	}

	ret := make(map[string]*ConfigKeys)
	for _, v := range tables {
		if _, ok := ret[v.Key]; !ok {
			ret[v.Key] = v
		}
	}
	return ret
}

func (tb *configKeys) Get(key string) *ConfigKeys {
	var table ConfigKeys

	ok, err := GetEngine().Where("`key` = ?", key).Get(&table)
	if !ok {
		return nil
	}
	if err != nil {
		logger.Error(err)
		return nil
	}
	return &table
}
