package wework

import (
	"encoding/json"
	"time"

	"github.com/pkg/errors"
	"gitlab.dailyyoga.com.cn/rdc/serverops/database"
	"gitlab.dailyyoga.com.cn/rdc/serverops/database/deployment"
)

type MessageRecord struct {
	ID             int64               `xorm:"int(11) unsigned autoincr pk 'id'"`
	TaskID         string              `xorm:"varchar(100) 'task_id'"`
	MsgID          string              `xorm:"varchar(100) 'msg_id'"`
	MsgType        string              `xorm:"varchar(100) 'msg_type'"`
	RespCode       string              `xorm:"varchar(100) 'resp_code'"`
	RelatedInfo    string              `xorm:"varchar(1024) 'related_info'"`
	RelatedObject  *RelatedObject      `xorm:"-"`
	FeedbackOp     string              `xorm:"varchar(500) 'feedback_op'"`
	FeedbackOpUser string              `xorm:"varchar(100) 'feedback_op_user'"`
	FeedbackTime   int64               `xorm:"int(11) 'feedback_time'"`
	CreatedAt      int64               `xorm:"int(11) 'created_at'"`
	UpdatedAt      int64               `xorm:"int(11) 'updated_at'"`
	RelatedMapping *deployment.Mapping `xorm:"-"`
}

type RelatedObject struct {
	MappingID       int64 `json:"mapping_id,omitempty"`
	MergeRequestIID int   `json:"merge_request_iid,omitempty"`
}

func (m *MessageRecord) TableName() string {
	return "wework_message_record"
}

func (m *MessageRecord) Save() error {
	m.CreatedAt = time.Now().Unix()
	m.UpdatedAt = m.CreatedAt

	b, _ := json.Marshal(m.RelatedObject)
	m.RelatedInfo = string(b)

	_, err := database.GetEngine().Insert(m)
	return err
}

func (m *MessageRecord) Update() error {
	if m.ID == 0 && m.TaskID == "" {
		return errors.New("not found, err: invalid params")
	}
	m.UpdatedAt = time.Now().Unix()

	if m.ID != 0 {
		_, err := database.GetEngine().ID(m.ID).Update(m)
		return err
	}
	_, err := database.GetEngine().Where("task_id = ?", m.TaskID).Update(m)
	return err
}

type messageRecord struct{}

var TbMessageRecord messageRecord

func (m *messageRecord) GetItemByTaskID(taskID string) (*MessageRecord, error) {
	var table MessageRecord

	ok, err := database.GetEngine().Where("task_id = ?", taskID).Limit(1).Get(&table)
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, errors.New("item not found")
	}

	info := RelatedObject{}
	if err := json.Unmarshal([]byte(table.RelatedInfo), &info); err != nil {
		return nil, err
	}
	table.RelatedObject = &info

	if table.RelatedObject.MappingID > 0 {
		mapping, err := deployment.TbMapping.GetMItemByID(table.RelatedObject.MappingID)
		if err != nil {
			return nil, err
		}
		table.RelatedMapping = mapping
	}

	return &table, nil
}
