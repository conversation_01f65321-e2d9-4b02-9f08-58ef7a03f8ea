package wework

import (
	"errors"
	"time"

	"gitlab.dailyyoga.com.cn/rdc/serverops/database"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
)

type Users struct {
	ID        int64  `xorm:"int(11) autoincr pk 'id'" json:"-"`
	AgentID   int    `xorm:"int(11) 'agent_id'" json:"agent_id"`
	UserID    string `xorm:"varchar(100) 'user_id'" json:"user_id"`
	Name      string `xorm:"varchar(100) 'name'" json:"name"`
	CreatedAt int64  `xorm:"int(11) 'created_at'" json:"created_at"`
	UpdatedAt int64  `xorm:"int(11) 'updated_at'" json:"updated_at"`
}

func (u *Users) TableName() string {
	return "wework_users"
}

func (u *Users) Save() error {
	u.CreatedAt = time.Now().Unix()
	u.UpdatedAt = u.CreatedAt

	_, err := database.GetEngine().Insert(u)
	return err
}

type users string

var TbUsers users

func (tb *users) GetItemByName(agentID int, name string) (*Users, error) {
	var table Users
	ok, err := database.GetEngine().
		Where("agent_id = ?", agentID).Where("name = ?", name).Limit(1).Get(&table)
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, errors.New("item not found")
	}
	return &table, nil
}

func (tb *users) GetItemByUserID(agentID int, userid string) (*Users, error) {
	var table Users
	ok, err := database.GetEngine().
		Where("agent_id = ?", agentID).Where("user_id = ?", userid).Limit(1).Get(&table)
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, errors.New("item not found")
	}
	return &table, nil
}

func (tb *users) GetUsers(agentID int, userids []string) []*Users {
	var tables []*Users
	if err := database.GetEngine().Where("agent_id = ?", agentID).
		In("user_id", userids).Find(&tables); err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (tb *users) FetchAllUserID(agentID int) []string {
	var tables []Users

	if err := database.GetEngine().Where("agent_id = ?", agentID).Find(&tables); err != nil {
		logger.Error(err)
		return nil
	}

	rets := make([]string, 0)
	for i := range tables {
		rets = append(rets, tables[i].UserID)
	}
	return rets
}
