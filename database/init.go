package database

import (
	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/rdc/serverops/config"
	"gitlab.dailyyoga.com.cn/server/go-artifact/database"
)

var dbConnect *database.Connect
var serviceManagerConnect *database.Connect

func Init(cfg *config.Conf) error {
	dbConnect = database.Init()
	serviceManagerConnect = database.Init()

	if err := dbConnect.AddConnect(cfg.ServeropsDB.Master.String(), database.Master); err != nil {
		return err
	}

	if err := serviceManagerConnect.AddConnect(
		cfg.ServiceManagerDB.Master.String(), database.Master,
	); err != nil {
		return err
	}

	return nil
}

func GetEngine() *xorm.Engine {
	return dbConnect.GetEngineMaster()
}

func GetServiceManagerEngine() *xorm.Engine {
	return serviceManagerConnect.GetEngineMaster()
}
