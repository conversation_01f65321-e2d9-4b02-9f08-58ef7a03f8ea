package cronmanager

import (
	"time"

	"gitlab.dailyyoga.com.cn/rdc/serverops/database"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
)

type Log struct {
	ID            int64  `xorm:"autoincr pk int(11) 'id'" json:"id"`
	ServiceName   string `xorm:"varchar(100) 'service_name'" json:"service_name"`
	ServiceEnv    string `xorm:"varchar(10) 'service_env'" json:"service_env"`
	ServiceID     string `xorm:"varchar(100) 'service_id'" json:"service_id"`
	Hostname      string `xorm:"varchar(100) 'hostname'" json:"hostname"`
	TaskName      string `xorm:"varchar(100) 'task_name'" json:"task_name"`
	TaskID        string `xorm:"varchar(100) 'task_id'" json:"task_id"`
	TaskSpec      string `xorm:"varchar(100) 'task_spec'" json:"task_spec"`
	TaskDesc      string `xorm:"varchar(255) 'task_desc'" json:"task_desc"`
	StartTime     int64  `xorm:"int(11) 'start_time'" json:"start_time"`
	EndTime       int64  `xorm:"int(11) 'end_time'" json:"end_time"`
	Status        int    `xorm:"tinyint(1) 'status'" json:"status"`
	AlarmDisabled int    `xorm:"tinyint(1) 'alarm_disabled'" json:"alarm_disabled"`
	ErrorInfo     string `xorm:"varchar(1024) 'error_info'" json:"error_info"`
	CreatedAt     int64  `xorm:"int(11) 'created_at'" json:"created_at"`
	UpdatedAt     int64  `xorm:"int(11) 'updated_at'" json:"updated_at"`
}

func (l *Log) TableName() string {
	return "cron_task_log"
}

func (l *Log) Save() error {
	l.CreatedAt = time.Now().Unix()
	l.UpdatedAt = l.CreatedAt

	_, err := database.GetEngine().Insert(l)
	return err
}

type log struct{}

var TbLog log

type fetchOption struct {
	taskID    string
	serviceID string
	limit     int
}
type FetchOption func(f *fetchOption)

func FetchWithTask(id string) FetchOption {
	return func(f *fetchOption) {
		f.taskID = id
	}
}

func FetchWithService(id string) FetchOption {
	return func(f *fetchOption) {
		f.serviceID = id
	}
}

func FetchWithLimit(limit int) FetchOption {
	return func(f *fetchOption) {
		f.limit = limit
	}
}

func (tl *log) FetchTaskLogs(options ...FetchOption) []Log {
	var tables []Log

	foptions := &fetchOption{limit: 20}
	for _, opt := range options {
		opt(foptions)
	}

	session := database.GetEngine().NewSession()
	if foptions.taskID != "" {
		session.Where(" `task_id` = ?", foptions.taskID)
	}
	if foptions.serviceID != "" {
		session.Where("`service_id` = ?", foptions.serviceID)
	}

	if err := session.Desc("id").Limit(foptions.limit).Find(&tables); err != nil {
		logger.Errorf("fetch task logs, err: %s", err)
		return nil
	}
	return tables
}
