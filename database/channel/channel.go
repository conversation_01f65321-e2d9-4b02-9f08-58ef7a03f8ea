package channel

import (
	"errors"
	"time"

	"gitlab.dailyyoga.com.cn/rdc/serverops/database"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
)

type ApkChannel struct {
	ID          int64  `xorm:"int(11) unsigned autoincr pk 'id'" json:"id,omitempty"`
	Platform    string `xorm:"'platform' varchar(3)" json:"platform,omitempty"`
	OS          string `xorm:"'os' varchar(10)" json:"os,omitempty"`
	ChannelName string `xorm:"'channel_name' varchar(100)" json:"channel_name,omitempty"`
	ChannelID   string `xorm:"'channel_id' varchar(100)" json:"channel_id,omitempty"`
	ChannelDesc string `xorm:"'channel_desc' varchar(100)" json:"channel_desc,omitempty"`
	CreateTime  int64  `xorm:"'create_time' int(11)" json:"create_time,omitempty"`
	UpdateTime  int64  `xorm:"'update_time' int(11)" json:"update_time,omitempty"`
}

func (a *ApkChannel) TableName() string {
	return "app_package_channel"
}

func (a *ApkChannel) Save() error {
	a.CreateTime = time.Now().Unix()
	a.UpdateTime = a.CreateTime

	_, err := database.GetServiceManagerEngine().Insert(a)
	return err
}

func (a *ApkChannel) Delete() error {
	if a.ID == 0 {
		return errors.New("delete record undefined")
	}

	_, err := database.GetServiceManagerEngine().ID(a.ID).Delete(a)
	return err
}

type apkChannel struct{}

var TbApkChannel apkChannel

func (a *apkChannel) GetItem(id int64) *ApkChannel {
	var table ApkChannel

	ok, err := database.GetServiceManagerEngine().ID(id).Get(&table)
	if !ok {
		return nil
	}
	if err != nil {
		logger.Error(err)
		return nil
	}
	return &table
}

func (a *apkChannel) Lists() []*ApkChannel {
	var tables []*ApkChannel

	if err := database.GetServiceManagerEngine().Desc("id").Find(&tables); err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
