package tapd

import (
	"gitlab.dailyyoga.com.cn/rdc/serverops/database"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
)

type Iteration struct {
	// unsigned 表示 mySql中 非负数，-128-127 -> 0-255
	// omitempty 表示 struct 和 json 互转时，剔除不必要的字段 注：对struct不生效，对指针生效
	ID          int64  `xorm:"int(11) unsigned autoincr pk 'id'" json:"id"`
	IterationID string `xorm:"'iteration_id' varchar(100)" json:"iteration_id"`
	Name        string `xorm:"'name' varchar(100)" json:"name"`
	Start       int64  `xorm:"'start' int(11)" json:"start"`
	End         int64  `xorm:"'end' int(11)" json:"end"`
	CreateTime  int64  `xorm:"'created_at' int(11)" json:"created_at,omitempty"`
	UpdateTime  int64  `xorm:"'update_at' int(11)" json:"update_at,omitempty"`
}

func (i *Iteration) TableName() string {
	return "tapd_iteration"
}

type iteration struct{}

var TbIteration iteration

func (t *iteration) GetItem(iterationID string) *Iteration {
	var table Iteration
	_, err := database.GetEngine().Where("iteration_id = ?", iterationID).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return &table
}
