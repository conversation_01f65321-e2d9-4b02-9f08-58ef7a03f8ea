package tapd

import (
	"fmt"
	"gitlab.dailyyoga.com.cn/rdc/serverops/database"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
)

type Story struct {
	// unsigned 表示 mySql中 非负数，-128-127 -> 0-255
	// omitempty 表示 struct 和 json 互转时，剔除不必要的字段 注：对struct不生效，对指针生效
	ID          int64   `xorm:"int(11) unsigned autoincr pk 'id'" json:"id"`
	StoryID     string  `xorm:"'story_id' varchar(100)" json:"story_id"`
	IterationID string  `xorm:"'iteration_id' varchar(100)" json:"iteration_id"`
	Name        string  `xorm:"'name' varchar(100)" json:"name"`
	Effort      float64 `xorm:"'effort' double" json:"effort"`
	ProductLine string  `xorm:"'product_line' varchar(100)" json:"product_line"`
	ProductType string  `xorm:"'product_type' varchar(50)" json:"product_type"`
	CreateTime  int64   `xorm:"'created_at' int(11)" json:"created_at,omitempty"`
	UpdateTime  int64   `xorm:"'update_at' int(11)" json:"update_at,omitempty"`
}

func (i *Story) TableName(start, end, year, month int64) string {
	if start == 0 && end == 0 {
		// 2024 11月新拆分的逻辑
		return fmt.Sprintf("tapd_story_%d_%02d", year, month) // 例如: tapd_story_2024_11
	} else {
		return "tapd_story"
	}
}

type story struct{}

var TbStory story

type StoriesItem struct {
	ID          int64   `xorm:"id int(11)" json:"id"`
	StoryID     string  `xorm:"story_id varchar" json:"story_id"`
	IterationID string  `xorm:"iteration_id varchar" json:"iteration_id"`
	Name        string  `xorm:"name varchar" json:"name"`
	Effort      float64 `xorm:"effort double" json:"effort"`
	ProductLine string  `xorm:"product_line varchar" json:"product_line"`
	ProductType string  `xorm:"product_type varchar" json:"product_type"`
}

func (t *story) FetchStories(start, end, year, month int64) []*StoriesItem {
	var tables []*StoriesItem
	tableName := (&Story{}).TableName(start, end, year, month)
	var err error
	if start == 0 && end == 0 {
		err = database.GetEngine().Table(tableName).Find(&tables)
	} else {
		sql := "select a.*,b.name as iteration_name,b.start as iteration_start,b.end as iteration_end " +
			"from `tapd_story` as a left join `tapd_iteration` as b on a.iteration_id = b.iteration_id " +
			"where `story_id` in (select distinct story_id from `tapd_task` where `begin` >= ? and `begin` <= ?);"
		err = database.GetEngine().SQL(sql, start, end).Find(&tables)
	}
	if err != nil {
		logger.Error(err)
		return nil
	}
	fmt.Println("zjx", tables)
	return tables
}
