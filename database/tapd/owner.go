package tapd

import (
	"gitlab.dailyyoga.com.cn/rdc/serverops/database"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
)

type Owner struct {
	ID          int64   `xorm:"int(11) unsigned autoincr pk 'id'" json:"id,omitempty"`
	Owner       string  `xorm:"'owner' varchar(50)" json:"owner"`
	AddEffort   float64 `xorm:"'add_effort' double" json:"add_effort"`
	Department  int64   `xorm:"'department' int(11)" json:"department"`
	LeaveEffort float64 `xorm:"'leave_effort' double" json:"leave_effort"`
	TimeAt      int64   `xorm:"'time_at' int(11)" json:"time_at"`
}

func (o *Owner) TableName() string {
	return "tapd_effort"
}

type owner struct{}

var TbOwner owner

func (o *owner) List(start, end int64) []*Owner {
	var tables []*Owner
	err := database.GetEngine().Where("time_at >= ?", start).Where("time_at <= ?", end).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
