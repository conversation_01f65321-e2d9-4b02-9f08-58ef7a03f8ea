package tapd

import (
	"gitlab.dailyyoga.com.cn/rdc/serverops/database"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
)

type Sign struct {
	ID       int64   `xorm:"int(11) unsigned autoincr pk 'id'" json:"id,omitempty"`
	Owner    string  `xorm:"'owner' varchar(50)" json:"owner"`
	TimeAt   int64   `xorm:"'time_at' int(11)" json:"time_at"`
	Hour     float64 `xorm:"'hour' double" json:"hour"`
	CreateAt int64   `xorm:"'create_at' int(11)" json:"create_at"`
	UpdateAt int64   `xorm:"'update_at' int(11)" json:"update_at"`
}

func (o *Sign) TableName() string {
	return "tapd_developer_sign"
}

type sign struct{}

var TbSign sign

func (o *sign) List(start, end int64) []*Sign {
	var tables []*Sign
	err := database.GetEngine().Where("time_at >= ?", start).Where("time_at <= ?", end).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
