package tapd

import (
	"fmt"
	"gitlab.dailyyoga.com.cn/rdc/serverops/database"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
)

type Task struct {
	ID          int64   `xorm:"int(11) unsigned autoincr pk 'id'" json:"id"`
	TaskID      string  `xorm:"'task_id' varchar(100)" json:"task_id"`
	Owner       string  `xorm:"'owner' varchar(50)" json:"owner"`
	Effort      float64 `xorm:"'effort' double" json:"effort"`
	ProductLine string  `xorm:"'product_line' varchar(100)" json:"product_line"`
	StoryID     string  `xorm:"'story_id' varchar(100)" json:"story_id"`
	IterationID string  `xorm:"'iteration_id' varchar(100)" json:"iteration_id"`
	ProductType string  `xorm:"'product_type' varchar(50)" json:"product_type"`
	Begin       int64   `xorm:"'begin' int(11)" json:"begin"`
	Due         int64   `xorm:"'due' int(11)" json:"due"`
	CreateTime  int64   `xorm:"'created_at' int(11)" json:"created_at,omitempty"`
	UpdateTime  int64   `xorm:"'update_at' int(11)" json:"update_at,omitempty"`
}

func (t *Task) TableName(start, end, year, month int64) string {
	if start == 0 && end == 0 {
		// 2024 11月新拆分的逻辑
		return fmt.Sprintf("tapd_task_%d_%02d", year, month) // 例如: tapd_task_2024_11
	} else {
		return "tapd_task" // 例如: tapd_task
	}
}

type task struct{}

var TbTask task

func (t *task) Lists(start, end, year, month int64) []*Task {
	var tables []*Task
	tableName := (&Task{}).TableName(start, end, year, month)
	var err error
	if start == 0 && end == 0 {
		err = database.GetEngine().Table(tableName).Find(&tables)
	} else {
		err = database.GetEngine().Table(tableName).Where("begin >= ?", start).Where("begin <= ?", end).Find(&tables)
	}
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
