package tapd

import (
	"gitlab.dailyyoga.com.cn/rdc/serverops/database"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
)

type H2oTask struct {
	ID          int64   `xorm:"int(11) unsigned autoincr pk 'id'" json:"id"`
	TaskID      string  `xorm:"'task_id' varchar(100)" json:"task_id"`
	Owner       string  `xorm:"'owner' varchar(50)" json:"owner"`
	Effort      float64 `xorm:"'effort' double" json:"effort"`
	ProductLine string  `xorm:"'product_line' varchar(100)" json:"product_line"`
	StoryID     string  `xorm:"'story_id' varchar(100)" json:"story_id"`
	IterationID string  `xorm:"'iteration_id' varchar(100)" json:"iteration_id"`
	ProductType string  `xorm:"'product_type' varchar(50)" json:"product_type"`
	Begin       int64   `xorm:"'begin' int(11)" json:"begin"`
	Due         int64   `xorm:"'due' int(11)" json:"due"`
	CreateTime  int64   `xorm:"'created_at' int(11)" json:"created_at,omitempty"`
	UpdateTime  int64   `xorm:"'update_at' int(11)" json:"update_at,omitempty"`
}

func (t *H2oTask) TableName() string {
	return "tapd_h2o_task"
}

type h2oTask struct{}

var TbH2oTask h2oTask

func (t *h2oTask) Lists(start, end int64) []*H2oTask {
	var tables []*H2oTask
	err := database.GetEngine().Where("begin >= ?", start).Where("begin <= ?", end).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
