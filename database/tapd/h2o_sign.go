package tapd

import (
	"gitlab.dailyyoga.com.cn/rdc/serverops/database"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
)

type H2oSign struct {
	ID       int64   `xorm:"int(11) unsigned autoincr pk 'id'" json:"id,omitempty"`
	Owner    string  `xorm:"'owner' varchar(50)" json:"owner"`
	TimeAt   int64   `xorm:"'time_at' int(11)" json:"time_at"`
	Hour     float64 `xorm:"'hour' double" json:"hour"`
	CreateAt int64   `xorm:"'create_at' int(11)" json:"create_at"`
	UpdateAt int64   `xorm:"'update_at' int(11)" json:"update_at"`
}

func (o *H2oSign) TableName() string {
	return "tapd_developer_h2o_sign"
}

type h2oSign struct{}

var TbH2oSign h2oSign

func (o *h2oSign) List(start, end int64) []*H2oSign {
	var tables []*H2oSign
	err := database.GetEngine().Where("time_at >= ?", start).Where("time_at <= ?", end).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
