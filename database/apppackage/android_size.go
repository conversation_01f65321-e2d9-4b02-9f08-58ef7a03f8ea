package apppackage

import (
	"time"

	"gitlab.dailyyoga.com.cn/rdc/serverops/database"
)

type AndroidSize struct {
	ID           int64        `xorm:"'id' autoincr pk int(11)" json:"-"`
	NewID        int64        `xorm:"'new_id' int(11)" json:"-"`
	BusinessLine BusinessLine `xorm:"'business_line' varchar(10)" json:"-"`
	AppName      AppName      `xorm:"'app_name' varchar(50)" json:"-"`
	All          int64        `xorm:"'all' int(11)" json:"all"`
	Assets       int64        `xorm:"'assets' int(11)" json:"assets"`
	Lib          int64        `xorm:"'lib' int(11)" json:"lib"`
	Res          int64        `xorm:"'res' int(11)" json:"res"`
	DexFiles     int64        `xorm:"'dex_files' int(11)" json:"dex_files"`
	Other        int64        `xorm:"'other' int(11)" json:"other"`
	CreateTime   int64        `xorm:"'create_time' int(11)" json:"create_time"`
	UpdateTime   int64        `xorm:"'update_time' int(11)" json:"-"`
}

func (a *AndroidSize) TableName() string {
	return "app_package_size_android"
}

func (a *AndroidSize) Save() error {
	a.CreateTime = time.Now().Unix()
	a.UpdateTime = a.CreateTime

	_, err := database.GetServiceManagerEngine().Insert(a)
	return err
}
