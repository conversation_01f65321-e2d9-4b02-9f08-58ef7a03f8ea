package apppackage

import (
	"gitlab.dailyyoga.com.cn/rdc/serverops/database"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
)

type size struct{}

var TbSize size

type sizeFilter struct {
	OS           OS           `json:"os"`
	AppName      AppName      `json:"app_name"`
	BusinessLine BusinessLine `json:"business_line"`
	Limit        int          `json:"limit"`
}

type SizeFilterOption func(f *sizeFilter)

func SizeFilterWithOS(os OS) SizeFilterOption {
	return func(f *sizeFilter) {
		f.OS = os
	}
}

func SizeFilterWithAppName(appName AppName) SizeFilterOption {
	return func(f *sizeFilter) {
		f.AppName = appName
	}
}

func SizeFilterWithBusinessLine(businessLine BusinessLine) SizeFilterOption {
	return func(f *sizeFilter) {
		f.BusinessLine = businessLine
	}
}

func SizeFilterWithLimit(limit int) SizeFilterOption {
	return func(f *sizeFilter) {
		f.Limit = limit
	}
}

type SizeFilterResponseItem struct {
	Filter      *sizeFilter  `json:"filter"`
	AndroidInfo *AndroidSize `json:"android_info,omitempty"`
	AppleOSInfo *AppleOSSize `json:"ios_info,omitempty"`
	Package     *PackageNew  `json:"package,omitempty"`
}

func (s *size) Find(options ...SizeFilterOption) []*SizeFilterResponseItem {
	filter := &sizeFilter{
		OS: OSAndroid, BusinessLine: BusinessLineH2, AppName: AppNameDailyYoga, Limit: 100,
	}
	for _, option := range options {
		option(filter)
	}

	if filter.OS == "" {
		return nil
	}

	data := make([]*SizeFilterResponseItem, 0)

	if filter.OS == "android" {
		var tables []*AndroidSize
		s.getDatas(filter, &tables)
		for i := range tables {
			data = append(data, &SizeFilterResponseItem{
				Filter:      filter,
				AndroidInfo: tables[i], Package: TbPackageNew.GetItem(tables[i].NewID),
			})
		}
	} else if filter.OS == "ios" {
		var tables []*AppleOSSize
		s.getDatas(filter, &tables)
		for i := range tables {
			data = append(data, &SizeFilterResponseItem{
				Filter:      filter,
				AppleOSInfo: tables[i], Package: TbPackageNew.GetItem(tables[i].NewID),
			})
		}
	}
	return data
}

func (s *size) getDatas(filter *sizeFilter, rowsSlicePtr interface{}) {
	session := database.GetServiceManagerEngine().NewSession()
	if filter.AppName != "" {
		session.Where("`app_name` = ?", filter.AppName)
	}
	if filter.BusinessLine != "" {
		session.Where("`business_line` = ?", filter.BusinessLine)
	}

	if filter.Limit > 0 {
		session.Limit(filter.Limit)
	}

	if err := session.Desc("`id`").Find(rowsSlicePtr); err != nil {
		logger.Error(err)
	}
}
