package apppackage

import (
	"time"

	"github.com/pkg/errors"
	"gitlab.dailyyoga.com.cn/rdc/serverops/database"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
)

type AppPackage struct {
	ID         int64  `xorm:"not null autoincr pk int(11) 'id'" json:"-"`
	Name       string `xorm:"not null varchar(100) 'name'" json:"name"`
	Size       int64  `xorm:"not null int(11) 'size'" json:"size"`
	Platform   string `xorm:"not null varchar(10) 'platform'" json:"platform"`
	Env        string `xorm:"not null varchar(20) 'env'" json:"env"`
	Version    string `xorm:"not null varchar(50) 'version'" json:"version"`
	ModTime    int64  `xorm:"not null int(11) 'mod_time'" json:"mod_time"`
	OS         string `xorm:"not null varchar(10) 'os'" json:"os"`
	Ext        string `xorm:"not null varchar(100) 'ext'" json:"ext"`
	Key        string `xorm:"not null varchar(100) 'key'" json:"key"`
	Hash       string `xorm:"not null varchar(100) 'hash'" json:"hash"`
	Channel    string `xorm:"not null varchar(100) 'channel'" json:"channel"`
	CreateTime int64  `xorm:"not null int(11) 'create_time'" json:"create_time"`
	UpdateTime int64  `xorm:"not null int(11) 'update_time'" json:"update_time"`
	AppName    string `xorm:"not null varchar(100) 'app_name'" json:"app_name"`
	ExtraInfo  string `xorm:"not null varchar(500) 'extra_info'" json:"extra_info"`
}

func (a *AppPackage) TableName() string {
	return "app_package"
}

func (a *AppPackage) Save() error {
	if a.Name == "" || a.Size < 0 || a.Hash == "" || a.Key == "" {
		return errors.New("invalid params")
	}

	a.CreateTime = time.Now().Unix()
	a.UpdateTime = a.CreateTime

	_, err := database.GetServiceManagerEngine().Insert(a)
	return err
}

func (a *AppPackage) Delete() error {
	if a.ID < 1 {
		return errors.New("record not found")
	}
	_, err := database.GetServiceManagerEngine().Delete(a)
	return err
}

type appPackage struct{}

var TbAppPackage appPackage

func (a *appPackage) CheckPackageExist(hash string) (bool, error) {
	var table AppPackage

	ok, err := database.GetServiceManagerEngine().Where("hash = ?", hash).Get(&table)
	if err != nil || !ok {
		return false, err
	}
	return table.ID > 0, nil
}

func (a *appPackage) GetItem(id int64) *AppPackage {
	var table AppPackage
	ok, err := database.GetServiceManagerEngine().ID(id).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

type SearchRequest struct {
	OS       string
	Platform string
	ENV      string
	Version  string
	Channel  string
	AppName  string
	Limit    int
}

func (a *appPackage) FetchPackages(s *SearchRequest) []AppPackage {
	var tables []AppPackage

	sess := database.GetServiceManagerEngine().NewSession()
	if s.OS == "Android" || s.OS == "iOS" {
		sess.Where("os = ?", s.OS)
	}
	if s.Platform == "h2" || s.Platform == "h2o" {
		sess.Where("platform = ?", s.Platform)
	}
	if s.ENV == "dev" || s.ENV == "release" {
		sess.Where("env = ?", s.ENV)
	}
	if s.Version != "" {
		sess.Where("version = ?", s.Version)
	}
	if s.Channel != "" {
		sess.Where("channel = ?", s.Channel)
	}
	if s.AppName != "" {
		sess.Where("app_name = ?", s.AppName)
	}

	if s.Limit == 0 {
		s.Limit = 50
	}

	err := sess.Desc("id").Limit(s.Limit).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
