package apppackage

import (
	"github.com/pkg/errors"
	"gitlab.dailyyoga.com.cn/rdc/serverops/database"
)

type BusinessLine string

const BusinessLineH2 BusinessLine = "h2"
const BusinessLineH2O BusinessLine = "h2o"

var SupportBusinessLine = []BusinessLine{
	BusinessLineH2, BusinessLineH2O,
}

type Env string

const EnvDebug Env = "dev"
const EnvRelease Env = "release"
const EnvReleaseMarket Env = "release_market"

var SupportEnv = []Env{
	EnvDebug, EnvRelease, EnvReleaseMarket,
}

type OS string

func (o OS) String() string {
	return string(o)
}

const OSAndroid OS = "android" // 安卓
const OSAppleiOS OS = "ios"    // 苹果iOS
const OSWechat OS = "wechat"   // 微信小程序
const OSDouYin OS = "douyin"   // 抖音小程序
const OSAlipay OS = "alipay"   // 支付宝小程序

var SupportOS = []OS{
	OSAndroid, OSAppleiOS, OSWechat, OSDouYin,
}

var MiniAppOS = []OS{
	OSWechat, OSDouYin, OSAlipay,
}

var OSName = map[OS]string{
	OSAndroid: "Android",
	OSAppleiOS: "iOS",
	OSWechat: "微信小程序",
	OSDouYin: "抖音小程序",
	OSAlipay: "支付宝小程序",
}

type AppName string

func (a AppName) String() string {
	return string(a)
}

const AppNameDailyYoga AppName = "dailyyoga"
const AppNameDanceFit AppName = "dancefit"
const AppNameFitness AppName = "fitness"
const AppNameStretch AppName = "stretch"
const AppNameYogaTV AppName = "yoga_tv"
const AppNameYogaLite AppName = "yoga_lite"
const AppNameChildren AppName = "children"

const AppNameDanceFitMBA15 AppName = "dancefit_mba_15"
const AppNameDanceFitMBA21 AppName = "dancefit_mba_21"

var SupportAppName = []AppName{
	AppNameDailyYoga, AppNameDanceFit, AppNameFitness, AppNameStretch,
	AppNameYogaTV, AppNameYogaLite, AppNameChildren,
	AppNameDanceFitMBA15, AppNameDanceFitMBA21, // 小程序
}

type AppInfo struct {
	Name        string
	Icon        string
}

var AppInfos = map[AppName]AppInfo{
	AppNameDailyYoga: {Name: "每日瑜伽", Icon: "https://qnapppackage.dailyyoga.com.cn/icon/icon_dailyyoga.ico"},
	AppNameDanceFit:  {Name: "热汗舞蹈", Icon: "https://qnapppackage.dailyyoga.com.cn/icon/icon_dancefit.ico"},
	AppNameFitness:   {Name: "硬汗健身", Icon: "https://qnapppackage.dailyyoga.com.cn/icon/icon_fitness.ico"},
	AppNameStretch:   {Name: "元气拉伸", Icon: "https://qnapppackage.dailyyoga.com.cn/icon/icon_stretch.ico"},
	AppNameYogaTV:    {Name: "每日瑜伽 TV 版", Icon: "https://qnapppackage.dailyyoga.com.cn/icon/icon_dailyyoga.ico"},
	AppNameYogaLite:  {Name: "每日瑜伽极速版", Icon: "https://qnapppackage.dailyyoga.com.cn/icon/icon_dailyyoga.ico"},
	AppNameDanceFitMBA15: {Name: "热汗舞蹈 15天挑战赛", Icon: "https://qnapppackage.dailyyoga.com.cn/icon/icon_dancefit.ico"},
	AppNameDanceFitMBA21: {Name: "热汗舞蹈 21天挑战赛", Icon: "https://qnapppackage.dailyyoga.com.cn/icon/icon_dancefit.ico"},
	AppNameChildren: {Name: "小树苗运动", Icon: "https://apppackage.dailyyoga.com.cn/children_h2.png"},
}

type PackageNew struct {
	ID            int64        `xorm:"'id' autoincr pk int(11)" json:"id"`
	BusinessLine  BusinessLine `xorm:"'business_line' varchar(10)" json:"business_line"`
	Env           Env          `xorm:"'env' varchar(20)" json:"env"`
	AppName       AppName      `xorm:"'app_name' varchar(50)" json:"app_name"`
	AppVersion    string       `xorm:"'app_version' varchar(50)" json:"app_version"`
	MarketVersion string       `xorm:"'market_version' varchar(50)" json:"market_version"`
	OS            OS           `xorm:"'os' varchar(20)" json:"os"`
	ChannelName   string       `xorm:"'channel_name' varchar(50)" json:"channel_name"`
	ExtendParams  string       `xorm:"'extend_params' varchar(1024)" json:"extend_params"`
	Filename      string       `xorm:"'filename' varchar(100)" json:"filename"`
	Size          int64        `xorm:"'size' int(11)" json:"size"`
	ModTime       int64        `xorm:"'mod_time' int(11)" json:"mod_time"`
	Key           string       `xorm:"'key' varchar(200)" json:"key"`
	Hash          string       `xorm:"'hash' varchar(100)" json:"hash"`
	CreateTime    int64        `xorm:"'create_time' int(11)" json:"create_time"`
	UpdateTime    int64        `xorm:"'update_time' int(11)" json:"-"`
}

func (p *PackageNew) TableName() string {
	return "app_package_new"
}

func (p *PackageNew) ValidationMiniApp() error {
	notEmptyParams := map[string]interface{}{
		"business_line": p.BusinessLine,
		"env":           p.Env,
		"app_name":      p.AppName,
		"os":            p.OS,
		"app_version":   p.AppVersion,
		"market_version": p.MarketVersion,
		"extend_params":   p.ExtendParams,
	}
	for k, v := range notEmptyParams {
		if v == "" {
			return errors.Errorf("invalid param: %s", k)
		}
	}
	return nil
}

func (p *PackageNew) Validation() error {
	notEmptyParams := map[string]interface{}{
		"channel_name":  p.ChannelName,
		"business_line": p.BusinessLine,
		"env":           p.Env,
		"os":            p.OS,
		"app_name":      p.AppName,
		"app_version":   p.AppVersion,
		"filename":      p.Filename,
		"key":           p.Key,
		"hash":          p.Hash,
	}
	for k, v := range notEmptyParams {
		if v == "" {
			return errors.Errorf("invalid param: %s", k)
		}
	}

	return nil
}

type packageNew struct{}

var TbPackageNew packageNew

func (p *packageNew) BatchInsert(rows []*PackageNew) error {
	_, err := database.GetServiceManagerEngine().Insert(rows)
	return err
}

func (p *packageNew) GetItem(id int64) *PackageNew {
	var table PackageNew

	ok, err := database.GetServiceManagerEngine().ID(id).Get(&table)
	if err != nil || !ok {
		return nil
	}
	return &table
}

func (p *packageNew) GetItemByHash(hash string) *PackageNew {
	var table PackageNew

	ok, err := database.GetServiceManagerEngine().Where("hash = ?", hash).Get(&table)
	if err != nil || !ok {
		return nil
	}
	return &table
}

func (p *packageNew) GetItemsByHashes(hashes []string) []*PackageNew {
	var table []*PackageNew
	err := database.GetServiceManagerEngine().In("hash", hashes).Find(&table)
	if err != nil {
		return nil
	}
	return table
}

func (p *packageNew) Exist(hash string) (bool, error) {
	var table PackageNew

	ok, err := database.GetServiceManagerEngine().Where("hash = ?", hash).Get(&table)
	if err != nil || !ok {
		return false, err
	}
	return table.ID > 0, nil
}

func (p *packageNew) FetchWithIDs(ids []int64) []*PackageNew {
	var tables []*PackageNew
	if err := database.GetServiceManagerEngine().In("id", ids).Find(&tables); err != nil {
		return nil
	}
	return tables
}
