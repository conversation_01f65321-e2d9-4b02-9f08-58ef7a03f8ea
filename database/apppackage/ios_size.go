package apppackage

import (
	"time"

	"gitlab.dailyyoga.com.cn/rdc/serverops/database"
)

type AppleOSSize struct {
	ID            int64        `xorm:"'id' autoincr pk int(11)" json:"-"`
	NewID         int64        `xorm:"'new_id' int(11)" json:"-"`
	BusinessLine  BusinessLine `xorm:"'business_line' varchar(10)" json:"-"`
	AppName       AppName      `xorm:"'app_name' varchar(50)" json:"-"`
	All           int64        `xorm:"'all' int(11)" json:"all"`
	Assets        int64        `xorm:"'assets' int(11)" json:"assets"`
	FlutterAssets int64        `xorm:"'flutter_assets' int(11)" json:"flutter_assets"`
	Resource      int64        `xorm:"'resource' int(11)" json:"resource"`
	Frameworks    int64        `xorm:"'frameworks' int(11)" json:"frameworks"`
	Binary        int64        `xorm:"'binary' int(11)" json:"binary"`
	Other         int64        `xorm:"'other' int(11)" json:"other"`
	CreateTime    int64        `xorm:"'create_time' int(11)" json:"create_time"`
	UpdateTime    int64        `xorm:"'update_time' int(11)" json:"-"`
}

func (a *AppleOSSize) TableName() string {
	return "app_package_size_ios"
}

func (a *AppleOSSize) Save() error {
	a.CreateTime = time.Now().Unix()
	a.UpdateTime = a.CreateTime

	_, err := database.GetServiceManagerEngine().Insert(a)
	return err
}
