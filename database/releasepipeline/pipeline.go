package releasepipeline

import (
	"time"

	"gitlab.dailyyoga.com.cn/rdc/serverops/database"
	"gitlab.dailyyoga.com.cn/rdc/serverops/database/apppackage"
)

type Status int

const StatusCancelled Status = 2
const StatusCompleted Status = 6
const StatusTerminate Status = 3
const StatusWaiting Status = 4
const StatusRunning Status = 5

type Pipeline struct {
	ID            int64                   `xorm:"int(11) unsigned pk autoincr 'id'" json:"id"`
	BusinessLine  apppackage.BusinessLine `xorm:"'business_line' varchar(20)" json:"business_line"`
	OS            apppackage.OS           `xorm:"'os' varchar(20)" json:"os"`
	AppName       apppackage.AppName      `xorm:"'app_name' varchar(50)" json:"app_name"`
	AppVersion    string                  `xorm:"'app_version' varchar(50)" json:"app_version"`
	BuildVersion  string                  `xorm:"'build_version' varchar(50)" json:"build_version"`
	MarketVersion string                  `xorm:"'market_version' varchar(50)" json:"market_version"`
	Status        Status                  `xorm:"tinyint(1) 'status'" json:"status"`
	CC            string                  `xorm:"varchar(255) 'cc'" json:"cc"`
	ExtraParams   string                  `xorm:"text 'extra_params'" json:"extra_params"`
	CreatedAt     int64                   `xorm:"int(11) 'created_at'" json:"created_at"`
	UpdatedAt     int64                   `xorm:"int(11) 'updated_at'" json:"updated_at"`
}

func (p *Pipeline) TableName() string {
	return "pipeline"
}

func (p *Pipeline) Save() error {
	p.CreatedAt = time.Now().Unix()
	p.UpdatedAt = p.CreatedAt

	_, err := database.GetEngine().Insert(p)
	return err
}

func (p *Pipeline) Update() error {
	p.UpdatedAt = time.Now().Unix()

	_, err := database.GetEngine().ID(p.ID).Update(p)
	return err
}

type pipeline struct{}

var TbPipeline pipeline

func (p *pipeline) GetItem(id int64) *Pipeline {
	var table Pipeline

	ok, err := database.GetEngine().ID(id).Get(&table)
	if err != nil || !ok {
		return nil
	}
	return &table
}

type SearchRequest struct {
	BusinessLine apppackage.BusinessLine `json:"business_line"`
	OS           apppackage.OS           `json:"os"`
	AppName      apppackage.AppName      `json:"app_name"`
	Limit        int                     `json:"limit"`
}

func (p *pipeline) Search(s *SearchRequest) []*Pipeline {
	var pipelines []*Pipeline
	if s == nil {
		s = &SearchRequest{Limit: 20}
	}
	if s.Limit == 0 {
		s.Limit = 20
	}

	sess := database.GetEngine().NewSession()
	if s.BusinessLine != "" {
		sess.Where("`business_line` = ?", s.BusinessLine)
	}
	if s.OS != "" {
		sess.Where("`os` = ?", s.OS)
	}
	if s.AppName != "" {
		sess.Where("`app_name` = ?", s.AppName)
	}
	err := sess.OrderBy(`id desc`).Limit(s.Limit).Find(&pipelines)
	if err != nil {
		return nil
	}
	return pipelines
}
