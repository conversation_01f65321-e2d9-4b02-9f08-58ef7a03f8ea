package releasepipeline

import (
	"time"

	"gitlab.dailyyoga.com.cn/rdc/serverops/database"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
)

type Stage struct {
	ID                  int64  `xorm:"pk autoincr 'id' int(11)" json:"id" form:"id"`
	PipelineID          int64  `xorm:"'pipeline_id' int(11)" json:"pipeline_id"`
	Index               int    `xorm:"'index' tinyint(1)" json:"index"`
	Key                 string `xorm:"'key' varchar(100)" json:"key"`
	Name                string `xorm:"'name' varchar(100)" json:"name"`
	Description         string `xorm:"'description' varchar(255)" json:"description"`
	Status              Status `xorm:"'status' tinyint(1)" json:"status"`
	AllowTerminate      bool   `xorm:"'allow_terminate' tinyint(1)" json:"allow_terminate"`
	NeedEvidence        bool   `xorm:"'need_evidence' tinyint(1)" json:"need_evidence"`
	EvidenceDescription string `xorm:"'evidence_description' varchar(255)" json:"evidence_description"`
	EvidenceMaterials   string `xorm:"'evidence_materials' text" json:"evidence_materials"`
	ConfirmUser         string `xorm:"'confirm_user' varchar(100)" json:"confirm_user"`
	RealConfirmUser     string `xorm:"'real_confirm_user' varchar(100)" json:"real_confirm_user"`
	ConfirmedAt         int64  `xorm:"'confirmed_at' int(11)" json:"confirmed_at"`
	Remark              string `xorm:"'remark' varchar(1024)" json:"remark"`
	AfterConfirm        string `xorm:"'after_confirm' text" json:"-"`
	CreatedAt           int64  `xorm:"'created_at' int(11)" json:"created_at"`
	UpdatedAt           int64  `xorm:"'updated_at' int(11)" json:"updated_at"`
}

func (s *Stage) TableName() string {
	return "pipeline_stages"
}

func (s *Stage) Save() error {
	s.CreatedAt = time.Now().Unix()
	s.UpdatedAt = s.CreatedAt

	_, err := database.GetEngine().Insert(s)
	return err
}

func (s *Stage) Update() error {
	s.UpdatedAt = time.Now().Unix()
	_, err := database.GetEngine().Where("`id` = ?", s.ID).Update(s)
	return err
}

type stage struct{}

var TbStage stage

func (s *stage) BatchInsert(rows []*Stage) error {
	data := make([]*Stage, 0, len(rows))
	for _, row := range rows {
		row.CreatedAt = time.Now().Unix()
		row.UpdatedAt = row.CreatedAt
		data = append(data, row)
	}
	_, err := database.GetEngine().Insert(data)
	return err
}

func (s *stage) GetItem(id int64) *Stage {
	var table Stage
	ok, err := database.GetEngine().Where("`id` = ? ", id).Get(&table)
	if err != nil || !ok {
		return nil
	}
	return &table
}

func (s *stage) GetNext(pipelineID int64, index int) *Stage {
	var table Stage
	ok, err := database.GetEngine().Where("`pipeline_id` = ? AND `index` = ?", pipelineID, index+1).Get(&table)
	if err != nil || !ok {
		return nil
	}
	return &table
}

func (s *stage) FetchWithPipeline(pipelineID int64) []*Stage {
	var stages []*Stage

	err := database.GetEngine().Where("`pipeline_id` = ?", pipelineID).Asc("`index`").Find(&stages)
	if err != nil {
		return nil
	}
	return stages
}

func (s *stage) FetchWithMultiPipeline(pipelineIDs []int64) map[int64][]*Stage {
	var stages []*Stage

	err := database.GetEngine().In("`pipeline_id`", pipelineIDs).
		Desc("`pipeline_id`").Asc("`index`").Find(&stages)
	if err != nil {
		logger.Errorf("fetch stages err: %v", err)
		return nil
	}

	ret := make(map[int64][]*Stage)
	for _, stageItem := range stages {
		if _, ok := ret[stageItem.PipelineID]; !ok {
			ret[stageItem.PipelineID] = make([]*Stage, 0)
		}
		ret[stageItem.PipelineID] = append(ret[stageItem.PipelineID], stageItem)
	}
	return ret
}
