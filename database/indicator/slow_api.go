package indicator

import (
	"errors"
	"time"

	"gitlab.dailyyoga.com.cn/rdc/serverops/database"
	"gitlab.dailyyoga.com.cn/rdc/serverops/library"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
)

type ApiStatRecord struct { //nolint
	ID         int64                         `xorm:"not null autoincr pk int(11) 'id'"`
	Name       string                        `xorm:"varchar(128) 'interface'"`
	Count      int64                         `xorm:"int(11) 'count'"`
	LPCount    int64                         `xorm:"int(11) 'lp_count'"`
	Date       int                           `xorm:"int(11) 'date'"`
	RecordType library.SlowApiStatRecordType `xorm:"int(11) 'type'"`
	Platform   string                        `xorm:"varchar(50) 'platform'"`
	Service    string                        `xorm:"varchar(100) 'service'"`
	SysFrom    string                        `xorm:"varchar(20) 'sys_from'"`
	CreateTime int64                         `xorm:"int(11) 'create_time'"`
	UpdateTime int64                         `xomr:"int(11) 'update_time"`
}

func (ApiStatRecord) TableName() string {
	return "interface_statistic"
}

func (a *ApiStatRecord) Save() error {
	a.CreateTime = time.Now().Unix()
	a.UpdateTime = a.CreateTime
	if a.RecordType == library.SlowApiStatUnknown {
		a.RecordType = library.SlowApiStatTotal
	}
	_, err := database.GetServiceManagerEngine().Insert(a)
	return err
}

func (a *ApiStatRecord) Update() error {
	a.UpdateTime = time.Now().Unix()
	_, err := database.GetServiceManagerEngine().ID(a.ID).Update(a)
	return err
}

type apiStatRecord struct{}

var TbApiStatRecord apiStatRecord // nolint

func (a *apiStatRecord) GetUniqueRecord(date int, api string,
	recordType library.SlowApiStatRecordType, platform string) (*ApiStatRecord, error) {
	var table ApiStatRecord

	ok, err := database.GetServiceManagerEngine().
		Where("`interface` = ? and `date` = ? and `type` = ? and `platform` = ?",
			api, date, recordType, platform).
		Limit(1).Get(&table)
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, errors.New("item not found")
	}
	return &table, nil
}

type ApiStatSearchPayload struct { // nolint
	Name      string     `json:"name"`
	Platform  string     `json:"platform"`
	StartDate int        `json:"start_date"`
	EndDate   int        `json:"end_date"`
	SysFrom   string     `json:"sys_from"`
	Service   string     `json:"service"`
	Project   APIProject `json:"project"`
}

type APIProject string

const ProjectYoga APIProject = "yoga"
const ProjectDanceFit APIProject = "dancefit"
const ProjectFitness APIProject = "fitness"
const ProjectStretch APIProject = "stretch"
const ProjectChildren APIProject = "children"
const ProjectH2Other APIProject = "h2other"

func (a *apiStatRecord) getProjectService(platform string, projects ...APIProject) []string {
	services := make([]string, 0)

	projectServices := map[APIProject][]string{
		ProjectDanceFit: {"dancefit", "dancefit-admin", "dancefit-webpage"},
		ProjectFitness:  {"fitness-admin-api", "fitness"},
		ProjectStretch:  {"stretch", "stretch-admin-api", "stretch-website"},
		ProjectChildren: {"children-admin-api", "children"},
		ProjectH2Other:  {"yoga-cs", "common-webpage", "dailyyoga-official-webpage", "resource-services"},
	}

	if platform == "h2o" {
		projectServices = map[APIProject][]string{
			ProjectDanceFit: {"dancefit-api"},
			ProjectFitness:  {"fitness-admin", "fitness-api"},
			ProjectStretch:  {"stretch-admin", "stretch-api"},
		}
	}

	for _, project := range projects {
		if data, ok := projectServices[project]; ok {
			services = append(services, data...)
		}
	}
	return services
}

func (a *apiStatRecord) Search(s *ApiStatSearchPayload) []ApiStatRecord {
	var tables []ApiStatRecord

	platform := "h2"

	session := database.GetServiceManagerEngine().NewSession()
	if s.Name != "" {
		session = session.Where("interface = ?", s.Name)
	}
	if s.Platform != "" {
		session = session.Where("platform = ?", s.Platform)
		platform = s.Platform
	}
	if s.SysFrom != "" {
		session = session.Where("sys_from = ?", s.SysFrom)
	}
	if s.Service != "" {
		session = session.Where("service = ?", s.Service)
	}
	if s.StartDate != 0 {
		session = session.Where("date >= ?", s.StartDate)
	}
	if s.EndDate != 0 {
		session = session.Where("date <= ?", s.EndDate)
	}

	if s.Project == ProjectYoga {
		session = session.NotIn("service", a.getProjectService(platform,
			ProjectFitness, ProjectDanceFit, ProjectStretch, ProjectChildren, ProjectH2Other))
	} else {
		session = session.In("service", a.getProjectService(platform, s.Project))
	}

	if err := session.Find(&tables); err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (a *apiStatRecord) GetDateInfo(date int,
	recordType library.SlowApiStatRecordType, apiName, platform string) *ApiStatRecord {
	var table ApiStatRecord

	ok, err := database.GetServiceManagerEngine().Where("interface = ?", apiName).
		Where("type = ?", recordType).Where("platform = ?", platform).Where("date = ?", date).Get(&table)
	if !ok {
		return nil
	}
	if err != nil {
		logger.Error(err)
		return nil
	}
	return &table
}

type statSummary struct {
	Date       int                           `xorm:"date"`
	RecordType library.SlowApiStatRecordType `xorm:"record_type"`
	Service    string                        `xorm:"service"`
	Count      int64                         `xorm:"count"`
}

func (a *apiStatRecord) Summary(date int, platform string) []statSummary {
	var datas []statSummary

	if err := database.GetServiceManagerEngine().
		Select("`date`, `type` as `record_type`, `service`, sum(`count`) as `count`").
		Where("`date` = ? and `platform` = ?", date, platform).
		GroupBy("`date`, `service`, `record_type`").Table(&ApiStatRecord{}).Find(&datas); err != nil {
		logger.Error(err)
		return nil
	}
	return datas
}
