package indicator

import (
	"gitlab.dailyyoga.com.cn/rdc/serverops/database"
	"gitlab.dailyyoga.com.cn/rdc/serverops/library"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
)

type APIStatSummary struct {
	ID         int64                         `xorm:"int(11) 'id' autoincr pk" json:"id"`
	Date       int                           `xorm:"int(11) 'date'" json:"date"`
	RecordType library.SlowApiStatRecordType `xorm:"tinyint(4) 'record_type'" json:"record_type"`
	Count      int64                         `xorm:"int(11) 'count'" json:"count"`
	Platform   string                        `xorm:"varchar(50) 'platform'" json:"platform"`
	Service    string                        `xorm:"varchar(50) 'service'" json:"service"`
}

func (APIStatSummary) TableName() string {
	return "interface_statistic_summary"
}

func (a *APIStatSummary) Save() error {
	_, err := database.GetServiceManagerEngine().Insert(a)
	return err
}

type apiStatSummary struct{}

var TbAPIStatSummary apiStatSummary

func (a *apiStatSummary) Row(date int,
	platform, service string, recordType library.SlowApiStatRecordType) *APIStatSummary {
	var table APIStatSummary

	ok, err := database.GetServiceManagerEngine().
		Where("platform = ?", platform).
		Where("service = ?", service).
		Where("record_type = ?", recordType).
		Where("date = ?", date).Get(&table)
	if !ok {
		return nil
	}
	if err != nil {
		logger.Error(err)
		return nil
	}
	return &table
}

var SummaryProject = map[APIProject]string{
	ProjectYoga:     "dailyyoga",
	ProjectDanceFit: "dancefit",
	ProjectFitness:  "fitness",
	ProjectStretch:  "stretch",
	ProjectChildren: "children",
}

func (a *apiStatSummary) Summary(s *ApiStatSearchPayload) []APIStatSummary {
	var tables []APIStatSummary

	sess := database.GetServiceManagerEngine().
		Where("platform = ?", s.Platform).
		Where("date >= ?", s.StartDate).
		Where("date <= ?", s.EndDate)

	if projectName, ok := SummaryProject[s.Project]; ok {
		sess = sess.Where("service = ?", projectName)
	} else {
		logger.Error("unsupported summary project")
		return nil
	}
	if err := sess.Find(&tables); err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
