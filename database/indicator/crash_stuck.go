package indicator

import (
	"github.com/pkg/errors"
	"gitlab.dailyyoga.com.cn/rdc/serverops/database"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
)

type CrashStuck struct {
	TableType         CrashStuckTableType `xorm:"-" json:"table_type"`
	ID                int64               `xorm:"bigint(20) unsigned not null pk autoincr 'id'" json:"id"`
	Platform          int                 `xorm:"tinyint(1) 'platform'" json:"platform"`
	Day               string              `xorm:"varchar(10) 'day'" json:"day"`
	ClientType        int                 `xorm:"tinyint(1) 'client_type'" json:"client_type"`
	LatestVersion     string              `xorm:"varchar(20) 'latest_version'" json:"latest_version"`
	OnlineVersionUser int                 `xorm:"int(11) 'online_version_user'" json:"online_version_user"`
	EffectUser        int                 `xorm:"int(11) 'effect_user'" json:"effect_user"`
	TotalUser         int                 `xorm:"int(11) 'total_user'" json:"total_user"`
	EventRemark       string              `xorm:"varchar(255) 'event_remark'" json:"event_remark"`

	OnlineVersionEffectUser int    `xorm:"int(11) 'online_version_effect_user'" json:"online_version_effect_user"`
	AppName                 string `xorm:"varchar(50) 'app_name'" json:"app_name"`
}

type CrashStuckTableType string

const TableUnknown CrashStuckTableType = "unknown"
const TableCrash CrashStuckTableType = "crash"
const TableStuck CrashStuckTableType = "slow_render"

func (c *CrashStuck) TableName() string {
	if c.TableType == TableUnknown {
		return ""
	}
	return string(c.TableType)
}

func (c *CrashStuck) Save() error {
	if c.TableType == TableUnknown {
		return errors.New("table unknown")
	}
	_, err := database.GetServiceManagerEngine().Insert(c)
	return err
}

func (c *CrashStuck) Delete() error {
	if c.TableType == TableUnknown {
		return errors.New("table unknown")
	}
	if c.ID == 0 {
		return errors.New("id undefined")
	}
	_, err := database.GetServiceManagerEngine().ID(c.ID).Delete(c)
	return err
}

type crashStuck struct{}

var TbCrashStuck crashStuck

type CrashStuckSearchPayload struct {
	TableType  CrashStuckTableType `json:"table_type"`
	Platform   int                 `json:"platform"`
	ClientType int                 `json:"client_type"`
	StartDate  string              `json:"start_date"`
	EndDate    string              `json:"end_date"`
	AppName    string              `json:"app_name"`
}

func (c *crashStuck) Search(payload *CrashStuckSearchPayload) []CrashStuck {
	var tables []CrashStuck

	if payload.TableType == "" || payload.TableType == TableUnknown {
		return nil
	}
	table := &CrashStuck{
		TableType: payload.TableType,
	}

	session := database.GetServiceManagerEngine().NewSession().Table(table.TableName())
	if payload.Platform > 0 {
		session = session.Where("platform = ?", payload.Platform)
	}
	if payload.ClientType > 0 {
		session = session.Where("client_type = ?", payload.ClientType)
	}
	if payload.StartDate != "" {
		session = session.Where("day >= ?", payload.StartDate)
	}
	if payload.EndDate != "" {
		session = session.Where("day <= ?", payload.EndDate)
	}
	if payload.AppName != "" {
		session = session.Where("app_name = ?", payload.AppName)
	}
	if err := session.Find(&tables); err != nil {
		logger.Error(err)
		return nil
	}

	for index := range tables {
		tables[index].TableType = table.TableType
	}
	return tables
}
