package indicator

import (
	"github.com/pkg/errors"

	"time"

	"gitlab.dailyyoga.com.cn/rdc/serverops/database"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
)

type DialtestTask struct {
	ID         int64  `xorm:"int(11) autoincr pk 'id'" json:"id"`
	URL        string `xorm:"not null varchar(128) 'url'" json:"url"`
	AppName    string `xorm:"not null varchar(50) 'app_name'" json:"app_name"`
	Name       string `xorm:"not null varchar(50) 'name'" json:"name"`
	Method     string `xorm:"not null varchar(50) 'method'" json:"method"`
	Headers    string `xorm:"not null varchar(128) 'headers'" json:"headers"`
	Body       string `xorm:"not null varchar(128) 'body'" json:"body"`
	StatusCode int64  `xorm:"int(11) 'status_code'" json:"status_code"`
	CreateAt   int64  `xorm:"int(11) 'created_at'" json:"-"`
	UpdateAt   int64  `xorm:"int(11) 'updated_at'" json:"-"`
}

func (d *DialtestTask) TableName() string {
	return "dialtest_task"
}

func (d *DialtestTask) Save() error {
	d.CreateAt = time.Now().Unix()
	d.UpdateAt = d.CreateAt

	_, err := database.GetEngine().Insert(d)
	return err
}

func (d *DialtestTask) Delete() error {
	if d.ID == 0 {
		return errors.New("record not found")
	}
	_, err := database.GetEngine().Delete(d)
	return err
}

func (d *DialtestTask) Update() error {
	_, err := database.GetEngine().ID(d.ID).MustCols("status_code").Update(d)
	return err
}

func (d *DialtestTask) build() *DialtestTask {
	return d
}

type dialtest struct{}

var TbDialtest dialtest

func makeListSearchOpts(opts []ListOption) *searchOpt {
	opt := &searchOpt{}
	for _, fn := range opts {
		fn(opt)
	}
	return opt
}

func (tb *dialtest) Lists(opts ...ListOption) []*DialtestTask {
	var tables []*DialtestTask
	opt := makeListSearchOpts(opts)
	session := database.GetEngine().NewSession()

	if opt.appName != "" {
		session.Where("app_name = ?", opt.appName)
	}

	if err := session.Desc("id").Find(&tables); err != nil {
		logger.Error(err)
		return nil
	}

	rets := make([]*DialtestTask, 0)
	for i := range tables {
		rets = append(rets, tables[i].build())
	}
	return rets
}

type searchOpt struct {
	appName string `search_field:"app_name"`
}

type ListOption func(*searchOpt)

func ListWithAppName(appName string) ListOption {
	return func(opt *searchOpt) {
		opt.appName = appName
	}
}
