package indicator

import (
	"time"

	"gitlab.dailyyoga.com.cn/rdc/serverops/database"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
)

type PageLoadTask struct {
	ID         int64  `xorm:"not null autoincr pk int(11) 'id'" json:"-"`
	AppID      int64  `xorm:"not null int(11) 'app_id'" json:"app_id"`
	PID        string `xorm:"not null varchar(50) 'pid'" json:"pid"`
	AppName    string `xorm:"not null varchar(50) 'app_name'" json:"app_name"`
	UpdateTime int64  `xorm:"not null int(11) 'update_time'" json:"-"`
	CreateTime int64  `xorm:"not null int(11) 'create_time'" json:"-"`
}

func (PageLoadTask) TableName() string {
	return "f2e_task"
}

func (t *PageLoadTask) Save() error {
	t.CreateTime = time.Now().Unix()
	t.UpdateTime = t.CreateTime

	_, err := database.GetServiceManagerEngine().Insert(t)
	return err
}

type pageLoadTask struct{}

var TbPageLoadTask pageLoadTask

func (t *pageLoadTask) GetAll() map[string]PageLoadTask {
	var tables []PageLoadTask

	if err := database.GetServiceManagerEngine().Find(&tables); err != nil {
		logger.Error(err)
		return nil
	}
	ret := make(map[string]PageLoadTask)
	if len(tables) < 1 {
		return ret
	}
	for _, v := range tables {
		if _, ok := ret[v.PID]; ok {
			continue
		}
		ret[v.PID] = v
	}
	return ret
}

var MetricType = struct {
	AvgFpt   string
	AvgReady string
	AvgLoad  string
	AvgDNS   string
	AvgTCP   string
	AvgSSL   string
	AvgTTFB  string
	AvgTrans string
	AvgDom   string
	AvgRes   string
	AvgTTI   string
}{
	AvgFpt:   "avg_fpt",
	AvgReady: "avg_ready",
	AvgLoad:  "avg_load",
	AvgDNS:   "avg_dns",
	AvgTCP:   "avg_tcp",
	AvgSSL:   "avg_ssl",
	AvgTTFB:  "avg_ttfb",
	AvgTrans: "avg_trans",
	AvgDom:   "avg_dom",
	AvgRes:   "avg_res",
	AvgTTI:   "avg_tti",
}

type PageLoadMetrics struct {
	ID          int64  `xorm:"not null autoincr pk int(11) 'id'" json:"-"`
	Date        int64  `xorm:"not null int(11) 'date'" json:"date"`
	PID         string `xorm:"not null varchar(50) 'pid'" json:"pid"`
	AppName     string `xorm:"-" json:"app_name"`
	MetricType  string `xorm:"not null varchar(50) 'metric_type'" json:"metric_type"`
	MetricValue string `xorm:"not null varchar(255) 'metric_value'" json:"metric_value"`
	UpdateTime  int64  `xorm:"not null int(11) 'update_time'" json:"-"`
	CreateTime  int64  `xorm:"not null int(11) 'create_time'" json:"-"`
}

func (PageLoadMetrics) TableName() string {
	return "f2e_task_metrics"
}

func (t *PageLoadMetrics) Save() error {
	t.CreateTime = time.Now().Unix()
	t.UpdateTime = t.CreateTime

	_, err := database.GetServiceManagerEngine().Insert(t)
	return err
}

type pageLoadMetrics struct{}

var TbPageLoadMetrics pageLoadMetrics

type FetchPageLoadMetricsRequest struct {
	StartTime int64
	EndTime   int64
	PID       []string
	Metric    []string
}

func (t *pageLoadMetrics) FetchData(r *FetchPageLoadMetricsRequest) []PageLoadMetrics {
	var tables []PageLoadMetrics

	session := database.GetServiceManagerEngine().Where("date >= ?", r.StartTime).Where("date <= ?", r.EndTime)
	if len(r.PID) > 0 {
		session.In("pid", r.PID)
	}
	if len(r.Metric) > 0 {
		session.In("metric_type", r.Metric)
	}
	if err := session.Find(&tables); err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
