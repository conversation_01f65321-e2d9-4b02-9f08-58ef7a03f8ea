FROM registry-vpc.cn-hangzhou.aliyuncs.com/dailyyoga/python3:latest

# 设置时区为 CST
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && echo "Asia/Shanghai" > /etc/timezone

RUN pip install --upgrade pip -i https://pypi.tuna.tsinghua.edu.cn/simple \
    && pip install openpyxl -i https://pypi.tuna.tsinghua.edu.cn/simple \
    && pip install Pillow -i https://pypi.tuna.tsinghua.edu.cn/simple \
    && pip install pandas -i https://pypi.tuna.tsinghua.edu.cn/simple \
    && pip install matplotlib -i https://pypi.tuna.tsinghua.edu.cn/simple \
     && pip install requests -i https://pypi.tuna.tsinghua.edu.cn/simple

COPY app /usr/local/bin/app

COPY ./scripts/SourceHanSansSC-Regular.otf /SourceHanSansSC-Regular.otf
COPY ./scripts/generate_api_alert_table.py /generate_api_alert_table.py

# http port
EXPOSE 23142
# run
ENTRYPOINT ["/usr/local/bin/app"]
