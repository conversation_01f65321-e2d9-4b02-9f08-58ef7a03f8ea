package config

import "gitlab.dailyyoga.com.cn/server/go-artifact/config"

type Conf struct {
	config.Base
	config.HTTP
	ELK              ELK             `yaml:"elk"`
	H2OELK           H2OELK          `yaml:"elk_h2o"`
	YogaRedis01      config.Redis    `yaml:"yoga_redis_01"`
	ServeropsDB      config.Database `yaml:"serverops_db"`
	ServiceManagerDB config.Database `yaml:"service_manager_db"`
	SensorsToken     string          `yaml:"api_secret"`
}

type ELK struct {
	ServerAddress  []string `yaml:"server_address"`
	APIKey         string   `yaml:"api_key"`
	ExcludeService []string `yaml:"exclude_service"`
}

type H2OELK struct {
	ServerAddress    []string `yaml:"server_address"`
	APIKey           string   `yaml:"api_key"`
	ExcludePodPrefix []string `yaml:"exclude_pod_prefix"`
}

var gConfig Conf

func Get() *Conf {
	return &gConfig
}
