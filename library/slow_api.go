// nolint
package library

type SlowApiStatRecordType int

func (e SlowApiStatRecordType) String() string {
	if name, ok := recordTypeName[e]; ok {
		return name
	}
	return ""
}

type IndicatorPlatform string

const (
	SlowApiStatUnknown       SlowApiStatRecordType = 0
	SlowApiStatTotal         SlowApiStatRecordType = 1
	SlowApiStatGT500         SlowApiStatRecordType = 2
	SlowApiStatGT300         SlowApiStatRecordType = 3
	SlowApiStatGT500Upstream SlowApiStatRecordType = 4

	IndicatorPlatformH2  IndicatorPlatform = "h2"
	IndicatorPlatformH2O IndicatorPlatform = "h2o"
)

var recordTypeName = map[SlowApiStatRecordType]string{
	SlowApiStatUnknown:       "unknown",
	SlowApiStatTotal:         "total_count",
	SlowApiStatGT300:         "gte_300ms_count",
	SlowApiStatGT500:         "gte_500ms_count",
	SlowApiStatGT500Upstream: "upstream_gte_500ms_count",
}
