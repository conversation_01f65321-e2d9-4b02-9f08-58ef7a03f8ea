package library

// 神策表盘设备类型
const (
	TerminalAll     = 0 // 总
	TerminalAndroid = 1
	TerminalIOS     = 2

	ChannelAll     = 0 // 总
	ChannelNature  = 1 // 自然量
	ChannelPayment = 2 // 付费
	ChannelUnknown = 3 // 付费

	PayTypeAll    = 0 // 总
	PayTypeWx     = 1 // 微信
	PayTypeWxSign = 2 // 微信签约代扣
	PayTypeAli    = 3 // 支付宝自动订阅
	PayTypeIos    = 4 // IOS应用商店
)

var TerminalMap = map[string]int64{
	"android": TerminalAndroid,
	"ios":     TerminalIOS,
}

var ItSTerminalMap = map[int64]string{
	TerminalAndroid: "android",
	TerminalIOS:     "ios",
}

var ChannelMap = map[string]int64{
	"自然量":  ChannelNature,
	"付费渠道": ChannelPayment,
	"":     ChannelUnknown,
}

var ItSChannelMap = map[int64]string{
	ChannelNature:  "自然量",
	ChannelPayment: "付费渠道",
	ChannelUnknown: "未知",
}

var PayTypeMap = map[string]int64{
	"微信":      PayTypeWx,
	"微信签约代扣":  PayTypeWxSign,
	"支付宝自动订阅": PayTypeAli,
	"IOS应用商店": PayTypeIos,
}

var ItSPayTypeMap = map[int64]string{
	PayTypeWx:     "微信",
	PayTypeWxSign: "微信签约代扣",
	PayTypeAli:    "支付宝自动订阅",
	PayTypeIos:    "IOS应用商店",
}
