#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import sys
import os
import argparse

# 尝试导入PIL，如果失败则提供友好的错误信息
try:
    from PIL import Image, ImageDraw, ImageFont
except ImportError:
    print("错误: 缺少PIL库，请安装: pip install Pillow")
    sys.exit(1)

def generate_api_alert_table_image(data_file, output_file):
    """
    根据JSON数据生成API告警表格图片
    """
    # 读取JSON数据
    with open(data_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 设置字体
    font_path = "/SourceHanSansSC-Regular.otf"
    if not os.path.exists(font_path):
        font_path = "/System/Library/Fonts/Arial.ttf"  # 备用字体
    
    try:
        title_font = ImageFont.truetype(font_path, size=20)
        header_font = ImageFont.truetype(font_path, size=16)
        content_font = ImageFont.truetype(font_path, size=14)
    except:
        # 如果字体加载失败，使用默认字体
        title_font = ImageFont.load_default()
        header_font = ImageFont.load_default()
        content_font = ImageFont.load_default()
    
    # 表格配置
    padding = 20
    title_height = 50
    info_height = 30
    header_height = 40
    row_height = 35
    
    # 列宽配置（根据内容调整）
    col_widths = [80, 500, 120, 180, 120]  # 项目, 接口名称, 7天平均值, 当前慢响应率, 上升幅度
    
    # 计算图片尺寸
    img_width = sum(col_widths) + padding * 2
    img_height = title_height + info_height + header_height + len(data['alerts']) * row_height + 60
    
    # 创建图片
    image = Image.new("RGB", (img_width, img_height), "white")
    draw = ImageDraw.Draw(image)
    
    # 绘制标题背景
    title_bg_color = (255, 193, 7)  # 橙色
    draw.rectangle([0, 0, img_width, title_height], fill=title_bg_color)
    
    # 绘制标题文字
    title_text = "300ms慢响应异常接口日播报"
    title_bbox = draw.textbbox((0, 0), title_text, font=title_font)
    title_width = title_bbox[2] - title_bbox[0]
    title_x = (img_width - title_width) // 2
    draw.text((title_x, 15), title_text, font=title_font, fill="black")
    
    # 绘制监控信息
    info_y = title_height + 5
    info_text = f"监控日期: {data['date']}"
    draw.text((padding, info_y), info_text, font=content_font, fill="black")
    
    # 绘制表格头部背景
    header_y = title_height + info_height
    header_bg_color = (233, 236, 239)  # 浅灰色
    draw.rectangle([padding, header_y, img_width - padding, header_y + header_height], fill=header_bg_color)
    
    # 绘制表格头部边框
    draw.rectangle([padding, header_y, img_width - padding, header_y + header_height], outline="black", width=1)
    
    # 绘制表格头部文字
    headers = ["项目", "接口名称", "7天平均值", "当前慢响应率", "上升幅度"]
    x = padding
    for i, header in enumerate(headers):
        # 绘制列分隔线
        if i > 0:
            draw.line([x, header_y, x, header_y + header_height], fill="black", width=1)
        
        # 绘制头部文字
        header_bbox = draw.textbbox((0, 0), header, font=header_font)
        header_width = header_bbox[2] - header_bbox[0]
        text_x = x + (col_widths[i] - header_width) // 2
        text_y = header_y + (header_height - (header_bbox[3] - header_bbox[1])) // 2
        draw.text((text_x, text_y), header, font=header_font, fill="black")
        
        x += col_widths[i]
    
    # 绘制表格内容
    content_y = header_y + header_height
    for row_idx, alert in enumerate(data['alerts']):
        y = content_y + row_idx * row_height
        
        # 绘制行背景（交替颜色）
        if row_idx % 2 == 1:
            row_bg_color = (248, 249, 250)  # 浅灰色
            draw.rectangle([padding, y, img_width - padding, y + row_height], fill=row_bg_color)
        
        # 绘制行边框
        draw.rectangle([padding, y, img_width - padding, y + row_height], outline="black", width=1)
        
        # 准备行数据
        project = alert['project']
        api_name = alert['api_name']
        if len(api_name) > 65:  # 截断长接口名称
            api_name = api_name[:62] + "..."
        
        avg_percentage = f"{alert['avg_percentage']:.2f}%"
        current_percentage = f"{alert['current_percentage']:.2f}% ({alert['current_gt300']}/{alert['current_total']})"
        difference = f"{alert['difference']:.2f}%"
        
        row_data = [project, api_name, avg_percentage, current_percentage, difference]
        
        # 绘制单元格内容
        x = padding
        for i, cell_data in enumerate(row_data):
            # 绘制列分隔线
            if i > 0:
                draw.line([x, y, x, y + row_height], fill="black", width=1)
            
            # 确定文字颜色
            text_color = "black"
            if i == 4:  # 上升幅度列
                diff_value = alert['difference']
                if diff_value > 2.0:
                    text_color = (220, 53, 69)  # 红色
                elif diff_value > 1.0:
                    text_color = (255, 193, 7)  # 橙色
                else:
                    text_color = (40, 167, 69)  # 绿色
            
            # 绘制文字
            cell_bbox = draw.textbbox((0, 0), cell_data, font=content_font)
            cell_width = cell_bbox[2] - cell_bbox[0]
            cell_height = cell_bbox[3] - cell_bbox[1]
            
            # 左对齐项目和接口名称，居中对齐数值
            if i <= 1:  # 项目和接口名称左对齐
                text_x = x + 10
            else:  # 数值居中对齐
                text_x = x + (col_widths[i] - cell_width) // 2
            
            text_y = y + (row_height - cell_height) // 2
            draw.text((text_x, text_y), cell_data, font=content_font, fill=text_color)
            
            x += col_widths[i]
    
    # 绘制底部提示
    footer_y = content_y + len(data['alerts']) * row_height + 20
    footer_text = "请相关同学及时处理接口慢响应问题！"
    draw.text((padding, footer_y), footer_text, font=content_font, fill=(220, 53, 69))
    
    # 保存图片
    image.save(output_file)
    print(f"API告警表格图片生成成功: {output_file}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="生成API告警表格图片")
    parser.add_argument("data_file", help="JSON数据文件路径")
    parser.add_argument("output_file", help="输出图片文件路径")
    
    args = parser.parse_args()
    generate_api_alert_table_image(args.data_file, args.output_file)
