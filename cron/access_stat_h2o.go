package cron

import (
	_ "embed"
	"encoding/json"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/elastic/go-elasticsearch/v8"
	"github.com/pkg/errors"
	"gitlab.dailyyoga.com.cn/rdc/serverops/config"
	"gitlab.dailyyoga.com.cn/rdc/serverops/database/indicator"
	"gitlab.dailyyoga.com.cn/rdc/serverops/library"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"golang.org/x/exp/maps"
	"k8s.io/utils/strings/slices"
)

//go:embed es_payload/h2o/access.lens.tpl
var accessLensPayload []byte

//go:embed es_payload/h2o/pod_name.lens.tpl
var podNameLensPayload []byte

func StatAccessH2O() error {
	conf := config.Get().H2OELK
	es, err := elasticsearch.NewClient(elasticsearch.Config{
		Addresses: conf.ServerAddress, APIKey: conf.APIKey, EnableDebugLogger: true,
	})
	if err != nil {
		logger.Error(err)
		return err
	}

	date := getYesterday()
	logger.Infof("cron stat access_log date: %s", date.Format("2006-01-02"))

	podNames := fetchPodNames(es, date)
	servicePods := make(map[string][]string)
	for _, pod := range podNames {
		prefix := getPodNamePrefix(pod)
		if slices.Contains(conf.ExcludePodPrefix, prefix) {
			continue
		}
		if _, ok := servicePods[prefix]; !ok {
			servicePods[prefix] = make([]string, 0)
		}
		servicePods[prefix] = append(servicePods[prefix], pod)
	}

	recordConditions := fetchRecordTypeCondition()
	services := maps.Keys(servicePods)
	for _, service := range services {
		pods := servicePods[service]
		for recordType, condition := range recordConditions {
			counter := make(map[string]int64)
			for _, pod := range pods {
				recordCount, err := fetchRecordCount(es, pod, condition, date)
				if err != nil {
					logger.Errorf("fetch record count failed, err: %s", err)
					continue
				}
				for api, cnt := range recordCount {
					if _, ok := counter[api]; !ok {
						counter[api] = 0
					}
					counter[api] += cnt
				}
			}
			saveServiceRecord(service, recordType, counter, date)
		}
	}

	SummaryH2oAccess(date)

	return nil
}

func fetchRecordCount(es *elasticsearch.Client, pod, condition string, date time.Time) (map[string]int64, error) {
	payload := getH2oSearchPayload(pod, condition, date)
	if payload == nil {
		return nil, errors.Errorf("search payload is nil")
	}

	aggregations, err := fetchH2oAggregations(es, payload)
	if err != nil {
		return nil, err
	}

	counter := make(map[string]int64)
	for _, data := range aggregations {
		for _, item := range data.Buckets {
			if item.Key == "" || item.DocCount == 0 {
				continue
			}
			counter[item.Key] = item.DocCount
		}
	}
	return counter, nil
}

func saveServiceRecord(service string, recordType library.SlowApiStatRecordType, counter map[string]int64, date time.Time) {
	dateIndex, err := strconv.Atoi(date.Format("20060102"))
	if err != nil {
		return
	}
	for api, cnt := range counter {
		if api[0] != '/' {
			api = "/" + api
		}
		record := &indicator.ApiStatRecord{
			Platform: "h2o", SysFrom: "elk", Date: dateIndex,
			RecordType: recordType, Service: service, Name: api, Count: cnt,
		}
		if err := setRecord(record); err != nil {
			logger.Errorf("set record failed, err: %s", err)
			continue
		}
		logger.Infof("%d<%s:%s>  %s -> %d", dateIndex, service, recordType.String(), api, cnt)
	}
}

func getH2oSearchPayload(pod, condition string, date time.Time) io.Reader {
	startTimeUTC, endTimeUTC := getUTCTime(date)
	data := &elasticsearchPayloadInfo{
		PodName:      pod,
		Condition:    condition,
		StartTimeUTC: startTimeUTC, EndTimeUTC: endTimeUTC,
	}
	return extraPayload(data, string(accessLensPayload))
}

func getPodNamePrefix(podName string) string {
	for i := 1; i <= 2; i++ {
		index := strings.LastIndex(podName, "-")
		podName = podName[:index]
	}
	return podName
}

func fetchPodNames(es *elasticsearch.Client, date time.Time) []string {
	payload := getPodNameLensPayload(date)
	if payload == nil {
		return nil
	}

	aggregations, err := fetchH2oAggregations(es, payload)
	if err != nil {
		logger.Error(err)
		return nil
	}

	pods := make([]string, 0)
	for _, data := range aggregations {
		for _, item := range data.Buckets {
			if item.Key == "" {
				continue
			}
			pods = append(pods, item.Key)
		}
	}
	return pods
}

func fetchH2oAggregations(es *elasticsearch.Client, payload io.Reader) (map[string]aggregation, error) {
	if es == nil {
		return nil, errors.New("es client is nil")
	}

	res, err := es.Search(es.Search.WithIndex("access-prod-*"), es.Search.WithBody(payload))
	if err != nil {
		return nil, err
	}
	defer func() {
		_ = res.Body.Close()
	}()

	if res.StatusCode != http.StatusOK {
		return nil, errors.Errorf("response status_code: %d", res.StatusCode)
	}

	ret := &searchResponse{}
	if err := json.NewDecoder(res.Body).Decode(&ret); err != nil {
		return nil, err
	}
	return ret.Aggregations, nil
}

func getPodNameLensPayload(date time.Time) io.Reader {
	startTimeUTC, endTimeUTC := getUTCTime(date)
	data := &elasticsearchPayloadInfo{
		StartTimeUTC: startTimeUTC, EndTimeUTC: endTimeUTC,
	}
	return extraPayload(data, string(podNameLensPayload))
}
