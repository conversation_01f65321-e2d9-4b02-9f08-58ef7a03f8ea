package cron

import (
	"strconv"
	"time"

	"gitlab.dailyyoga.com.cn/rdc/serverops/database/indicator"
	"gitlab.dailyyoga.com.cn/rdc/serverops/library"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
)

func SummaryAccess(summaryDate time.Time) {
	serviceProject := map[string]indicator.APIProject{
		"dancefit":           indicator.ProjectDanceFit,
		"dancefit-admin":     indicator.ProjectDanceFit,
		"fitness-admin-api":  indicator.ProjectFitness,
		"fitness":            indicator.ProjectFitness,
		"stretch":            indicator.ProjectStretch,
		"stretch-admin-api":  indicator.ProjectStretch,
		"children-admin-api": indicator.ProjectChildren,
		"children":           indicator.ProjectChildren,
	}
	summary(summaryDate, "h2", serviceProject)
}

func SummaryH2oAccess(summaryDate time.Time) {
	serviceProject := map[string]indicator.APIProject{
		"fitness-admin": indicator.ProjectFitness,
		"fitness-api":   indicator.ProjectFitness,
		"dancefit-api":  indicator.ProjectDanceFit,
		"dailyyoga-api": indicator.ProjectYoga,
		"api-admin":     indicator.ProjectYoga,
		"stretch-api":   indicator.ProjectStretch,
		"stretch-admin": indicator.ProjectStretch,
	}
	summary(summaryDate, "h2o", serviceProject)
}

func summary(summaryDate time.Time, platform string, serviceProject map[string]indicator.APIProject) {
	if summaryDate.IsZero() {
		summaryDate = getYesterday()
	}
	date, _ := strconv.Atoi(summaryDate.Format("20060102"))
	datas := indicator.TbApiStatRecord.Summary(date, platform)

	summary := make(map[string]map[library.SlowApiStatRecordType]int64)

	for _, item := range datas {
		project := indicator.ProjectYoga
		if _, ok := serviceProject[item.Service]; ok {
			project = serviceProject[item.Service]
		}
		projectName, ok := indicator.SummaryProject[project]
		if !ok || projectName == "" {
			logger.Error("unsupported summary project, project: %s", project)
			continue
		}
		if _, ok := summary[projectName]; !ok {
			summary[projectName] = map[library.SlowApiStatRecordType]int64{
				library.SlowApiStatTotal: 0,
				library.SlowApiStatGT500: 0,
				library.SlowApiStatGT300: 0,
			}
		}
		summary[projectName][item.RecordType] += item.Count
	}

	for projectName, data := range summary {
		for recordType, count := range data {
			bean := &indicator.APIStatSummary{
				Date: date, RecordType: recordType, Count: count, Platform: platform, Service: projectName,
			}
			if row := indicator.TbAPIStatSummary.Row(date, platform, projectName, recordType); row == nil ||
				row.ID == 0 {
				if err := bean.Save(); err != nil {
					logger.Error(err)
				}
			}
		}
	}
	logger.Infof("slow_api summary calculate success，platform: %s, date: %d", platform, date)
}
