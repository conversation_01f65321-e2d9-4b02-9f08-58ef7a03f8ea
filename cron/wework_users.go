package cron

import (
	"context"

	db "gitlab.dailyyoga.com.cn/rdc/serverops/database/wework"
	service "gitlab.dailyyoga.com.cn/rdc/serverops/service/wework"
	"gitlab.dailyyoga.com.cn/rdc/serverops/service/wework/agent"
	member "gitlab.dailyyoga.com.cn/rdc/serverops/service/wework/users"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
)

func SyncWeworkAgentUsers() error {
	ctx := context.TODO()
	agentID := service.ServeropsAgentID

	appInfo, err := agent.GetInfo(ctx, agentID)
	if err != nil {
		return err
	}

	users := make(map[string]string)

	if len(appInfo.AllowUserInfos.User) > 0 {
		for _, v := range appInfo.AllowUserInfos.User {
			if _, ok := users[v.UserID]; ok {
				continue
			}
			users[v.UserID] = ""
		}
	}

	if len(appInfo.AllowPartys.PartyID) > 0 {
		for _, v := range appInfo.AllowPartys.PartyID {
			lists, err := member.UserList(ctx, v, true)
			if err != nil {
				logger.Error(err)
			}
			for _, item := range lists {
				if name, ok := users[item.UserID]; ok && name != "" {
					continue
				}
				users[item.UserID] = item.Name
			}
		}
	}
	delete(users, "")
	delete(users, " ")

	// exist users
	userids := db.TbUsers.FetchAllUserID(agentID)
	for _, v := range userids {
		if _, ok := users[v]; ok {
			if users != nil {
				delete(users, v)
			}
		}
	}

	rets := make([]string, 0)
	for id, name := range users {
		if id == "" {
			continue
		}
		user := db.Users{
			AgentID: agentID, UserID: id, Name: name,
		}
		if name == "" {
			profile, err := member.UserInfo(ctx, id)
			if err != nil {
				logger.Error(err)
				delete(users, id)
				continue
			}
			if profile != nil && profile.Name != "" {
				users[id] = profile.Name
				user.Name = profile.Name
			}
		}
		if err := user.Save(); err != nil {
			logger.Error(err)
		}
		rets = append(rets, user.Name)
	}
	logger.Infof("sync users %v", rets)
	return nil
}
