package cron

import (
	"encoding/json"
	"net/http"
	"strings"
	"sync"

	"gitlab.dailyyoga.com.cn/rdc/serverops/config"
	lib "gitlab.dailyyoga.com.cn/rdc/serverops/library"
	"gitlab.dailyyoga.com.cn/server/go-artifact/goroutine"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/go-artifact/requests"
)

// nolint
const (
	SensorWebURI   = "http://101.37.146.94:8107"
	SensorAPIURL   = "/api/events/report"
	SensorProject  = "production"
	WaitGroupCount = 2
)

type Rows struct {
	Values    [][]int     `json:"values"`
	ByValues  []string    `json:"by_values"`
	AvgValues [][]float64 `json:"avg_values"`
	MinValues [][]float64 `json:"min_values"`
	MaxValues [][]float64 `json:"max_values"`
}

type CommonResult struct {
	Series            []string `json:"series"`
	Rows              []Rows   `json:"rows"`
	NumRows           int64    `json:"num_rows"`
	TotalRows         int64    `json:"total_rows"`
	Approx            bool     `json:"approx"`
	DownloadTruncated bool     `json:"download_truncated"`
	Truncated         bool     `json:"truncated"`
}

type SensorWatchRsp struct {
	DetailResult             CommonResult    `json:"detail_result"`
	RollupResult             CommonResult    `json:"rollup_result"`
	TotalDetailResult        CommonResult    `json:"total_detail_result"`
	TotalRollupResult        CommonResult    `json:"total_rollup_result"`
	MeasureApproxMaps        map[string]bool `json:"measure_approx_maps"`
	DownloadTruncated        bool            `json:"download_truncated"`
	ReportUpdateTime         string          `json:"report_update_time"`
	DataUpdateTime           string          `json:"data_update_time"`
	DataSufficientUpdateTime string          `json:"data_sufficient_update_time"`
	Truncated                bool            `json:"truncated"`
	SamplingFactor           int64           `json:"sampling_factor"`
}

type WatchData struct {
	Terminal    int64 `json:"terminal"`
	Channel     int64 `json:"channel"`
	PayType     int64 `json:"pay_type"`
	Income      int64 `json:"income"`
	MemberCount int64 `json:"member_count"`
	UnitPrice   int64 `json:"unit_price"`
}

func SensorAPI() error {
	logger.Info("神策表盘任务执行开始")
	token := "?token=" + config.Get().SensorsToken
	project := "&project=" + SensorProject
	sensorURL := SensorWebURI + SensorAPIURL + token + project
	var wg sync.WaitGroup
	wg.Add(WaitGroupCount)
	goroutine.GoSafely(func() {
		defer wg.Done()
		sensorWatchNewOBAllIncome(sensorURL)
	})
	goroutine.GoSafely(func() {
		defer wg.Done()
		sensorWatchNewOBDetail(sensorURL)
	})
	wg.Wait()
	logger.Info("神策表盘任务执行结束")
	return nil
}

func sensorWatchNewOBAllIncome(sensorURL string) {
	// 请求参数
	str := "{\"measures\":[{\"events\":[\"purchase_vip_operation\"],\"expression\":\"sum(" +
		"event.purchase_vip_operation.actually_pay_money)+sum(event.purchase_vip_operation.resource_num)|%d\"," +
		"\"format\":\"%d\",\"expression_denominator_without_group\":false,\"name\":\"新用户ob收入\"," +
		"\"expression_filters\":[{},{}],\"editName\":\"新用户ob收入\",\"subjectIdIsUserId\":false},{\"event_name\":" +
		"\"purchase_vip_operation\",\"aggregator\":\"unique\",\"name\":\"支付用户数\",\"editName\":\"支付用户数\"}," +
		"{\"events\":[\"purchase_vip_operation\"],\"expression\":\"(sum(" +
		"event.purchase_vip_operation.actually_pay_money)+sum(event.purchase_vip_operation.resource_num))/" +
		"uniqcount(event.purchase_vip_operation)|%d\",\"format\":\"%d\",\"expression_denominator_without_group\":" +
		"false,\"name\":\"客单价\",\"subjectIdIsUserId\":false,\"expression_filters\":[{},{},{}],\"editName\":\"客单价\"}" +
		"],\"from_date\":\"2023-05-11 00:00:00\",\"to_date\":\"2023-05-11 23:59:59\",\"unit\":\"hour\",\"by_fields\":[]" +
		",\"detail_and_rollup\":true,\"enable_detail_follow_rollup_by_values_rank\":true,\"sub_task_type\":" +
		"\"SEGMENTATION\",\"time_zone_mode\":\"\",\"server_time_zone\":\"\",\"rangeText\":\"0 day\",\"compareKey\":" +
		"\"\",\"include_today\":true,\"chartType\":\"line\",\"filter\":{\"conditions\":[{\"field\":\"" +
		"event.$Anything.source\",\"function\":\"equal\",\"params\":[\"仿海外onboard流程_安装后首次\",\"" +
		"仿海外onboard流程_安装后非首次\",\"仿海外onboard流程_支付挽回弹窗\",\"仿海外onboard流程_退出挽留弹窗\",\"" +
		"仿海外onboard流程_收回首页底部小条\"],\"$$searchValue\":\"订单\",\"$$render_index\":1},{\"field\":\"" +
		"user.signup_time\",\"function\":\"relative_event_time\",\"params\":[\"month\"],\"$$searchValue\":\"" +
		"注册\",\"$$render_index\":2}]},\"bookmarkid\":\"11854\",\"fromDash\":\"{\\\"id\\\":868,\\\"type\\\":\\\"" +
		"normal\\\"}\",\"request_id\":\"1683766231322:799685\",\"use_cache\":true}"

	header := http.Header{}
	header.Set("Content-Type", "application/json")
	reader := strings.NewReader(str)
	resp, err := requests.HTTPRequest(sensorURL, "POST", header, reader)
	if err != nil {
		logger.Error("调用神策API失败", err.Error())
		return
	}
	result := SensorWatchRsp{}
	if err = json.Unmarshal(resp, &result); err != nil {
		logger.Infof("神策表盘返回值解析失败:%v", err.Error())
		return
	}
	logger.Infof("神策表盘原始数据:%v", string(resp))
	logger.Infof("神策表盘数据:%v", result)
	// 新用户ob各项总数
	obNewValues := result.RollupResult.Rows[0].Values
	obNewAll := &WatchData{
		Terminal:    lib.TerminalAll,
		Channel:     lib.ChannelAll,
		PayType:     lib.PayTypeAll,
		Income:      int64(obNewValues[0][0]),
		MemberCount: int64(obNewValues[0][1]),
		UnitPrice:   int64(obNewValues[0][2]),
	}
	logger.Infof("新用户ob各项总数： 新用户ob收入:%v, 支付用户数:%v，客单价:%v", obNewAll.Income, obNewAll.MemberCount,
		obNewAll.UnitPrice)
}

func sensorWatchNewOBDetail(sensorURL string) {
	// 请求参数
	str := "{\"measures\":[{\"events\":[\"purchase_vip_operation\"],\"expression\":\"" +
		"sum(event.purchase_vip_operation.actually_pay_money)+sum(event.purchase_vip_operation.resource_num)|%d\"," +
		"\"format\":\"%d\",\"expression_denominator_without_group\":false,\"name\":\"新用户ob收入\",\"" +
		"expression_filters\":[{},{}],\"editName\":\"新用户ob收入\",\"subjectIdIsUserId\":false},{\"event_name\":" +
		"\"purchase_vip_operation\",\"aggregator\":\"unique\",\"name\":\"支付用户数\",\"editName\":\"支付用户数\"}," +
		"{\"events\":[\"purchase_vip_operation\"],\"expression\":\"(" +
		"sum(event.purchase_vip_operation.actually_pay_money)+" +
		"sum(event.purchase_vip_operation.resource_num))/uniqcount(event.purchase_vip_operation)|%d\",\"format\":" +
		"\"%d\",\"expression_denominator_without_group\":false,\"name\":\"客单价\",\"subjectIdIsUserId\":" +
		"false,\"expression_filters\":[{},{},{}],\"editName\":\"客单价\"}],\"from_date\":\"2023-05-10 00:00:00\"," +
		"\"to_date\":\"2023-05-10 23:59:59\",\"unit\":\"hour\",\"by_fields\":[\"event.$Anything.platform\"," +
		"\"user.channels\",\"event.$Anything.purchase_type\"],\"detail_and_rollup\":true," +
		"\"enable_detail_follow_rollup_by_values_rank\":true,\"sub_task_type\":\"SEGMENTATION\",\"time_zone_mode\"" +
		":\"\",\"server_time_zone\":\"\",\"rangeText\":\"0 day\",\"compareKey\":\"\",\"include_today\":true," +
		"\"chartType\":\"line\",\"filter\":{\"conditions\":[{\"field\":\"event.$Anything.source\",\"function\":" +
		"\"equal\",\"params\":[\"仿海外onboard流程_安装后首次\",\"仿海外onboard流程_安装后非首次\"," +
		"\"仿海外onboard流程_支付挽回弹窗\",\"仿海外onboard流程_退出挽留弹窗\",\"仿海外onboard流程_收回首页底部小条\"]," +
		"\"$$searchValue\":\"订单\",\"$$render_index\":1},{\"field\":\"user.signup_time\",\"function\":" +
		"\"relative_event_time\",\"params\":[\"month\"],\"$$searchValue\":\"注册\",\"$$render_index\":2}]}," +
		"\"bucket_params\":{},\"bookmarkid\":\"11855\",\"fromDash\":\"{\\\"id\\\":868,\\\"type\\\":\\\"normal\\\"}\"," +
		"\"request_id\":\"1683712194571:446610\",\"use_cache\":true}"

	header := http.Header{}
	header.Set("Content-Type", "application/json")
	reader := strings.NewReader(str)
	resp, err := requests.HTTPRequest(sensorURL, "POST", header, reader)
	if err != nil {
		logger.Error("调用神策API失败", err.Error())
		return
	}
	result := SensorWatchRsp{}
	if err = json.Unmarshal(resp, &result); err != nil {
		logger.Infof("神策表盘返回值解析失败:%v", err.Error())
		return
	}
	logger.Infof("神策表盘原始数据:%v", string(resp))
	logger.Infof("神策表盘数据:%v", result)
	// 新用户ob
	obNew := result.RollupResult.Rows
	data := make([]*WatchData, 0)
	for _, v := range obNew {
		temp := &WatchData{
			Terminal:    lib.TerminalMap[v.ByValues[0]],
			Channel:     lib.ChannelMap[v.ByValues[1]],
			PayType:     lib.PayTypeMap[v.ByValues[2]],
			Income:      int64(v.Values[0][0]),
			MemberCount: int64(v.Values[0][1]),
			UnitPrice:   int64(v.Values[0][2]),
		}
		data = append(data, temp)
	}
	for _, v := range data {
		logger.Infof("新用户ob %v %v %v 新用户ob收入:%v,支付用户数:%v，客单价:%v", lib.ItSTerminalMap[v.Terminal],
			lib.ItSChannelMap[v.Channel], lib.ItSPayTypeMap[v.PayType], v.Income, v.MemberCount, v.UnitPrice)
	}
}
