package cron

import (
	"crypto/md5"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"strconv"
	"time"

	"gitlab.dailyyoga.com.cn/rdc/serverops/database/indicator"
	"gitlab.dailyyoga.com.cn/rdc/serverops/library"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/go-artifact/wework/robot"
)

// APIAlertData 用于传递给Python脚本的数据结构
type APIAlertData struct {
	Date   int         `json:"date"`
	Alerts []AlertItem `json:"alerts"`
}

type AlertItem struct {
	Project           string  `json:"project"`
	APIName           string  `json:"api_name"`
	AvgPercentage     float64 `json:"avg_percentage"`
	CurrentPercentage float64 `json:"current_percentage"`
	CurrentGT300      int     `json:"current_gt300"`
	CurrentTotal      int     `json:"current_total"`
	Difference        float64 `json:"difference"`
}

// APIMonitor API监控主函数，每天9:50执行
func APIMonitor() error {
	logger.Info("开始执行API监控任务")

	// 获取当前日期和前7天的日期
	end := time.Now().AddDate(0, 0, -1)
	enDate := end.Format("20060102")
	endDateInt, _ := strconv.Atoi(enDate)

	// 获取前7天的日期
	var dates []int
	for i := 1; i <= 7; i++ {
		date := end.AddDate(0, 0, -i).Format("20060102")
		dateInt, _ := strconv.Atoi(date)
		dates = append(dates, dateInt)
	}

	// 监控所有项目
	projects := []indicator.APIProject{
		indicator.ProjectYoga,
		indicator.ProjectDanceFit,
		indicator.ProjectFitness,
		//indicator.ProjectStretch,
		//indicator.ProjectChildren,
	}

	// 收集所有项目的告警数据
	allProjectAlerts := make(map[indicator.APIProject][]APIAlert)
	hasProjectAlert := false

	for _, project := range projects {
		if err := monitorProject(project, endDateInt, dates); err != nil {
			logger.Errorf("监控项目 %s 失败: %v", project, err)
		}

		// 监控接口级别的异常，收集告警数据
		alerts, err := getProjectAPIAlerts(project, endDateInt, dates)
		if err != nil {
			logger.Errorf("监控项目 %s 接口失败: %v", project, err)
		} else if len(alerts) > 0 {
			allProjectAlerts[project] = alerts
			hasProjectAlert = true
		}
	}

	// 如果有接口级别的告警，发送统一的表格播报
	if hasProjectAlert {
		sendUnifiedAPIAlerts(allProjectAlerts, endDateInt)
	}

	logger.Info("API监控任务执行完成")
	return nil
}

// monitorProject 监控单个项目
func monitorProject(project indicator.APIProject, today int, pastDates []int) error {
	// 1. 获取当日数据
	todayData, err := getProjectSummaryData(project, today, today)
	if err != nil {
		return fmt.Errorf("获取当日数据失败: %v", err)
	}

	// 2. 获取过去7天数据
	startDate := pastDates[len(pastDates)-1]
	endDate := pastDates[0]
	pastData, err := getProjectSummaryData(project, startDate, endDate)
	if err != nil {
		return fmt.Errorf("获取历史数据失败: %v", err)
	}

	// 3. 计算当日300ms慢响应百分比
	todayPercentage := calculateGT300Percentage(todayData)

	// 4. 计算过去7天平均300ms慢响应百分比
	avgPercentage := calculateAvgGT300Percentage(pastData)
	// 5. 检查是否需要告警
	if shouldAlert(todayPercentage, avgPercentage) {
		sendAlertWithChart(project, todayPercentage, avgPercentage, today, pastDates, pastData)
	}

	logger.Infof("项目 %s 监控完成 - 当日慢响应率: %.2f%%, 7天平均: %.2f%%",
		project, todayPercentage, avgPercentage)

	return nil
}

// getProjectSummaryData 获取项目汇总数据
func getProjectSummaryData(project indicator.APIProject, startDate, endDate int) ([]indicator.APIStatSummary, error) {
	req := &indicator.ApiStatSearchPayload{
		Project:   project,
		Platform:  string(library.IndicatorPlatformH2),
		StartDate: startDate,
		EndDate:   endDate,
	}

	records := indicator.TbApiStatRecord.Search(req)

	// 将ApiStatRecord转换为APIStatSummary格式
	summaryMap := make(map[string]*indicator.APIStatSummary)

	for _, record := range records {
		key := fmt.Sprintf("%d_%d_%s", record.Date, record.RecordType, record.Service)
		if summary, exists := summaryMap[key]; exists {
			summary.Count += record.Count
		} else {
			// 创建新的汇总记录
			summaryMap[key] = &indicator.APIStatSummary{
				Date:       record.Date,
				RecordType: record.RecordType,
				Count:      record.Count,
				Platform:   record.Platform,
				Service:    record.Service,
			}
		}
	}

	var result []indicator.APIStatSummary
	for _, summary := range summaryMap {
		result = append(result, *summary)
	}

	return result, nil
}

// calculateGT300Percentage 计算300ms慢响应百分比
func calculateGT300Percentage(data []indicator.APIStatSummary) float64 {
	var totalCount, gt300Count int64

	for _, item := range data {
		switch item.RecordType {
		case library.SlowApiStatTotal:
			totalCount += item.Count
		case library.SlowApiStatGT300:
			gt300Count += item.Count
		}
	}

	if totalCount == 0 {
		return 0
	}

	return float64(gt300Count) / float64(totalCount) * 100
}

// calculateAvgGT300Percentage 计算过去7天平均300ms慢响应百分比
func calculateAvgGT300Percentage(data []indicator.APIStatSummary) float64 {
	// 按日期分组统计
	dateStats := make(map[int]struct {
		total int64
		gt300 int64
	})

	for _, item := range data {
		stats := dateStats[item.Date]
		switch item.RecordType {
		case library.SlowApiStatTotal:
			stats.total += item.Count
		case library.SlowApiStatGT300:
			stats.gt300 += item.Count
		}
		dateStats[item.Date] = stats
	}

	// 计算每天的百分比，然后求平均
	var totalPercentage float64
	var validDays int

	for _, stats := range dateStats {
		if stats.total > 0 {
			percentage := float64(stats.gt300) / float64(stats.total) * 100
			totalPercentage += percentage
			validDays++
		}
	}

	if validDays == 0 {
		return 0
	}

	return totalPercentage / float64(validDays)
}

// shouldAlert 判断是否需要告警
func shouldAlert(todayPercentage, avgPercentage float64) bool {
	// 环比超出0.05个百分点告警
	threshold := 0.05
	diff := todayPercentage - avgPercentage

	return diff > threshold
}

// sendAlertWithChart 发送带图表的告警消息
func sendAlertWithChart(project indicator.APIProject, todayPercentage, avgPercentage float64, date int, pastDates []int, pastData []indicator.APIStatSummary) {
	diff := todayPercentage - avgPercentage

	// 生成趋势图标
	trendIcon := "📈"
	if diff > 1.0 {
		trendIcon = "🔴"
	} else if diff > 0.5 {
		trendIcon = "🟡"
	}

	// 发送文字说明
	content := fmt.Sprintf(`> %s <font color="warning">API慢响应告警</font>
>
> 📊 **项目概览**
> 
> • 项目名称: %s
> • 监控日期: %d
> • 告警级别: %s
>
> 📈 **慢响应率统计**
> • 当日慢响应率: **%.2f%%**
> • 7天平均值: **%.2f%%**
> • 环比增长: **+%.2f%%**
>
> 🔍 **详细分析**
> • 当前慢响应率超出近7天平均值 **%.2f个百分点**
> • 建议立即检查相关接口性能
> • 关注是否有新发布或配置变更
>
> ⚠️ 请相关同学及时处理！`,
		trendIcon, project, date, getAlertLevel(diff),
		todayPercentage, avgPercentage, diff, diff)

	// 发送企业微信群消息
	msg := &robot.Message{
		RobotURL: "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=4fe69c87-20ca-41c4-a09c-6a57c38fdade",
		MsgType:  robot.Markdown,
		Markdown: &robot.MarkdownMessage{
			Content: content,
		},
	}

	robot.Get().AddMessage(msg)

	logger.Infof("发送API慢响应告警 - 项目: %s, 当日: %.2f%%, 平均: %.2f%%, 差值: %.2f%%",
		project, todayPercentage, avgPercentage, diff)
}

// getAlertLevel 获取告警级别
func getAlertLevel(diff float64) string {
	if diff > 2.0 {
		return "🔴 严重"
	} else if diff > 1.0 {
		return "🟡 中等"
	} else if diff > 0.5 {
		return "🟠 轻微"
	}
	return "🟢 一般"
}

// getImageMessage 获取图片消息
func getImageMessage(imagePath string) (*robot.ImageMessage, error) {
	imageMessage := &robot.ImageMessage{}

	imageData, err := os.ReadFile(imagePath)
	if err != nil {
		return nil, err
	}

	imageMessage.Base64 = base64.StdEncoding.EncodeToString(imageData)
	hash := md5.Sum(imageData)
	imageMessage.MD5 = fmt.Sprintf("%x", hash)

	return imageMessage, nil
}

// getProjectAPIData 获取项目接口详细数据
func getProjectAPIData(project indicator.APIProject, startDate, endDate int) ([]indicator.ApiStatRecord, error) {
	req := &indicator.ApiStatSearchPayload{
		Project:   project,
		Platform:  string(library.IndicatorPlatformH2),
		StartDate: startDate,
		EndDate:   endDate,
	}

	return indicator.TbApiStatRecord.Search(req), nil
}

// APIAlert 接口告警信息
type APIAlert struct {
	APIName         string
	TodayPercentage float64
	AvgPercentage   float64
	Difference      float64
	TodayCount      int64
	TodayGT300Count int64
}

// analyzeAPIPerformance 分析接口性能
func analyzeAPIPerformance(todayAPIs, pastAPIs []indicator.ApiStatRecord) []APIAlert {
	var alerts []APIAlert

	// 统计当日接口数据
	todayStats := groupAPIStats(todayAPIs)

	// 统计过去7天接口数据
	pastStats := groupAPIStatsByDate(pastAPIs)

	// 分析每个接口
	for apiName, todayData := range todayStats {
		// 检查该接口是否有完整的7天历史数据
		validDays := 0
		for _, dayStats := range pastStats {
			if apiStats, exists := dayStats[apiName]; exists && apiStats.total > 0 {
				validDays++
			}
		}

		// 如果历史数据不足7天，说明是新上线的接口，跳过告警
		if validDays < 7 {
			logger.Infof("接口 %s 历史数据不足7天(%d天)，跳过告警", apiName, validDays)
			continue
		}

		todayPercentage := calculateAPIGT300Percentage(todayData)

		// 计算该接口过去7天的平均百分比
		avgPercentage := calculateAPIAvgPercentage(apiName, pastStats)

		// 检查是否需要告警（阈值可以调整）
		if shouldAlertAPI(todayPercentage, avgPercentage, todayData.total) {
			alerts = append(alerts, APIAlert{
				APIName:         apiName,
				TodayPercentage: todayPercentage,
				AvgPercentage:   avgPercentage,
				Difference:      todayPercentage - avgPercentage,
				TodayCount:      todayData.total,
				TodayGT300Count: todayData.gt300,
			})
		}
	}

	return alerts
}

// APIStats 接口统计数据
type APIStats struct {
	total int64
	gt300 int64
}

// groupAPIStats 按接口名分组统计
func groupAPIStats(records []indicator.ApiStatRecord) map[string]APIStats {
	stats := make(map[string]APIStats)

	for _, record := range records {
		if record.Name == "" {
			continue
		}

		apiStats := stats[record.Name]
		switch record.RecordType {
		case library.SlowApiStatTotal:
			apiStats.total += record.Count
		case library.SlowApiStatGT300:
			apiStats.gt300 += record.Count
		}
		stats[record.Name] = apiStats
	}

	return stats
}

// groupAPIStatsByDate 按日期和接口名分组统计
func groupAPIStatsByDate(records []indicator.ApiStatRecord) map[int]map[string]APIStats {
	stats := make(map[int]map[string]APIStats)

	for _, record := range records {
		if record.Name == "" {
			continue
		}

		if _, exists := stats[record.Date]; !exists {
			stats[record.Date] = make(map[string]APIStats)
		}

		apiStats := stats[record.Date][record.Name]
		switch record.RecordType {
		case library.SlowApiStatTotal:
			apiStats.total += record.Count
		case library.SlowApiStatGT300:
			apiStats.gt300 += record.Count
		}
		stats[record.Date][record.Name] = apiStats
	}

	return stats
}

// calculateAPIGT300Percentage 计算接口300ms慢响应百分比
func calculateAPIGT300Percentage(stats APIStats) float64 {
	if stats.total == 0 {
		return 0
	}
	return float64(stats.gt300) / float64(stats.total) * 100
}

// calculateAPIAvgPercentage 计算接口过去7天平均百分比
func calculateAPIAvgPercentage(apiName string, pastStats map[int]map[string]APIStats) float64 {
	var totalPercentage float64
	var validDays int

	for _, dayStats := range pastStats {
		if apiStats, exists := dayStats[apiName]; exists && apiStats.total > 0 {
			percentage := float64(apiStats.gt300) / float64(apiStats.total) * 100
			totalPercentage += percentage
			validDays++
		}
	}

	if validDays == 0 {
		return 0
	}

	return totalPercentage / float64(validDays)
}

// shouldAlertAPI 判断接口是否需要告警
func shouldAlertAPI(todayPercentage, avgPercentage float64, todayTotal int64) bool {
	// 只对有一定调用量的接口进行监控（避免噪音）
	if todayTotal < 150 {
		return false
	}

	// 环比超出10个百分点告警
	threshold := float64(10)
	diff := todayPercentage - avgPercentage

	return diff > threshold
}

// getProjectAPIAlerts 获取项目的接口告警数据（不发送消息）
func getProjectAPIAlerts(project indicator.APIProject, today int, pastDates []int) ([]APIAlert, error) {
	// 获取当日接口详细数据
	todayAPIs, err := getProjectAPIData(project, today, today)
	if err != nil {
		return nil, fmt.Errorf("获取当日接口数据失败: %v", err)
	}

	// 获取过去7天接口数据
	startDate := pastDates[len(pastDates)-1]
	endDate := pastDates[0]
	pastAPIs, err := getProjectAPIData(project, startDate, endDate)
	if err != nil {
		return nil, fmt.Errorf("获取历史接口数据失败: %v", err)
	}

	// 分析每个接口的异常情况
	alerts := analyzeAPIPerformance(todayAPIs, pastAPIs)

	return alerts, nil
}

// sendUnifiedAPIAlerts 发送统一的接口告警表格
func sendUnifiedAPIAlerts(allProjectAlerts map[indicator.APIProject][]APIAlert, date int) {
	if len(allProjectAlerts) == 0 {
		return
	}

	// 计算最大差值，用于确定告警级别
	maxDiff := 0.0
	totalAlerts := 0
	for _, alerts := range allProjectAlerts {
		totalAlerts += len(alerts)
		for _, alert := range alerts {
			if alert.Difference > maxDiff {
				maxDiff = alert.Difference
			}
		}
	}

	// 生成表格图片
	imagePath, err := generateAPIAlertTableImage(allProjectAlerts, date, maxDiff)
	if err != nil {
		logger.Errorf("生成表格图片失败: %v", err)
		return
	}

	// 发送图片
	imageMessage, err := getImageMessage(imagePath)
	if err != nil {
		logger.Errorf("获取图片消息失败: %v", err)
		return
	}

	// 发送企业微信群消息
	msg := &robot.Message{
		RobotURL: "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=4fe69c87-20ca-41c4-a09c-6a57c38fdade",
		MsgType:  robot.Image,
		Image:    imageMessage,
	}

	robot.Get().AddMessage(msg)

	// 清理临时文件
	defer func() {
		if err := os.Remove(imagePath); err != nil {
			logger.Errorf("删除临时表格图片文件失败: %v", err)
		}
	}()

	logger.Infof("发送统一接口性能告警图片 - 总异常接口数: %d, 涉及项目数: %d", totalAlerts, len(allProjectAlerts))
}

// generateAPIAlertTableImage 生成接口告警表格图片（使用Python脚本）
func generateAPIAlertTableImage(allProjectAlerts map[indicator.APIProject][]APIAlert, date int, maxDiff float64) (string, error) {
	// 准备数据
	var alertItems []AlertItem
	for project, alerts := range allProjectAlerts {
		// 限制每个项目显示的接口数量
		maxAlertsPerProject := 5
		if len(alerts) > maxAlertsPerProject {
			// 按差值排序，取前5个
			for i := 0; i < len(alerts)-1; i++ {
				for j := i + 1; j < len(alerts); j++ {
					if alerts[i].Difference < alerts[j].Difference {
						alerts[i], alerts[j] = alerts[j], alerts[i]
					}
				}
			}
			alerts = alerts[:maxAlertsPerProject]
		}

		// 转换为AlertItem格式
		for _, alert := range alerts {
			alertItems = append(alertItems, AlertItem{
				Project:           string(project),
				APIName:           alert.APIName,
				AvgPercentage:     alert.AvgPercentage,
				CurrentPercentage: alert.TodayPercentage,
				CurrentGT300:      int(alert.TodayGT300Count),
				CurrentTotal:      int(alert.TodayCount),
				Difference:        alert.Difference,
			})
		}
	}

	// 创建数据结构
	data := APIAlertData{
		Date:   date,
		Alerts: alertItems,
	}

	// 创建临时JSON文件
	tempDir := os.TempDir()
	jsonPath := filepath.Join(tempDir, fmt.Sprintf("api_alert_data_%d.json", date))
	imagePath := filepath.Join(tempDir, fmt.Sprintf("api_alert_table_%d.png", date))

	// 写入JSON数据
	jsonData, err := json.MarshalIndent(data, "", "  ")
	if err != nil {
		return "", fmt.Errorf("序列化JSON数据失败: %v", err)
	}

	err = os.WriteFile(jsonPath, jsonData, 0644)
	if err != nil {
		return "", fmt.Errorf("写入JSON文件失败: %v", err)
	}

	// 调用Python脚本生成图片
	pythonScript := "/generate_api_alert_table.py"
	cmd := exec.Command("python", pythonScript, jsonPath, imagePath)

	output, err := cmd.CombinedOutput()
	if err != nil {
		// 清理临时JSON文件
		os.Remove(jsonPath)
		return "", fmt.Errorf("调用Python脚本失败: %v, 输出: %s", err, string(output))
	}

	// 清理临时JSON文件
	defer os.Remove(jsonPath)

	// 检查图片是否生成成功
	if _, err := os.Stat(imagePath); os.IsNotExist(err) {
		return "", fmt.Errorf("Python脚本未能生成图片文件: %s", imagePath)
	}

	logger.Infof("API告警表格图片生成成功: %s", imagePath)
	return imagePath, nil
}
