package cron

import (
	"strings"
	"time"
)

func getYesterday() time.Time {
	return getAgoDays(1)
}

func getAgoDays(agoDays int) time.Time {
	return time.Now().AddDate(0, 0, -agoDays)
}

func getStartAndEndTime(day time.Time) (startTime, endTime time.Time) {
	start := time.Date(day.Year(), day.Month(), day.Day(), 0, 0, 0, 0, time.Local)
	end := time.Date(day.Year(), day.Month(), day.Day(), 23, 59, 59, 999999, time.Local)
	return start, end
}

func getUTCTime(date time.Time) (startTime, endTime string) {
	layout := "2006-01-02T15:04:05Z"
	start, end := getStartAndEndTime(date)
	return start.UTC().Format(layout), end.UTC().Format(layout)
}

func standardRequestURI(service, api string) string {
	if api[0] != '/' {
		api = "/" + api
	}
	prefix := map[string]string{
		"yoga-service": "/yoga/",
		"h2v5":         "/620/",
	}
	for name, v := range prefix {
		if service != name {
			continue
		}
		if !strings.HasPrefix(api, v) {
			return v + api[1:]
		}
	}
	return api
}
