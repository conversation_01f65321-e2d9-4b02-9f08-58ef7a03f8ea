package cron

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"gitlab.dailyyoga.com.cn/rdc/serverops/database/wework"
	"gitlab.dailyyoga.com.cn/rdc/serverops/service/tapd"
	wework2 "gitlab.dailyyoga.com.cn/rdc/serverops/service/wework"
	"gitlab.dailyyoga.com.cn/rdc/serverops/service/wework/message"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/go-artifact/wework/robot"
)

type mod struct {
	iteration tapd.Iteration
	story     tapd.Story
	task      tapd.Task
}

// DangerTasksSyncDeveloper 有延期风险任务发对应开发 18：14
func DangerTasksSyncDeveloper() error {
	tasks, err := fetchTasks(false)
	if err != nil {
		return err
	}
	if len(tasks) == 0 {
		return errors.New("no tasks")
	}

	developerTasks := make(map[string][]mod)
	// 单独发企业微信应用消息 17：00
	for i := range tasks {
		v := tasks[i]
		if _, ok := developerTasks[v.task.Owner]; !ok {
			developerTasks[v.task.Owner] = make([]mod, 0)
		}
		developerTasks[v.task.Owner] = append(developerTasks[v.task.Owner], v)
	}

	for name, mods := range developerTasks {
		user, err := wework.TbUsers.GetItemByName(wework2.ServeropsAgentID, name)
		if err != nil {
			logger.Error(err)
			continue
		}
		if messages := getMarkdownMessage(mods, false, true); len(messages) > 0 {
			for _, content := range messages {
				sendApplicationMarkdownMessage(user.UserID, content)
			}
		}
	}
	return nil
}

// DangerTasksSyncManager 有延期风险任务发应用消息到管理，21：00
func DangerTasksSyncManager() error {
	tasks, err := fetchTasks(false)
	if err != nil {
		return err
	}
	if len(tasks) == 0 {
		return errors.New("no tasks")
	}
	if messages := getMarkdownMessage(tasks, false, false); len(messages) > 0 {
		toUser := "ZhaoJiaXing"
		for _, content := range messages {
			sendApplicationMarkdownMessage(toUser, content)
		}
	}
	return nil
}

func sendApplicationMarkdownMessage(toUser, content string) {
	resp, err := message.MarkdownMessage(context.TODO(), &message.MarkdownOptions{
		ToUser:   toUser,
		Markdown: message.MarkdownContent{Content: content},
	})
	if err != nil {
		logger.Error(err)
		return
	}
	logger.Infof("send application message _id: %s", resp.MsgID)
}

// DelayedTasksGroupMessage 延期任务发群消息，8：30
func DelayedTasksGroupMessage() error {
	tasks, err := fetchTasks(true)
	if err != nil {
		return err
	}
	if len(tasks) == 0 {
		return errors.New("no tasks")
	}
	if messages := getMarkdownMessage(tasks, true, false); len(messages) > 0 {
		for _, v := range messages {
			msg := &robot.Message{
				RobotURL: "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=02fd3057-8057-4c5d-a07a-6aa546f0ca1d",
				MsgType:  robot.Markdown,
				Markdown: &robot.MarkdownMessage{Content: v},
			}
			robot.Get().AddMessage(msg)
		}
	}
	return nil
}

func fetchTasks(delayed bool) ([]mod, error) {
	var mods = make([]mod, 0)

	currents, err := tapd.GetCurrentIteration()
	if err != nil {
		return nil, err
	}

	for _, v := range currents {
		stories, _ := tapd.GetIterationStory(v.ID)
		for _, vs := range stories {
			// api频率限制，不加会丢失数据
			time.Sleep(time.Second * 1)
			tasks, _ := tapd.GetStoryTasks(v.ID, vs.ID)
			for _, vt := range tasks {
				if vt.Status == "done" || vt.Due == "" {
					continue
				}
				// danger
				if !delayed && time.Now().Format("2006-01-02") == vt.Due {
					mods = append(mods, mod{iteration: v, story: vs, task: vt})
				}
				if delayed {
					dayStart, _ := getStartAndEndTime(time.Now())
					// delayed
					due := tapd.ParseDate(vt.Due)
					if dayStart.Unix() > due {
						vt.DelayedDays = int((dayStart.Unix() - due) / 86400) //nolint
						mods = append(mods, mod{iteration: v, story: vs, task: vt})
					}
				}
			}
		}
	}
	return mods, nil
}

func getMarkdownMessage(arr []mod, needDelayedDays, note bool) []string {
	iterationDelayedTasks := make(map[string][]mod)
	for i := range arr {
		v := arr[i]
		if _, ok := iterationDelayedTasks[v.iteration.ID]; !ok {
			iterationDelayedTasks[v.iteration.ID] = make([]mod, 0)
		}
		iterationDelayedTasks[v.iteration.ID] = append(iterationDelayedTasks[v.iteration.ID], v)
	}

	messages := make([]string, 0)
	for _, v := range iterationDelayedTasks {
		markdown := "时间：" + time.Now().Format("2006-01-02")
		markdown += "\n迭代 <font color=\"info\">" + v[0].iteration.Name + "</font> "
		if needDelayedDays {
			markdown += "延期任务："
		} else {
			markdown += "有延期风险的任务："
		}
		for i := range v {
			task := v[i]
			markdown += "\n> " + task.story.Name
			if needDelayedDays {
				markdown += " <font color=\"warning\">" + task.task.Owner +
					"</font>" + "\n"
			} else if !note {
				markdown += " <font color=\"warning\">" + task.task.Owner + "</font>\n"
			}
		}
		if note {
			markdown += "\n\n" + `
> =========================
> 已经完成的任务，请及时<font color="warning">更新状态</font>
> 进度确实有异常的，请及时同步并抽空赶上
`
		}
		messages = append(messages, markdown)
	}
	return messages
}
