{
  "aggs": {
    "0": {
      "terms": {
        "field": "request_uri.keyword",
        "order": {
          "_count": "desc"
        },
        "size": 999
      }
    }
  },
  "size": 0,
  "fields": [
    {
      "field": "time",
      "format": "date_time"
    }
  ],
  "script_fields": {},
  "stored_fields": [
    "*"
  ],
  "runtime_mappings": {},
  "_source": {
    "excludes": []
  },
  "query": {
    "bool": {
      "must": [],
      "filter": [
        {
          "bool": {
            "filter": [
              {
                "bool": {
                  "should": [
                    {
                      "match_phrase": {
                        "cluster": "prod"
                      }
                    }
                  ],
                  "minimum_should_match": 1
                }
              },
              {
                "bool": {
                  "should": [
                    {
                      "match_phrase": {
                        "k8s.pod.name": "{{.PodName}}"
                      }
                    }
                  ],
                  "minimum_should_match": 1
                }
              }{{.Condition}}
            ]
          }
        },
        {
          "range": {
            "time": {
              "format": "strict_date_optional_time",
              "gte": "{{.StartTimeUTC}}",
              "lte": "{{.EndTimeUTC}}"
            }
          }
        }
      ],
      "should": [],
      "must_not": [
        {
          "bool": {
            "should": [
              {
                "match_phrase": {
                  "request_method.keyword": "HEAD"
                }
              },
              {
                "match_phrase": {
                  "request_method.keyword": "OPTIONS"
                }
              }
            ],
            "minimum_should_match": 1
          }
        }
      ]
    }
  }
}