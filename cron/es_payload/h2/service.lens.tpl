{"aggs": {"0": {"terms": {"field": "service.keyword", "order": {"_count": "desc"}, "size": 999}}}, "size": 0, "fields": [{"field": "@timestamp", "format": "date_time"}], "script_fields": {}, "stored_fields": ["*"], "runtime_mappings": {}, "_source": {"excludes": []}, "query": {"bool": {"must": [], "filter": [{"bool": {"should": [{"match_phrase": {"cluster": "prod"}}], "minimum_should_match": 1}}, {"range": {"@timestamp": {"format": "strict_date_optional_time", "gte": "{{.StartTimeUTC}}", "lte": "{{.EndTimeUTC}}"}}}], "should": [], "must_not": []}}}