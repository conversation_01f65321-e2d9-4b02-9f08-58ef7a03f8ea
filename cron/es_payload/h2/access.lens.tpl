{
  "aggs": {
    "0": {
      "terms": {
        "field": "request_uri.keyword",
        "order": {
          "_count": "desc"
        },
        "size": 1000
      }
    }
  },
  "size": 0,
  "fields": [
    {
      "field": "@timestamp",
      "format": "date_time"
    }
  ],
  "script_fields": {},
  "stored_fields": [
    "*"
  ],
  "runtime_mappings": {},
  "_source": {
    "excludes": []
  },
  "query": {
    "bool": {
      "must": [],
      "filter": [
        {
          "bool": {
            "filter": [
              {
                "bool": {
                  "should": [
                    {
                      "match_phrase": {
                        "cluster": "prod"
                      }
                    }
                  ],
                  "minimum_should_match": 1
                }
              },
              {
                "bool": {
                  "should": [
                    {
                      "match_phrase": {
                        "service": "{{.Service}}"
                      }
                    }
                  ],
                  "minimum_should_match": 1
                }
              }{{.Condition}}
            ]
          }
        },
        {
          "range": {
            "@timestamp": {
              "format": "strict_date_optional_time",
              "gte": "{{.StartTimeUTC}}",
              "lte": "{{.EndTimeUTC}}"
            }
          }
        }
      ],
      "should": [],
      "must_not": [
        {
          "bool": {
            "should": [
              {
                "match_phrase": {
                  "request_method.keyword": "HEAD"
                }
              },
              {
                "match_phrase": {
                  "request_method.keyword": "OPTIONS"
                }
              }
            ],
            "minimum_should_match": 1
          }
        }
      ]
    }
  }
}