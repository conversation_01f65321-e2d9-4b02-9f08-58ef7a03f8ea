package cron

import (
	"fmt"

	"github.com/alibabacloud-go/tea/tea"
	"gitlab.dailyyoga.com.cn/rdc/serverops/service/bill"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/go-artifact/wework/robot"
)

const RenewalConsoleURL = "(https://usercenter2.aliyun.com/renew/manual?expiresIn=30&commodityCode=)"

func SendBillMsg() error {
	client, err := bill.CreateClient(tea.String("LTAI5tPnE74NmEo21Mqvrz5K"), tea.String("******************************"))
	if err != nil {
		logger.Error(err)
		return err
	}

	AccountBalance, err := bill.AliyunCheckBalance(client)
	if err != nil {
		logger.Error(err)
		return err
	}

	DailyBill, err := bill.AliyunGetBill(client)
	if err != nil {
		logger.Error(err)
		return err
	}

	PendingRenewalCount, err := bill.CheckPendingRenewal(client)
	if err != nil {
		logger.Error(err)
		return err
	}

	if PendingRenewalCount != "0" {
		PendingRenewalCount = "[" + PendingRenewalCount + "]" + RenewalConsoleURL
	}

	v := fmt.Sprintf("<font color=\"info\">阿里云账户情况</font>\n"+
		"\n>昨日消耗:<font color=\"comment\"> %s </font>\n"+
		"\n>当前余额:<font color=\"comment\"> %s </font>\n"+
		"\n>30天内到期资源数:<font color=\"comment\"> %s </font>\n",
		DailyBill, AccountBalance, PendingRenewalCount)

	msg := &robot.Message{
		RobotURL: "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=bb198991-8a77-4c97-9531-33af567a08e1",
		MsgType:  robot.Markdown,
		Markdown: &robot.MarkdownMessage{Content: v},
	}
	robot.Get().AddMessage(msg)
	return nil
}
