package cron

import (
	"bytes"
	_ "embed" // embed
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"strconv"
	"strings"
	"text/template"
	"time"

	"github.com/elastic/go-elasticsearch/v8"
	"github.com/pkg/errors"
	"gitlab.dailyyoga.com.cn/rdc/serverops/config"
	"gitlab.dailyyoga.com.cn/rdc/serverops/database/indicator"
	"gitlab.dailyyoga.com.cn/rdc/serverops/library"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
)

//go:embed es_payload/h2/access.lens.tpl
var searchPayload []byte

//go:embed es_payload/h2/service.lens.tpl
var servicePayload []byte

type searchResponse struct {
	Took         int64                  `json:"took"`
	TimedOut     bool                   `json:"timed_out"`
	Hits         hits                   `json:"hits"`
	Aggregations map[string]aggregation `json:"aggregations"`
}

type hits struct {
	Total    interface{}   `json:"total"`
	MaxScore interface{}   `json:"max_score"`
	Hits     []interface{} `json:"hits"`
}

type aggregation struct {
	DocCountErrorUpperBound int64 `json:"doc_count_error_upper_bound"`
	SumOtherDocCount        int64 `json:"sum_other_doc_count"`
	Buckets                 []struct {
		Key      string `json:"key"`
		DocCount int64  `json:"doc_count"`
	} `json:"buckets"`
}

type elasticsearchPayloadInfo struct {
	Service      string
	PodName      string
	Condition    string
	StartTimeUTC string
	EndTimeUTC   string
}

func StatAccess() error {
	conf := config.Get().ELK
	es, err := elasticsearch.NewClient(elasticsearch.Config{
		Addresses: conf.ServerAddress,
		APIKey:    conf.APIKey,
	})
	if err != nil {
		return err
	}

	excludeServices := make(map[string]int)
	for _, v := range conf.ExcludeService {
		if _, ok := excludeServices[v]; ok {
			continue
		}
		excludeServices[v] = 1
	}

	// yesterday
	date := getYesterday()
	logger.Infof("cron stat access_log date: %s", date.Format("2006-01-02"))

	services := fetchServices(es, date)
	for recordType, condition := range fetchRecordTypeCondition() {
		for _, service := range services {
			if _, ok := excludeServices[service]; ok {
				continue
			}
			if err := fetchSave(es, recordType, service, condition, date); err != nil {
				logger.Error(err)
			}
		}
	}
	// summary
	SummaryAccess(date)
	return nil
}

// fetchServices from elk
func fetchServices(es *elasticsearch.Client, date time.Time) []string {
	payload := getServicesPayload(date)
	if payload == nil {
		return nil
	}
	aggregations, err := fetchAggregations(es, payload)
	if err != nil {
		logger.Error(err)
		return nil
	}
	services := make([]string, 0)
	for _, data := range aggregations {
		for _, item := range data.Buckets {
			if item.Key == "" {
				continue
			}
			services = append(services, item.Key)
		}
	}
	return services
}

func fetchSave(es *elasticsearch.Client, recordType library.SlowApiStatRecordType,
	service, condition string, date time.Time) error {
	logger.Infof("service: %s, record_type: %s", service, recordType.String())
	recordTypeName := recordType.String()
	dateIndex, err := strconv.Atoi(date.Format("20060102"))
	if err != nil {
		return err
	}
	payload := getSearchPayload(service, condition, date)
	if payload == nil {
		return errors.Errorf("search payload is nil")
	}
	aggregations, err := fetchAggregations(es, payload)
	if err != nil {
		return err
	}
	for _, data := range aggregations {
		for _, item := range data.Buckets {
			if item.Key == "" || item.DocCount == 0 {
				continue
			}
			api := standardRequestURI(service, item.Key)
			record := &indicator.ApiStatRecord{
				Platform: "h2", SysFrom: "elk", Date: dateIndex,
				RecordType: recordType, Service: service,
				Name: api, Count: item.DocCount,
			}
			if err := setRecord(record); err != nil {
				logger.Error(err)
				continue
			}
			logger.Infof("%d<%s>  %s -> %d", dateIndex, recordTypeName, api, item.DocCount)
		}
	}
	return nil
}

func fetchAggregations(es *elasticsearch.Client, payload io.Reader) (map[string]aggregation, error) {
	if es == nil {
		return nil, errors.New("es client is nil")
	}
	res, err := es.Search(
		es.Search.WithIndex("*-nginx-access-*"),
		es.Search.WithBody(payload))
	if err != nil {
		return nil, err
	}
	defer func() {
		_ = res.Body.Close()
	}()
	if res.StatusCode != http.StatusOK {
		return nil, errors.Errorf("response status_code: %d", res.StatusCode)
	}
	body, err := ioutil.ReadAll(res.Body)
	if err != nil {
		return nil, err
	}
	ret := &searchResponse{}
	if err := json.Unmarshal(body, ret); err != nil {
		return nil, err
	}
	return ret.Aggregations, nil
}

func setRecord(record *indicator.ApiStatRecord) error {
	item, err := indicator.TbApiStatRecord.GetUniqueRecord(record.Date, record.Name, record.RecordType, record.Platform)
	if err == nil && item != nil {
		item.SysFrom = record.SysFrom
		item.Service = record.Service
		item.Platform = record.Platform

		item.Count += record.Count // 加上之前统计到的数据
		return item.Update()
	}
	return record.Save()
}

func getServicesPayload(date time.Time) io.Reader {
	startTimeUTC, endTimeUTC := getUTCTime(date)
	data := &elasticsearchPayloadInfo{
		StartTimeUTC: startTimeUTC,
		EndTimeUTC:   endTimeUTC,
	}
	return extraPayload(data, string(servicePayload))
}

func getSearchPayload(service, condition string, date time.Time) io.Reader {
	startTimeUTC, endTimeUTC := getUTCTime(date)
	data := &elasticsearchPayloadInfo{
		Service:      service,
		Condition:    condition,
		StartTimeUTC: startTimeUTC,
		EndTimeUTC:   endTimeUTC,
	}
	return extraPayload(data, string(searchPayload))
}

func extraPayload(data *elasticsearchPayloadInfo, text string) io.Reader {
	tpl, err := template.New("es_payload").Parse(text)
	if err != nil {
		logger.Error(err)
		return nil
	}
	var buf bytes.Buffer
	if err := tpl.Execute(&buf, data); err != nil {
		logger.Error(err)
		return nil
	}
	replacer := strings.NewReplacer("\n", "", " ", "")
	return strings.NewReader(replacer.Replace(buf.String()))
}

func fetchRecordTypeCondition() map[library.SlowApiStatRecordType]string {
	cond := `,{"bool":{"should":[{"range":{"upstream_response_time":{"gte":"%s"}}}],"minimum_should_match":1}}`
	return map[library.SlowApiStatRecordType]string{
		library.SlowApiStatTotal: "",
		library.SlowApiStatGT300: fmt.Sprintf(cond, "0.3"),
		library.SlowApiStatGT500: fmt.Sprintf(cond, "0.5"),
	}
}
