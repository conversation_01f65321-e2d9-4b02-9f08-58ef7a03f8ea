package cron

import (
	"context"

	"github.com/pkg/errors"
	"gitlab.dailyyoga.com.cn/gokit/cron"
	"gitlab.dailyyoga.com.cn/rdc/serverops/config"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/go-artifact/microsvr"
)

type cronLogger struct{}

func (c *cronLogger) Infof(format string, args ...interface{}) {
	logger.Infof(format, args...)
}

func Init(cfg *config.Conf) error {
	options := []cron.Option{
		cron.WithService(cfg.GetServiceName(), cfg.GetEnv().String()),
		cron.WithLogger(&cronLogger{}),
	}

	if endpoints := cfg.GetEtcdEndpoints(); len(endpoints) == 0 {
		return errors.New("etcd endpoints is empty")
	} else {
		options = append(options, cron.WithEndpoints(endpoints...))
	}

	if task := buildTask(); task != nil {
		options = append(options, cron.WithTask(task))
	}

	microsvr.RegisterStop(func(micro *microsvr.MicroSvr) {
		cron.Stop()
	})

	return cron.Initial(context.Background(), options...)
}

func buildTask() *cron.Task {
	task := cron.NewTask()
	task.Handle("sync_wework_users", "@every 10m", SyncWeworkAgentUsers)
	// access_log stat from ELK
	task.Handle("stat_access_log", "0 8 * * *", StatAccess)
	task.Handle("stat_access_log_h2o", "0 7 * * *", StatAccessH2O)

	// tapd
	task.Handle("DelayedTasksGroupMessage", "30 8 * * *", DelayedTasksGroupMessage)
	task.Handle("DangerTasksSyncManager", "00 21 * * *", DangerTasksSyncManager)
	task.Handle("DangerTasksSyncDeveloper", "14 18 * * *", DangerTasksSyncDeveloper)

	// aliyun bill
	task.Handle("sensor_watch", "45 15 * * *", SensorAPI) // 神策表盘

	task.Handle("sensor_watch", "50 9 * * *", APIMonitor) // 慢响应播报
	return task
}
