package middlewares

import (
	"net/http"

	"github.com/julienschmidt/httprouter"
	"gitlab.dailyyoga.com.cn/rdc/serverops/service/gitlab"
	"gitlab.dailyyoga.com.cn/server/go-artifact/http/middleware"
)

func verifyGitlabToken(fn httprouter.Handle, _ interface{}) httprouter.Handle {
	return func(w http.ResponseWriter, r *http.Request, params httprouter.Params) {
		token := r.Header.Get("x-gitlab-token")
		if token != gitlab.XGitlabToken {
			w.WriteHeader(http.StatusUnauthorized)
			return
		}
		fn(w, r, params)
	}
}

var VerifyGitlabToken = middleware.Before{
	Fn: verifyGitlabToken,
}
