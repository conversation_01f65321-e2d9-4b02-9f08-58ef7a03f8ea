package middlewares

var whiteURIs = []string{
	"/login",
	"/gitlab/hook/handler",
	"/wework/callback",
	"/android/signed/conf",
	"/apk/release/lists",
	"/apk/release/file-download-url",
	"/ipa/:id/mainifest.plist",
	"/ipanew/:id/mainifest.plist",
	"/package/list",
	"/tapd/tasks",
	"/tapd/story",
	"/tapd/stories",
	"/tapd/iteration",
	"/tapd/owner",
	"/tapd/developer_sign",
	"/job/set",
	"/tapd-h2o/tasks",
	"/tapd-h2o/owner",
	"/tapd-h2o/developer_sign",
	"/app/version",
	"/config/key/figma-mcp-token",
	"/oceanengine/oauth/callback",
	"/package/new",
	"/package/bucket/list",
	"/wework/auth/userinfo",
	"/wework/user/infos",
	"/release/pipeline/item",
	"/release/pipeline/confirm",
	"/release/pipeline/terminate",
	"/release/pipeline/start",
	"/release/pipeline/complete_test",
	"/common/qiniu/uptoken",
	"/app/creator",
}
