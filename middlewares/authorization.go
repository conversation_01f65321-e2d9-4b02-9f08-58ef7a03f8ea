package middlewares

import (
	"net/http"

	"github.com/julienschmidt/httprouter"
	"gitlab.dailyyoga.com.cn/rdc/serverops/database/admin"
	"gitlab.dailyyoga.com.cn/server/go-artifact/artifact"
	"gitlab.dailyyoga.com.cn/server/go-artifact/ctx"
	"gitlab.dailyyoga.com.cn/server/go-artifact/http/middleware"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
)

const AdminUIDKey ctx.Key = "admin_uid"

func authorization(fn httprouter.Handle, mwparams interface{}) httprouter.Handle {
	return func(w http.ResponseWriter, r *http.Request, params httprouter.Params) {
		uid, ok := r.Context().Value(AdminUIDKey).(int)
		if !ok {
			returnStatusCode(w, r, http.StatusUnauthorized)
			logger.Errorf("invalid token, not found uid")
			return
		}
		// fetch role_id and operation
		user := admin.TbUser.GetItemByUID(uid)
		if user == nil {
			returnStatusCode(w, r, http.StatusUnauthorized)
			logger.Errorf("user not found, id: %d", uid)
			return
		}

		if mwparams == nil {
			returnStatusCode(w, r, http.StatusUnauthorized)
			logger.Errorf("mwparams is nil")
			return
		}
		roles, ok := mwparams.([]string)
		if !ok {
			returnStatusCode(w, r, http.StatusInternalServerError)
			return
		}
		if artifact.InArray(user.RoleID, roles) {
			fn(w, r, params)
			return
		}
		returnStatusCode(w, r, http.StatusForbidden)
	}
}

func Authorization(roles []string) middleware.Before {
	return middleware.Before{
		Fn: authorization, Params: roles,
	}
}
