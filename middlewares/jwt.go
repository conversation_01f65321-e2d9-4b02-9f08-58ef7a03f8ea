package middlewares

import (
	"context"
	"net/http"
	"time"

	"github.com/julienschmidt/httprouter"
	"github.com/pkg/errors"
	"gitlab.dailyyoga.com.cn/rdc/serverops/service/jwt"
	"gitlab.dailyyoga.com.cn/server/go-artifact/http/middleware"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
)

func verifyJwtToken(fn httprouter.Handle, _ interface{}) httprouter.Handle {
	return func(rw http.ResponseWriter, r *http.Request, params httprouter.Params) {
		for _, uri := range whiteURIs {
			if r.URL.Path == uri {
				fn(rw, r, params)
				return
			}
		}

		token := r.Header.Get("Authorization")

		var validFns = []func() error{
			func() error {
				if token == "" {
					return errors.New("invalid token, err: empty token")
				}
				return nil
			},
			func() error {
				claims, err := jwt.ParseToken(token)
				if err != nil {
					return errors.Errorf("invalid token, err: %v", err)
				}
				if claims.ExpiresAt.Unix() < time.Now().Unix() {
					return errors.New("invalid token, err: expire token, refresh it")
				}
				r = r.WithContext(context.WithValue(r.Context(), AdminUIDKey, claims.ID))
				return nil
			},
		}

		for _, fnv := range validFns {
			if err := fnv(); err != nil {
				returnStatusCode(rw, r, http.StatusUnauthorized)
				logger.Errorf("uri: %s, err: %v", r.URL.Path, err)
				return
			}
		}

		fn(rw, r, params)
	}
}

var VerifyJwtToken = middleware.Before{
	Fn: verifyJwtToken,
}
