stages:
  - build
  - images
  - deploy

before_script:
  - git version
  - gvm use go1.20.3
  - go env -w GOPRIVATE=*.dailyyoga.com.cn
  - go version
  - go env -w GO111MODULE=on
  - go env -w GOPROXY="https://goproxy.cn,direct"

build:
  stage: build
  tags:
    - rdc-runner
  script:
    - CGO_ENABLE=0 go build -a -tags netgo -ldflags '-extldflags=-v -s -w' -o app
  allow_failure: false
  only:
    refs:
      - master
  artifacts:
    name: "app"
    untracked: true
    expire_in: 10 mins
    paths:
      - app

build-images:
  stage: images
  tags:
    - rdc-runner
  script:
    - /usr/local/bin/yogactl --version
    - /usr/local/bin/yogactl push-acr --service=serverops-api --image-tag=v$CI_COMMIT_SHORT_SHA --no-cache
  allow_failure: false
  only:
    refs:
      - master

deploy-via-helm:
  stage: deploy
  tags:
    - rdc-runner
  script:
    - /usr/local/bin/yogactl --version
    - /usr/local/bin/yogactl helm-update --field-values=.deployment.image.tag=v$CI_COMMIT_SHORT_SHA --app-name=serverops-api --branch=$CI_COMMIT_REF_NAME
  allow_failure: false
  only:
    refs:
      - master
