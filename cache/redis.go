package cache

import (
	"gitlab.dailyyoga.com.cn/server/go-artifact/config"
	"gitlab.dailyyoga.com.cn/server/go-artifact/redisv2"
)

var yogaNewRedis *redisv2.Client

func initYogaRedis01(rd *config.Redis) error {
	var err error
	yogaNewRedis, err = redisv2.NewClient(rd.Address, rd.Password)
	if err != nil {
		return err
	}
	return nil
}

func GetYogaNewRedis() *redisv2.Client {
	return yogaNewRedis
}
