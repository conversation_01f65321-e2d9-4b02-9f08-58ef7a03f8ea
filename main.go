package main

import (
	"log"

	"gitlab.dailyyoga.com.cn/rdc/serverops/cache"
	"gitlab.dailyyoga.com.cn/rdc/serverops/config"
	"gitlab.dailyyoga.com.cn/rdc/serverops/controller"
	"gitlab.dailyyoga.com.cn/rdc/serverops/cron"
	"gitlab.dailyyoga.com.cn/rdc/serverops/database"
	"gitlab.dailyyoga.com.cn/rdc/serverops/middlewares"
	"gitlab.dailyyoga.com.cn/rdc/serverops/routers"
	"gitlab.dailyyoga.com.cn/rdc/serverops/service/ali"
	"gitlab.dailyyoga.com.cn/rdc/serverops/service/cronmanager"
	"gitlab.dailyyoga.com.cn/rdc/serverops/service/gitlab"
	"gitlab.dailyyoga.com.cn/rdc/serverops/service/jenkins"
	"gitlab.dailyyoga.com.cn/rdc/serverops/service/k8s"
	"gitlab.dailyyoga.com.cn/rdc/serverops/service/releasepipeline"
	conf "gitlab.dailyyoga.com.cn/server/go-artifact/config"
	"gitlab.dailyyoga.com.cn/server/go-artifact/env"
	"gitlab.dailyyoga.com.cn/server/go-artifact/http"
	"gitlab.dailyyoga.com.cn/server/go-artifact/microsvr"
	"gitlab.dailyyoga.com.cn/server/go-artifact/wework/robot"
)

func main() {
	cfg := config.Get()

	configKeys := []string{
		"common/redis", "common/etcd", "db/serverops", "db/service_manager",
	}
	resources := []func(option *conf.Option) *conf.Option{
		conf.WithKeys(configKeys),
	}
	conf.Init(cfg, "serverops", resources...)

	if err := microsvr.Init(cfg); err != nil {
		log.Fatal(err)
	}
	if err := database.Init(cfg); err != nil {
		log.Fatal(err)
	}
	if err := cache.Init(cfg); err != nil {
		log.Fatal(err)
	}
	robot.Init()
	k8s.InitClients()
	gitlab.Init()
	jenkins.Init()
	releasepipeline.Init()

	if cfg.GetEnv() == env.Product {
		if err := cronmanager.Init(cronmanager.WithEndpoints(cfg.GetEtcdEndpoints()...)); err != nil {
			log.Fatal(err)
		}
		if err := cron.Init(cfg); err != nil {
			log.Fatal(err)
		}
	}

	// http server
	opts := []http.Option{
		http.WithResponseStruct(&controller.Response{}),
	}
	httpServer := http.NewHTTPServer(opts...)
	httpServer.UseMiddleware(middlewares.VerifyJwtToken)
	routers.Initial(httpServer)
	if err := httpServer.ListenAndServe(cfg.ListenAddress); err != nil {
		log.Fatal(err)
	}
	ali.RunRDSSlowLogCollector()

	microsvr.Run()
}
