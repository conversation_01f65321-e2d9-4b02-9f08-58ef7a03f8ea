package gitlab

import (
	"os"

	"github.com/xanzy/go-gitlab"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
)

var apiClient *gitlab.Client

func GetApiClient() *gitlab.Client {
	return apiClient
}

func Init() {
	var err error
	token := os.Getenv("GITLAB_TOKEN")
	if token == "" {
		token = "**************************"
	}
	apiClient, err = gitlab.NewClient(token,
		gitlab.WithBaseURL("https://gitlab.dailyyoga.com.cn/api/v4"))
	if err != nil {
		logger.Error(err)
	}
}
