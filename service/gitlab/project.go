package gitlab

import (
	"strings"

	"github.com/xanzy/go-gitlab"
)

type project struct{}

var Project project

func (p *project) UpdateMergeRequestCheck(project int) error {
	enabled := true
	opt := &gitlab.EditProjectOptions{
		OnlyAllowMergeIfAllDiscussionsAreResolved: &enabled,
	}
	if _, _, err := apiClient.Projects.EditProject(project, opt); err != nil {
		return err
	}
	return nil
}

func (p *project) SetProtectedBranch(project int, branch string) error {
	opt := &gitlab.ListProtectedBranchesOptions{}
	branches, _, err := apiClient.ProtectedBranches.ListProtectedBranches(project, opt)
	if err != nil {
		return err
	}

	// check exist
	var protectedBranch *gitlab.ProtectedBranch
	prefix := branch
	if strings.Contains(branch, "/") {
		prefix = strings.Split(branch, "/")[0]
	}

	for _, v := range branches {
		if strings.HasPrefix(v.Name, prefix) {
			protectedBranch = v
			break
		}
	}

	// check level and set
	if protectedBranch != nil && !p.checkProtectedBranchAccessLevelInvalid(protectedBranch) {
		if err := p.unprotectBranch(project, protectedBranch); err != nil {
			return err
		}
	}
	return p.setProtectedBranch(project, branch)
}

func (p *project) isMaster(branch string) bool {
	return branch == "master"
}

func (p *project) setProtectedBranch(project int, branch string) error {
	pushAccessLevel := gitlab.NoPermissions
	mergeAccessLevel := gitlab.DeveloperPermissions
	if p.isMaster(branch) {
		mergeAccessLevel = gitlab.MaintainerPermissions
	}
	unprotectAccessLevel := gitlab.OwnerPermissions

	opt := &gitlab.ProtectRepositoryBranchesOptions{
		Name:                 &branch,
		PushAccessLevel:      &pushAccessLevel,
		MergeAccessLevel:     &mergeAccessLevel,
		UnprotectAccessLevel: &unprotectAccessLevel,
	}
	if _, _, err := apiClient.ProtectedBranches.ProtectRepositoryBranches(project, opt); err != nil {
		return err
	}
	return nil
}

func (p *project) checkProtectedBranchAccessLevelInvalid(protectedBranch *gitlab.ProtectedBranch) bool {
	if protectedBranch == nil {
		return false
	}
	if len(protectedBranch.PushAccessLevels) == 0 ||
		len(protectedBranch.MergeAccessLevels) == 0 ||
		len(protectedBranch.UnprotectAccessLevels) == 0 {
		return false
	}

	for _, v := range protectedBranch.PushAccessLevels {
		if v.AccessLevel != gitlab.NoPermissions {
			return false
		}
	}
	for _, v := range protectedBranch.MergeAccessLevels {
		if p.isMaster(protectedBranch.Name) {
			if v.AccessLevel != gitlab.MaintainerPermissions {
				return false
			}
		} else {
			if v.AccessLevel < gitlab.DeveloperPermissions {
				return false
			}
		}
	}
	for _, v := range protectedBranch.UnprotectAccessLevels {
		if v.AccessLevel != gitlab.OwnerPermissions {
			return false
		}
	}

	return true
}

func (p *project) unprotectBranch(project int, protectedBranch *gitlab.ProtectedBranch) error {
	if protectedBranch == nil {
		return nil
	}
	if _, err := apiClient.ProtectedBranches.UnprotectRepositoryBranches(project, protectedBranch.Name); err != nil {
		return err
	}
	return nil
}

func (p *project) CreateBranch(project int, branch, ref string) error {
	opt := &gitlab.CreateBranchOptions{
		Branch: gitlab.Ptr(branch), Ref: gitlab.Ptr(ref),
	}
	if _, _, err := apiClient.Branches.CreateBranch(project, opt); err != nil {
		return err
	}
	return nil
}

func (p *project) Commits(project int, commitOptions *gitlab.CreateCommitOptions) (*gitlab.Commit, error) {
	commit, _, err := apiClient.Commits.CreateCommit(project, commitOptions)
	if err != nil {
		return nil, err
	}
	return commit, nil
}
