package gitlab

import "github.com/xanzy/go-gitlab"

type webhook struct{}

type webhookOptions struct {
	url   *string
	token *string
}

var Webhook webhook

const HookAddress = "https://serveropsapi.dailyyoga.com.cn/gitlab/hook/handler"
const XGitlabToken = "^IE#OQIB92YhNSbQ8QaH%RJwVOAF%HH0" //nolint

func (w *webhook) getOptions() *webhookOptions {
	url := HookAddress
	token := XGitlabToken
	return &webhookOptions{
		url:   &url,
		token: &token,
	}
}

func (w *webhook) CheckAndSave(project int) error {
	opts := &gitlab.ListProjectHooksOptions{}
	hooks, _, err := apiClient.Projects.ListProjectHooks(project, opts)
	if err != nil {
		return err
	}

	for i := range hooks {
		if hooks[i].URL != HookAddress || hooks[i].ProjectID != project {
			continue
		}
		return w.update(hooks[i])
	}
	return w.new(project)
}

func (w *webhook) update(item *gitlab.ProjectHook) error {
	opt := &gitlab.EditProjectHookOptions{}

	enableEvents := true
	if item.PushEvents {
		item.PushEvents = false
	}

	if !item.MergeRequestsEvents {
		opt.MergeRequestsEvents = &enableEvents
	}
	if !item.PipelineEvents {
		opt.PipelineEvents = &enableEvents
	}

	if !item.MergeRequestsEvents || !item.PipelineEvents {
		if _, _, err := apiClient.Projects.EditProjectHook(item.ProjectID, item.ID, opt); err != nil {
			return err
		}
	}
	return nil
}

func (w *webhook) new(project int) error {
	options := w.getOptions()
	enableEvents := true
	disableEvents := false
	opt := &gitlab.AddProjectHookOptions{
		URL:                 options.url,
		Token:               options.token,
		PushEvents:          &disableEvents,
		PipelineEvents:      &enableEvents,
		MergeRequestsEvents: &enableEvents,
	}
	_, _, err := apiClient.Projects.AddProjectHook(project, opt)
	if err != nil {
		return err
	}
	return nil
}
