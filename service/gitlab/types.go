package gitlab

import "github.com/xanzy/go-gitlab"

type SimpleGroupItem struct {
	ID          int                    `json:"id"`
	Name        string                 `json:"name"`
	FullName    string                 `json:"full_name"`
	Description string                 `json:"description"`
	Visibility  gitlab.VisibilityValue `json:"visibility"`
	WebURL      string                 `json:"web_url"`
}

type SimpleProjectItem struct {
	ID                int                    `json:"id"`
	Name              string                 `json:"name"`
	PathWithNamespace string                 `json:"path_with_namespace"`
	Description       string                 `json:"description"`
	DefaultBranch     string                 `json:"default_branch"`
	Visibility        gitlab.VisibilityValue `json:"visibility"`
	WebURL            string                 `json:"web_url"`
}

type SimpleBranchItem struct {
	Name      string `json:"name"`
	Protected bool   `json:"protected"`
}

type ObjectKindName string

const ObjectKindPipeline = "pipeline"
const ObjectKindMergeRequest = "merge_request"

type HookPayload struct {
	ObjectKind ObjectKindName `json:"object_kind"`
}

type MergeRequestPayload struct {
	User             User                         `json:"user"`
	Project          ProjectInfo                  `json:"project"`
	Repository       RepositoryInfo               `json:"repository"`
	ObjectAttributes MergeRequestObjectAttributes `json:"object_attributes"`
	Labels           []LabelItem                  `json:"labels"`
	Changes          interface{}                  `json:"changes"`
}

type RepositoryInfo struct {
	Name        string `json:"name"`
	Description string `json:"description"`
	URL         string `json:"url"`
}

type MergeRequestObjectAttributes struct {
	AssigneeID                int64             `json:"assignee_id"`
	AuthorID                  int64             `json:"author_id"`
	CreatedAt                 string            `json:"created_at"`
	Description               string            `json:"description"`
	HeadPipelineID            int64             `json:"head_pipeline_id"`
	ID                        int64             `json:"id"`
	IID                       int               `json:"iid"`
	LastEditedAt              string            `json:"last_edited_at"`
	LastEditedByID            int64             `json:"last_edited_by_id"`
	MergeCommitSHA            string            `json:"merge_commit_sha"`
	MergeError                string            `json:"merge_error"`
	MergeParams               map[string]string `json:"merge_params"`
	MergeStatus               string            `json:"merge_status"`
	MergeUserID               int64             `json:"merge_user_id"`
	MergeWhenPipelineSucceeds bool              `json:"merge_when_pipeline_succeeds"`
	MilestoneID               int64             `json:"milestone_id"`
	SourceBranch              string            `json:"source_branch"`
	SourceProjectID           int64             `json:"source_project_id"`
	State                     string            `json:"state"`
	TargetBranch              string            `json:"target_branch"`
	TargetBranchID            int64             `json:"target_branch_id"`
	Title                     string            `json:"title"`
	UpdatedAt                 string            `json:"updated_at"`
	Source                    ProjectInfo       `json:"source"`
	Target                    ProjectInfo       `json:"target"`
	LastCommit                Commit            `json:"last_commit"`
	WorkInProgress            bool              `json:"work_in_progress"`
	URL                       string            `json:"url"`
	Action                    string            `json:"action"`
	Assignee                  User              `json:"assignee"`
}

type PipelinePayload struct {
	ObjectAttributes PipelineObjectAttributes `json:"object_attributes"`
	MergeRequest     PipelineMergeRequest     `json:"merge_request"`
	User             User                     `json:"user"`
	Project          ProjectInfo              `json:"project"`
	Commit           Commit                   `json:"commit"`
	Builds           []*BuildItem             `json:"builds"`
}

type PipelineObjectAttributes struct {
	ID         int64      `json:"id"`
	Ref        string     `json:"ref"`
	Sha        string     `json:"sha"`
	BeforeSha  string     `json:"before_sha"`
	Source     string     `json:"source"`
	Status     string     `json:"status"`
	Stages     []string   `json:"stages"`
	CreatedAt  string     `json:"created_at"`
	FinishedAt string     `json:"finished_at"`
	Duration   int        `json:"duration"`
	Variables  []Variable `json:"variables"`
}

type PipelineMergeRequest struct {
	ID              int64  `json:"id"`
	IID             int64  `json:"iid"`
	Title           string `json:"title"`
	SourceBranch    string `json:"source_branch"`
	SourceProjectID int64  `json:"source_project_id"`
	TargetBranch    string `json:"target_branch"`
	TargetProjectID int64  `json:"target_project_id"`
	State           string `json:"state"`
	MergeStatus     string `json:"merge_status"`
	URL             string `json:"url"`
}

type BuildItem struct {
	ID           int64        `json:"id"`
	Stage        string       `json:"stage"`
	Name         string       `json:"name"`
	Status       string       `json:"status"`
	CreatedAt    string       `json:"created_at"`
	StartedAt    string       `json:"started_at"`
	FinishedAt   string       `json:"finished_at"`
	When         string       `json:"when"`
	Manual       bool         `json:"manual"`
	AllowFailure bool         `json:"allow_failure"`
	User         User         `json:"user"`
	Runner       Runner       `json:"runner"`
	ArtifactFile ArtifactFile `json:"artifact_file"`
}

type Runner struct {
	Shared      bool   `json:"is_shared"`
	ID          int64  `json:"id"`
	Active      bool   `json:"active"`
	Description string `json:"description"`
}

type Variable struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}

type ArtifactFile struct {
	FileName string `json:"filename"`
	Size     int64  `json:"size"`
}

type Commit struct {
	ID        string `json:"id"`
	Message   string `json:"message"`
	Timestamp string `json:"timestamp"`
	URL       string `json:"url"`
	Author    User   `json:"author"`
}

type ProjectInfo struct {
	ID          int64  `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description"`
	Namespace   string `json:"namespace"`
}

type User struct {
	Name     string `json:"name"`
	Username string `json:"username"`
	Email    string `json:"email"`
}

type LabelItem struct {
	ID          int64  `json:"id"`
	Title       string `json:"title"`
	Color       string `json:"color"`
	ProjectID   int64  `json:"project_id"`
	CreateAt    string `json:"create_at"`
	UpdatedAt   string `json:"updated_at"`
	Template    bool   `json:"template"`
	Description string `json:"description"`
	Type        string `json:"type"`
	GroupID     int64  `json:"group_id"`
}
