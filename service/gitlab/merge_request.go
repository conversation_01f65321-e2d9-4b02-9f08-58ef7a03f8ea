package gitlab

import (
	"strings"

	"github.com/xanzy/go-gitlab"
)

type mergeRequest struct{}

var MergeRequest mergeRequest

func (mr *mergeRequest) Close(project, iid int) error {
	closeState := "close"
	opt := &gitlab.UpdateMergeRequestOptions{StateEvent: &closeState}
	if _, _, err := apiClient.MergeRequests.UpdateMergeRequest(project, iid, opt); err != nil {
		return err
	}
	return nil
}

func (mr *mergeRequest) Comment(project, iid int, body string) error {
	opt := &gitlab.CreateMergeRequestNoteOptions{Body: &body}
	if _, _, err := apiClient.Notes.CreateMergeRequestNote(project, iid, opt); err != nil {
		return err
	}
	return nil
}

func (mr *mergeRequest) ResolveDiscussion(project, iid int, discussion string) error {
	resolved := true
	opt := &gitlab.ResolveMergeRequestDiscussionOptions{Resolved: &resolved}
	if _, _, err := apiClient.Discussions.ResolveMergeRequestDiscussion(project, iid, discussion, opt); err != nil {
		return err
	}
	return nil
}

func (mr *mergeRequest) CommentDiscussion(project, iid int, discussion, body string) error {
	opt := &gitlab.AddMergeRequestDiscussionNoteOptions{Body: &body}
	if _, _, err := apiClient.Discussions.AddMergeRequestDiscussionNote(project, iid, discussion, opt); err != nil {
		return err
	}
	return nil
}

func (mr *mergeRequest) FindDiscussion(project, iid int, search string) ([]string, error) {
	opt := &gitlab.ListMergeRequestDiscussionsOptions{}
	discussions, _, err := apiClient.Discussions.ListMergeRequestDiscussions(project, iid, opt)
	if err != nil {
		return nil, err
	}

	ids := make([]string, 0)
	for _, item := range discussions {
		for _, val := range item.Notes {
			if strings.Contains(val.Body, search) {
				ids = append(ids, item.ID)
			}
		}
	}
	return ids, nil
}

func (mr *mergeRequest) StartDiscussion(project, iid int, body string) error {
	opt := &gitlab.CreateMergeRequestDiscussionOptions{
		Body: &body,
	}
	if _, _, err := apiClient.Discussions.CreateMergeRequestDiscussion(project, iid, opt); err != nil {
		return err
	}
	return nil
}
