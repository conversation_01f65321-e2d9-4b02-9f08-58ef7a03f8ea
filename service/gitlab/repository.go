package gitlab

import (
	gogitlab "github.com/xanzy/go-gitlab"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
)

type repository struct{}

var Repository repository

func (r *repository) ListsGroupProjects(group int) []*SimpleProjectItem {
	rets := make([]*SimpleProjectItem, 0)
	page := 1
	for {
		opt := &gogitlab.ListGroupProjectsOptions{}
		opt.Page = page
		opt.PerPage = 200
		projects, _, err := apiClient.Groups.ListGroupProjects(group, opt)
		if err != nil {
			logger.Error(err)
			break
		}
		if len(projects) == 0 {
			break
		}
		for _, v := range projects {
			rets = append(rets, &SimpleProjectItem{
				ID: v.ID, Name: v.Name,
				PathWithNamespace: v.PathWithNamespace,
				Description:       v.Description, DefaultBranch: v.Description,
				Visibility: v.Visibility, WebURL: v.WebURL,
			})
		}
		page++
	}
	return rets
}

func (r *repository) GetFileContent(project int, filename, ref string) string {
	opt := &gogitlab.GetFileOptions{}
	opt.Ref = &ref
	file, _, err := apiClient.RepositoryFiles.GetFile(project, filename, opt)
	if err != nil {
		return ""
	}
	return file.Content
}

func (r *repository) Create(options *gogitlab.CreateProjectOptions) (*gogitlab.Project, error) {
	options.Visibility = gogitlab.Ptr(gogitlab.PrivateVisibility)
	projectInfo, _, err := apiClient.Projects.CreateProject(options)
	if err != nil {
		return nil, err
	}
	return projectInfo, nil
}
