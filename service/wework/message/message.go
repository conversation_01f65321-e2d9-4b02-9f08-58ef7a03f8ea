package message

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"

	"gitlab.dailyyoga.com.cn/rdc/serverops/service/wework"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
)

type Response struct {
	ErrCode      int    `json:"errcode"`
	ErrMsg       string `json:"errmsg"`
	InvalidUser  string `json:"invaliduser"`
	InvalidParty string `json:"invalidparty"`
	InvalidTag   string `json:"invalidtag"`
	MsgID        string `json:"msgid"`
	ResponseCode string `json:"response_code"`
}

type Payload interface {
	MarkdownOptions | InteractiveTaskCardOptions | TextCardOptions | TemplateCardOptions
}

func doSendMessage[T Payload](ctx context.Context, data *T) (*Response, error) {
	resp := &Response{}

	payload := bytes.NewBuffer(nil)
	if err := json.NewEncoder(payload).Encode(data); err != nil {
		return resp, err
	}

	req := &wework.DoRequest{
		Method: http.MethodPost, Path: "/message/send", Payload: payload,
	}

	respBody, err := req.Do(ctx)
	if err != nil {
		return resp, err
	}
	logger.Infof("doSendMessage response_body: %s", string(respBody))
	if err := json.NewDecoder(bytes.NewReader(respBody)).Decode(&resp); err != nil {
		return resp, err
	}
	return resp, nil
}

func MarkdownMessage(ctx context.Context, opt *MarkdownOptions) (*Response, error) {
	opt.MsgType = "markdown"
	opt.AgentID = wework.ServeropsAgentID
	return doSendMessage(ctx, opt)
}

func InteractiveTaskCardMessage(ctx context.Context, opt *InteractiveTaskCardOptions) (*Response, error) {
	return doSendMessage(ctx, opt)
}

func TextCardMessage(ctx context.Context, opt *TextCardOptions) (*Response, error) {
	opt.MsgType = "textcard"
	opt.AgentID = wework.ServeropsAgentID
	return doSendMessage(ctx, opt)
}

func TemplateCardTextNoticeMessage(ctx context.Context, opt *TemplateCardOptions) (*Response, error) {
	opt.MsgType = "template_card"
	opt.AgentID = wework.ServeropsAgentID
	opt.TemplateCard.CardType = "text_notice"
	return doSendMessage(ctx, opt)
}
