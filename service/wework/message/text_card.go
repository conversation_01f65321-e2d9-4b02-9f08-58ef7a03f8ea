package message

type TextCardOptions struct {
	ToUser                 string          `json:"touser"`
	MsgType                string          `json:"msgtype"`
	AgentID                int             `json:"agentid"`
	TextCard               TextCardContent `json:"textcard"`
	EnableIDTrans          int             `json:"enable_id_trans,omitempty"`
	EnableDuplicateCheck   int             `json:"enable_duplicate_check,omitempty"`
	DuplicateCheckInterval int             `json:"duplicate_check_interval,omitempty"`
}

type TextCardContent struct {
	Title       string `json:"title"`
	Description string `json:"description"`
	URL         string `json:"url"`
}
