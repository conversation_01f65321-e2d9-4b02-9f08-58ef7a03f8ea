package message

const TemplateCardButtonKeyConfirm = "confirm"
const TemplateCardButtonKeyReject = "reject"

type InteractiveTaskCardOptions struct {
	ToUser              string              `json:"touser"`
	MsgType             string              `json:"msgtype"`
	AgentID             int                 `json:"agentid"`
	InteractiveTaskCard InteractiveTaskCard `json:"interactive_taskcard"`
}

type InteractiveTaskCardButton struct {
	Key    string `json:"key"`
	Name   string `json:"name"`
	Color  string `json:"color,omitempty"`
	IsBold bool   `json:"is_bold,omitempty"`
}

type InteractiveTaskCard struct {
	Title       string                      `json:"title"`
	Description string                      `json:"description"`
	URL         string                      `json:"url,omitempty"`
	TaskID      string                      `json:"task_id"`
	Button      []InteractiveTaskCardButton `json:"btn"`
}
