package message

type TemplateCardOptions struct {
	ToUser                 string               `json:"touser"`
	MsgType                string               `json:"msgtype"`
	AgentID                int                  `json:"agentid"`
	TemplateCard           *TemplateCardContent `json:"template_card"`
	EnableIDTrans          int                  `json:"enable_id_trans,omitempty"`
	EnableDuplicateCheck   int                  `json:"enable_duplicate_check,omitempty"`
	DuplicateCheckInterval int                  `json:"duplicate_check_interval,omitempty"`
}

type TemplateCardContent struct {
	CardType              string                               `json:"card_type"`
	Source                *TemplateCardSource                  `json:"source,omitempty"`
	ActionMenu            *TemplateCardActionMenu              `json:"action_menu,omitempty"`
	TaskID                string                               `json:"task_id,omitempty"`
	MainTitle             *TemplateCardMainTitle               `json:"main_title,omitempty"`
	QuoteArea             *TemplateCardQuoteArea               `json:"quote_area,omitempty"`
	EmphasisContent       *TemplateCardEmphasisContent         `json:"emphasis_content,omitempty"`
	SubTitleText          string                               `json:"sub_title_text,omitempty"`
	HorizontalContentList []*TemplateCardHorizontalContentItem `json:"horizontal_content_list,omitempty"`
	JumpList              []*TemplateCardActionItem            `json:"jump_list,omitempty"`
	CardAction            *TemplateCardActionItem              `json:"card_action"`
}

type TemplateCardMainTitle struct {
	Title string `json:"title"`
	Desc  string `json:"desc"`
}

type TemplateCardSource struct {
	IconURL   string `json:"icon_url,omitempty"`
	Desc      string `json:"desc,omitempty"`
	DescColor int    `json:"desc_color,omitempty"`
}

type TemplateCardActionMenu struct {
	Desc       string                        `json:"desc,omitempty"`
	ActionList []*TemplateCardActionMenuItem `json:"action_list"`
}

type TemplateCardActionMenuItem struct {
	Text string `json:"text"`
	Key  string `json:"key"`
}

type TemplateCardQuoteArea struct {
	Type      int    `json:"type,omitempty"`
	URL       string `json:"url,omitempty"`
	AppID     string `json:"appid,omitempty"`
	PagePath  string `json:"pagepath,omitempty"`
	Title     string `json:"title,omitempty"`
	QuoteText string `json:"quote_text,omitempty"`
}

type TemplateCardEmphasisContent struct {
	Title string `json:"title,omitempty"`
	Desc  string `json:"desc,omitempty"`
}

type TemplateCardHorizontalContentItem struct {
	Type    int    `json:"type,omitempty"`
	KeyName string `json:"keyname"`
	Value   string `json:"value,omitempty"`
	URL     string `json:"url,omitempty"`
	MediaID string `json:"media_id,omitempty"`
	UserID  string `json:"userid,omitempty"`
}

type TemplateCardActionItem struct {
	Type     int    `json:"type,omitempty"`
	Title    string `json:"title,omitempty"`
	URL      string `json:"url,omitempty"`
	AppID    string `json:"appid,omitempty"`
	PagePath string `json:"pagepath,omitempty"`
}
