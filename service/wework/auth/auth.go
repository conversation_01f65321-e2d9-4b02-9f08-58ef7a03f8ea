package auth

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/url"

	"github.com/pkg/errors"
	"gitlab.dailyyoga.com.cn/rdc/serverops/service/wework"
)

type Response struct {
	ErrCode    int    `json:"errcode"`
	ErrMessage string `json:"errmsg"`
	UserID     string `json:"userid"`
	UserTicket string `json:"user_ticket"`
}

func GetUserInfo(ctx context.Context, code string) (*Response, error) {
	req := &wework.DoRequest{
		Method: http.MethodGet,
		Path:   "/auth/getuserinfo",
		Params: url.Values{},
	}
	if code == "" {
		return nil, errors.New("code is empty")
	}
	req.Params.Set("code", code)

	respBody, err := req.Do(ctx)
	if err != nil {
		return nil, err
	}

	resp := &Response{}
	if err := json.NewDecoder(bytes.NewReader(respBody)).Decode(resp); err != nil {
		return nil, err
	}
	return resp, nil
}
