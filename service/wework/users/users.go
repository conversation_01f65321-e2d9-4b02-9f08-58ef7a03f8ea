package users

import (
	"context"
	"encoding/json"
	"net/http"
	"net/url"
	"strconv"

	"gitlab.dailyyoga.com.cn/rdc/serverops/service/wework"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
)

func DepartmentList(ctx context.Context, id int) ([]Department, error) {
	req := &wework.DoRequest{
		Method: http.MethodGet,
		Path:   "/department/list",
		Params: url.Values{},
	}
	if id > 0 {
		req.Params.Set("id", strconv.Itoa(id))
	}

	respBody, err := req.Do(ctx)
	if err != nil {
		return nil, err
	}
	logger.Debugf("request result: %s", string(respBody))

	resp := &DepartmentListResponse{}
	if err := json.Unmarshal(respBody, &resp); err != nil {
		return nil, err
	}
	return resp.Department, nil
}

func UserList(ctx context.Context, departmentID int, fetchChild bool) ([]SimpleUser, error) {
	req := &wework.DoRequest{
		Method: http.MethodGet,
		Path:   "/user/simplelist",
		Params: url.Values{},
	}

	req.Params.Set("department_id", strconv.Itoa(departmentID))
	if fetchChild {
		req.Params.Set("fetch_child", "1")
	}
	respBody, err := req.Do(ctx)
	if err != nil {
		return nil, err
	}
	logger.Debugf("request result: %s", string(respBody))

	resp := &SimpleUserListResponse{}
	if err := json.Unmarshal(respBody, &resp); err != nil {
		return nil, err
	}
	return resp.UserList, nil
}

func UserInfo(ctx context.Context, userID string) (*UserInfoResponse, error) {
	req := &wework.DoRequest{
		Method: http.MethodGet,
		Path:   "/user/get",
		Params: url.Values{},
	}
	if userID != "" {
		req.Params.Set("userid", userID)
	}

	respBody, err := req.Do(ctx)
	if err != nil {
		return nil, err
	}

	resp := &UserInfoResponse{}
	if err := json.Unmarshal(respBody, &resp); err != nil {
		return nil, err
	}
	return resp, nil
}
