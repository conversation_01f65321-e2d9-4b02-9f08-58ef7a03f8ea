package users

type DepartmentListResponse struct {
	ErrCode    int          `json:"errcode"`
	ErrMsg     string       `json:"errmsg"`
	Department []Department `json:"department"`
}

type Department struct {
	ID       int    `json:"id"`
	Name     string `json:"name"`
	NameEn   string `json:"name_en"`
	ParentID int    `json:"parentid"`
	Order    int    `json:"order"`
}

type SimpleUserListResponse struct {
	ErrCode  int          `json:"errcode"`
	ErrMsg   string       `json:"errmsg"`
	UserList []SimpleUser `json:"userlist"`
}

type SimpleUser struct {
	UserID     string `json:"userid"`
	Name       string `json:"name"`
	Department []int  `json:"department"`
	OpenUserID string `json:"open_userid"`
}

type UserInfoResponse struct {
	ErrCode          int    `json:"errcode"`
	ErrMsg           string `json:"errmsg"`
	UserID           string `json:"userid"`
	Name             string `json:"name"`
	Department       []int  `json:"department"`
	Order            []int  `json:"order"`
	Position         string `json:"position"`
	Mobile           string `json:"mobile"`
	Gender           string `json:"gender"`
	Email            string `json:"email"`
	IsLeaderInDept   []int  `json:"is_leader_in_dept"`
	Avatar           string `json:"avatar"`
	ThumbAvatar      string `json:"thumb_avatar"`
	Telephone        string `json:"telephone"`
	Alias            string `json:"alias"`
	Address          string `json:"address"`
	OpenUserID       string `json:"open_user_id"`
	MainDepartment   int    `json:"main_department"`
	Status           int    `json:"status"`
	ExternalPosition string `json:"external_position"`
}
