package agent

import (
	"context"
	"encoding/json"
	"gitlab.dailyyoga.com.cn/rdc/serverops/service/wework"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"net/http"
	"net/url"
	"strconv"
)

type InfoResponse struct {
	ErrCode        int    `json:"errcode"`
	ErrMsg         string `json:"errmsg"`
	AgentID        int    `json:"agentid"`
	Name           string `json:"name"`
	SquareLogoURL  string `json:"square_logo_url"`
	Description    string `json:"description"`
	AllowUserInfos struct {
		User []struct {
			UserID string `json:"userid"`
		} `json:"user"`
	} `json:"allow_userinfos"`
	AllowPartys struct {
		PartyID []int `json:"partyid"`
	} `json:"allow_partys"`
	AllowTags struct {
		TagID []int `json:"tag_id"`
	} `json:"allow_tags"`
	Close           int    `json:"close"`
	RedirectDomain  string `json:"redirect_domain"`
	ReportLocalFlag int    `json:"report_local_flag"`
	IsReportEnter   int    `json:"isreportenter"`
	HomeURL         string `json:"home_url"`
}

func GetInfo(ctx context.Context, id int) (*InfoResponse, error) {
	req := &wework.DoRequest{
		Method: http.MethodGet,
		Path:   "/agent/get",
		Params: url.Values{},
	}
	req.Params.Set("agentid", strconv.Itoa(id))

	respBody, err := req.Do(ctx)
	if err != nil {
		return nil, err
	}
	logger.Debugf("request result: %s", string(respBody))

	resp := &InfoResponse{}
	if err := json.Unmarshal(respBody, &resp); err != nil {
		return nil, err
	}
	return resp, nil
}
