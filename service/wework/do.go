package wework

import (
	"context"
	"io"
	"io/ioutil"
	"net/http"
	"net/url"
	"strings"

	"github.com/pkg/errors"
)

type DoRequest struct {
	Method  string
	Path    string
	Params  url.Values
	Payload io.Reader
}

func (d *DoRequest) Do(ctx context.Context) ([]byte, error) {
	accessToken := getAccessToken()
	if accessToken == "" {
		return nil, errors.New("empty access_token")
	}

	baseURL := "https://qyapi.weixin.qq.com/cgi-bin"
	if !strings.HasPrefix(d.Path, "/") {
		baseURL += "/"
	}
	baseURL += d.Path + "?access_token=" + accessToken

	if len(d.Params) > 0 {
		baseURL += "&" + d.Params.Encode()
	}

	req, err := http.NewRequestWithContext(ctx, d.Method, baseURL, d.Payload)
	if err != nil {
		return nil, err
	}
	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	respBody, _ := ioutil.ReadAll(resp.Body)
	return respBody, nil
}
