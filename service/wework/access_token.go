package wework

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"
	"time"

	"github.com/go-redis/redis/v8"
	"gitlab.dailyyoga.com.cn/rdc/serverops/cache"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
)

type accessTokenPayload struct {
	AccessToken string `json:"access_token"`
	ExpireTime  int64  `json:"expire_time"`
}

func getAccessToken() string {
	token, err := cache.GetYogaNewRedis().Get(context.TODO(), AccessTokenKey).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			if payload := refreshToken(); payload.AccessToken != "" {
				return payload.AccessToken
			}
		}
		logger.Error(err)
		return ""
	}
	return parseToken(token)
}

func parseToken(token string) string {
	payload := &accessTokenPayload{}

	if err := json.Unmarshal([]byte(token), payload); err != nil {
		payload = refreshToken()
	}

	// early 30 mins
	var bufferTime int64 = 1800
	if time.Now().Unix()-payload.ExpireTime >= bufferTime {
		payload = refreshToken()
	}
	return payload.AccessToken
}

func refreshToken() *accessTokenPayload {
	payload := &accessTokenPayload{}
	tokenAddr := fmt.Sprintf("https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=%s&corpsecret=%s",
		CropID, ServeropsSecret)

	type response struct {
		ErrCode     int    `json:"errcode"`
		ErrMsg      string `json:"errmsg"`
		AccessToken string `json:"access_token"`
		ExpireIn    int    `json:"expire_in"`
	}
	resp, err := http.Get(tokenAddr) // nolint
	if err != nil {
		logger.Error(err)
		return payload
	}
	defer resp.Body.Close()

	body, _ := ioutil.ReadAll(resp.Body)
	ret := &response{}
	if err := json.Unmarshal(body, &ret); err != nil {
		logger.Error(err)
		return payload
	}
	// save
	payload.AccessToken = ret.AccessToken
	payload.ExpireTime = time.Now().Unix() + int64(ret.ExpireIn)

	data, _ := json.Marshal(payload)
	cache.GetYogaNewRedis().Set(context.TODO(), AccessTokenKey, string(data), time.Duration(ret.ExpireIn)*time.Second)
	return payload
}
