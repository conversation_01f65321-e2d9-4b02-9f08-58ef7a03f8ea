package process

import (
	"context"
	"encoding/xml"
	"fmt"
	"strconv"
	"time"

	"github.com/pkg/errors"
	"gitlab.dailyyoga.com.cn/rdc/serverops/database/wework"
	"gitlab.dailyyoga.com.cn/rdc/serverops/service/gitlab"
	wework2 "gitlab.dailyyoga.com.cn/rdc/serverops/service/wework"
	"gitlab.dailyyoga.com.cn/rdc/serverops/service/wework/message"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
)

type EventTaskCardClick struct {
	ToUserName   string `xml:"ToUserName"`
	FromUserName string `xml:"FromUserName"`
	MsgType      string `xml:"MsgType"`
	Event        string `xml:"Event"`
	CreateTime   int64  `xml:"CreateTime"`
	EventKey     string `xml:"EventKey"`
	TaskID       string `xml:"TaskId"`
	AgentID      int    `xml:"AgentId"`

	FromName         string `xml:"-"`
	CreateTimeFormat string `xml:"-"`
}

type EventTaskCardUpdateButtonReturns struct {
	XMLName      xml.Name
	ToUserName   string                         `xml:"ToUserName"`
	FromUserName string                         `xml:"FromUserName"`
	CreateTime   int64                          `xml:"CreateTime"`
	MsgType      wework2.XMLCDATA               `xml:"MsgType"`
	TaskCard     EventTaskCardButtonReplaceName `xml:"TaskCard"`
}

type EventTaskCardButtonReplaceName struct {
	ReplaceName wework2.XMLCDATA `xml:"ReplaceName"`
}

func (t *EventTaskCardClick) Process(ctx context.Context) {
	t.FromName = t.FromUserName
	if user, _ := wework.TbUsers.GetItemByUserID(t.AgentID, t.FromUserName); user != nil {
		t.FromName = user.Name
	}
	t.CreateTimeFormat = time.Unix(t.CreateTime, 0).Format("2006-01-02 15:04:05")

	record, err := wework.TbMessageRecord.GetItemByTaskID(t.TaskID)
	if err != nil {
		logger.Error(err)
		return
	}
	if record == nil || record.ID < 0 {
		logger.Error("not found message history record")
		return
	}

	if record.FeedbackTime > 0 || record.FeedbackOp != "" {
		logger.Warn("repeated callback")
		return
	}

	// save callback update
	record.FeedbackTime = t.CreateTime
	record.FeedbackOp = t.EventKey
	record.FeedbackOpUser = t.FromUserName
	if err := record.Update(); err != nil {
		logger.Error(err)
		return
	}
	if err := t.processButtonClick(record); err != nil {
		logger.Error(err)
	}
}

func (t *EventTaskCardClick) Returns(ctx context.Context) (data []byte, timestamp, nonce string, err error) {
	returns := &EventTaskCardUpdateButtonReturns{
		XMLName: xml.Name{Local: "xml"},
		MsgType: wework2.XMLCDATA{Value: "update_taskcard"},
	}
	switch t.EventKey {
	case message.TemplateCardButtonKeyConfirm:
		returns.TaskCard.ReplaceName = wework2.XMLCDATA{Value: "已通过"}
	case message.TemplateCardButtonKeyReject:
		returns.TaskCard.ReplaceName = wework2.XMLCDATA{Value: "已驳回"}
	default:
		err = errors.New("action not support")
		return
	}

	returns.FromUserName = t.ToUserName
	returns.ToUserName = t.FromUserName
	returns.CreateTime = time.Now().Unix()

	timestamp = strconv.FormatInt(returns.CreateTime, 10)
	nonce = timestamp
	data, err = xml.Marshal(returns)
	return
}

func (t *EventTaskCardClick) processButtonClick(record *wework.MessageRecord) error {
	if record.RelatedObject.MappingID == 0 || record.RelatedObject.MergeRequestIID == 0 {
		return errors.Errorf("invalid record, "+
			"not get mapping id or merge request id from related info, record_id: %d", record.ID)
	}
	if record.RelatedMapping == nil {
		return errors.Errorf("invalid record, mapping info not found, record_id: %d", record.ID)
	}

	project := record.RelatedMapping.ProjectID
	iid := record.RelatedObject.MergeRequestIID

	switch t.EventKey {
	case message.TemplateCardButtonKeyConfirm:
		return t.clickConfirm(project, iid)
	case message.TemplateCardButtonKeyReject:
		return t.clickReject(project, iid)
	default:
		return errors.New("action not support")
	}
}

func (t *EventTaskCardClick) clickConfirm(project, iid int) error {
	search := fmt.Sprintf("TaskID:%s", t.TaskID)
	discussions, err := gitlab.MergeRequest.FindDiscussion(project, iid, search)
	if err != nil {
		return err
	}

	comment := fmt.Sprintf(":white_check_mark: 上线请求已于 %s 通过测试，操作人：%s", t.CreateTimeFormat, t.FromName)
	for _, id := range discussions {
		if err := gitlab.MergeRequest.CommentDiscussion(project, iid, id, comment); err != nil {
			return err
		}
		if err := gitlab.MergeRequest.ResolveDiscussion(project, iid, id); err != nil {
			return err
		}
	}
	return nil
}

func (t *EventTaskCardClick) clickReject(project, iid int) error {
	search := fmt.Sprintf("TaskID:%s", t.TaskID)
	discussions, err := gitlab.MergeRequest.FindDiscussion(project, iid, search)
	if err != nil {
		return err
	}

	comment := fmt.Sprintf(":x: 上线请求已于 %s 被驳回，操作人：%s", t.CreateTimeFormat, t.FromName)
	for _, id := range discussions {
		if err := gitlab.MergeRequest.CommentDiscussion(project, iid, id, comment); err != nil {
			return err
		}
	}

	if err := gitlab.MergeRequest.Close(project, iid); err != nil {
		return err
	}
	return nil
}
