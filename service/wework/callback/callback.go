package callback

import (
	"encoding/xml"
	"fmt"

	"github.com/pkg/errors"
	"gitlab.dailyyoga.com.cn/rdc/serverops/service/wework/callback/process"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
)

// MsgType 参考 https://work.weixin.qq.com/api/doc/90000/90135/90239
type MsgType string
type EventType string

// MsgEventTypeResp 获取用户消息类型响应
type MsgEventTypeResp struct {
	MsgType   string `xml:"MsgType"` // 消息类型
	EventType string `xml:"Event"`   // 事件类型
}

// VerifyURLReq 回调验证URL请求
type VerifyURLReq struct {
	MsgSignature string
	Timestamp    string
	Nonce        string
	EchoStr      string
}

// DecryptUserMsgReq 用户消息解密请求
type DecryptUserMsgReq struct {
	MsgSignature string // 签名
	Timestamp    string
	Nonce        string
	Data         []byte // post请求发送的xml密文数据
}

// EncryptUserMsgReq 企业回复消息加密请求
type EncryptUserMsgReq struct {
	Timestamp string
	Nonce     string
	Data      string // 企业待发送的xml明文数据
}

// Manager 回调管理
type Manager struct {
	crypt *WXBizMsgCrypt
}

// NewCallbackManager 返回一个企业微信加解密实例
func NewCallbackManager(receiverID, token, encodingAESKey string) *Manager {
	cpt := NewWXBizMsgCrypt(token, encodingAESKey, receiverID, XMLType)
	return &Manager{crypt: cpt}
}

// CallbackVerifyURL /*
// CallbackVerifyURL 企业微信验证回调URL
// 参考 https://work.weixin.qq.com/api/doc/90000/90135/90930
func (m *Manager) CallbackVerifyURL(req VerifyURLReq) (string, error) {
	echoStr, cryptErr := m.crypt.verifyURL(req.MsgSignature, req.Timestamp, req.Nonce, req.EchoStr)
	if cryptErr != nil {
		err := fmt.Errorf("CallbackVerifyURL error : errcode=%v , errormsg=%v", cryptErr.ErrCode, cryptErr.ErrMsg)
		return "", err
	}
	return string(echoStr), nil
}

func (m *Manager) DecryptMsg(req DecryptUserMsgReq) (MessageProcessor, error) {
	var typesInfo MsgEventTypeResp
	message, cryptErr := m.crypt.decryptMsg(req.MsgSignature, req.Timestamp, req.Nonce, req.Data)
	if cryptErr != nil {
		return nil, errors.Errorf("decrypt error: errcode=%v, errmsg=%v", cryptErr.ErrCode, cryptErr.ErrMsg)
	}
	if err := xml.Unmarshal(message, &typesInfo); err != nil {
		return nil, err
	}

	logger.Infof("event payload: %s", string(message))

	var content MessageProcessor
	switch typesInfo.MsgType {
	case "event":
		switch typesInfo.EventType {
		case "taskcard_click":
			content = &process.EventTaskCardClick{}
		default:
			return nil, errors.New("event not support")
		}
	default:
		return nil, errors.New("msg_type not support")
	}

	err := xml.Unmarshal(message, &content)
	if err != nil {
		return nil, err
	}
	return content, nil
}

// EncryptUserMsg 企业回复的消息加密
func (m *Manager) EncryptUserMsg(req *EncryptUserMsgReq) ([]byte, error) {
	encryptMsg, cryptErr := m.crypt.encryptMsg(req.Data, req.Timestamp, req.Nonce)
	if cryptErr != nil {
		err := fmt.Errorf("EncryptUserMsg error : errcode=%v , errormsg=%v", cryptErr.ErrCode, cryptErr.ErrMsg)
		return nil, err
	}

	return encryptMsg, nil
}
