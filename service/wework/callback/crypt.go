package callback

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/sha1" // nolint
	"encoding/base64"
	"encoding/binary"
	"encoding/xml"
	"fmt"
	"math/rand"
	"sort"
	"strings"

	"gitlab.dailyyoga.com.cn/rdc/serverops/service/wework"
)

// 复制企业微信https://open.work.weixin.qq.com/api/doc/90000/90138/90307#golang%E5%BA%93
const letterBytes = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"

const (
	ValidateSignatureError int = -40001
	ParseXMLError          int = -40002
	ComputeSignatureError  int = -40003
	IllegalAesKey          int = -40004
	ValidateCorpidError    int = -40005
	EncryptAESError        int = -40006
	DecryptAESError        int = -40007
	IllegalBuffer          int = -40008
	EncodeBase64Error      int = -40009
	DecodeBase64Error      int = -40010
	GenXMLError            int = -40010
	ParseJSONError         int = -40012
	GenJSONError           int = -40013
	IllegalProtocolType    int = -40014
)

type ProtocolType int

const (
	XMLType ProtocolType = 1
)

type CryptError struct {
	ErrCode int
	ErrMsg  string
}

func NewCryptError(errCode int, errMsg string) *CryptError {
	return &CryptError{ErrCode: errCode, ErrMsg: errMsg}
}

type WXBizMsg4Recv struct {
	ToUserName string `xml:"ToUserName"`
	Encrypt    string `xml:"Encrypt"`
	AgentID    string `xml:"AgentID"`
}

type WXBizMsg4Send struct {
	XMLName   xml.Name        `xml:"xml"`
	Encrypt   wework.XMLCDATA `xml:"Encrypt"`
	Signature wework.XMLCDATA `xml:"MsgSignature"`
	Timestamp string          `xml:"TimeStamp"`
	Nonce     wework.XMLCDATA `xml:"Nonce"`
}

func NewWXBizMsg4Send(encrypt, signature, timestamp, nonce string) *WXBizMsg4Send {
	return &WXBizMsg4Send{
		Encrypt:   wework.XMLCDATA{Value: encrypt},
		Signature: wework.XMLCDATA{Value: signature},
		Timestamp: timestamp,
		Nonce:     wework.XMLCDATA{Value: nonce},
	}
}

type ProtocolProcessor interface {
	parse(srcData []byte) (*WXBizMsg4Recv, *CryptError)
	serialize(msgSend *WXBizMsg4Send) ([]byte, *CryptError)
}

type WXBizMsgCrypt struct {
	token             string
	encodingAESKey    string
	receiverID        string
	protocolProcessor ProtocolProcessor
}

type XMLProcessor struct {
}

func (xp *XMLProcessor) parse(srcData []byte) (*WXBizMsg4Recv, *CryptError) {
	if len(srcData) == 0 {
		return nil, NewCryptError(ParseXMLError, "data is empty")
	}
	var msg4Recv WXBizMsg4Recv
	err := xml.Unmarshal(srcData, &msg4Recv)
	if err != nil {
		return nil, NewCryptError(ParseXMLError, fmt.Sprintf("xml to msg fail, err: %v", err))
	}
	return &msg4Recv, nil
}

func (xp *XMLProcessor) serialize(msg4Send *WXBizMsg4Send) ([]byte, *CryptError) {
	xmlMsg, err := xml.Marshal(msg4Send)
	if err != nil {
		return nil, NewCryptError(GenXMLError, err.Error())
	}
	return xmlMsg, nil
}

func NewWXBizMsgCrypt(token, encodingAESKey, receiverID string, protocolType ProtocolType) *WXBizMsgCrypt {
	var protocolProcessor ProtocolProcessor
	if protocolType != XMLType {
		panic("unsupported protocol")
	} else {
		protocolProcessor = new(XMLProcessor)
	}

	return &WXBizMsgCrypt{
		token:             token,
		encodingAESKey:    encodingAESKey + "=",
		receiverID:        receiverID,
		protocolProcessor: protocolProcessor,
	}
}

func (wxMsg *WXBizMsgCrypt) randString(n int) string {
	b := make([]byte, n)
	for i := range b {
		b[i] = letterBytes[rand.Int63()%int64(len(letterBytes))] // nolint
	}
	return string(b)
}

func (wxMsg *WXBizMsgCrypt) pKCS7Padding(plaintext string, blockSize int) []byte {
	padding := blockSize - (len(plaintext) % blockSize)
	padtext := bytes.Repeat([]byte{byte(padding)}, padding)
	var buffer bytes.Buffer
	buffer.WriteString(plaintext)
	buffer.Write(padtext)
	return buffer.Bytes()
}

func (wxMsg *WXBizMsgCrypt) pKCS7Unpadding(plaintext []byte, blockSize int) ([]byte, *CryptError) {
	plaintextLen := len(plaintext)
	if plaintext == nil || plaintextLen == 0 {
		return nil, NewCryptError(DecryptAESError, "pKCS7Unpadding error nil or zero")
	}
	if plaintextLen%blockSize != 0 {
		return nil, NewCryptError(DecryptAESError, "pKCS7Unpadding text not a multiple of the block size")
	}
	paddingLen := int(plaintext[plaintextLen-1])
	return plaintext[:plaintextLen-paddingLen], nil
}

func (wxMsg *WXBizMsgCrypt) cbcEncrypt(plaintext string) ([]byte, *CryptError) {
	aeskey, err := base64.StdEncoding.DecodeString(wxMsg.encodingAESKey)
	if err != nil {
		return nil, NewCryptError(DecodeBase64Error, err.Error())
	}
	const blockSize = 32
	padMsg := wxMsg.pKCS7Padding(plaintext, blockSize)

	block, err := aes.NewCipher(aeskey)
	if err != nil {
		return nil, NewCryptError(EncryptAESError, err.Error())
	}

	ciphertext := make([]byte, len(padMsg))
	iv := aeskey[:aes.BlockSize]

	mode := cipher.NewCBCEncrypter(block, iv)

	mode.CryptBlocks(ciphertext, padMsg)
	base64Msg := make([]byte, base64.StdEncoding.EncodedLen(len(ciphertext)))
	base64.StdEncoding.Encode(base64Msg, ciphertext)

	return base64Msg, nil
}

func (wxMsg *WXBizMsgCrypt) cbcDecrypt(base64EncryptMsg string) ([]byte, *CryptError) {
	asked, err := base64.StdEncoding.DecodeString(wxMsg.encodingAESKey)
	if err != nil {
		return nil, NewCryptError(DecodeBase64Error, err.Error())
	}

	encryptMsg, err := base64.StdEncoding.DecodeString(base64EncryptMsg)
	if err != nil {
		return nil, NewCryptError(DecodeBase64Error, err.Error())
	}

	block, err := aes.NewCipher(asked)
	if err != nil {
		return nil, NewCryptError(DecryptAESError, err.Error())
	}

	if len(encryptMsg) < aes.BlockSize {
		return nil, NewCryptError(DecryptAESError, "encrypt_msg size is not valid")
	}

	iv := asked[:aes.BlockSize]

	if len(encryptMsg)%aes.BlockSize != 0 {
		return nil, NewCryptError(DecryptAESError, "encrypt_msg not a multiple of the block size")
	}

	mode := cipher.NewCBCDecrypter(block, iv)

	mode.CryptBlocks(encryptMsg, encryptMsg)

	return encryptMsg, nil
}

func (wxMsg *WXBizMsgCrypt) calSignature(timestamp, nonce, data string) string {
	sortArr := []string{wxMsg.token, timestamp, nonce, data}
	sort.Strings(sortArr)
	var buffer bytes.Buffer
	for _, value := range sortArr {
		buffer.WriteString(value)
	}

	sha := sha1.New() // nolint
	_, _ = sha.Write(buffer.Bytes())
	return fmt.Sprintf("%x", sha.Sum(nil))
}

func (wxMsg *WXBizMsgCrypt) parsePlainText(plaintext []byte) (msg, receiverID []byte, err *CryptError) {
	var blockSize = 32
	plaintext, err = wxMsg.pKCS7Unpadding(plaintext, blockSize)
	if err != nil {
		return
	}

	var maxTextLen uint32 = 20
	textLen := uint32(len(plaintext))
	if textLen < maxTextLen {
		err = NewCryptError(IllegalBuffer, "plain is too small")
		return
	}
	msgLen := binary.BigEndian.Uint32(plaintext[16:20])
	if textLen < (20 + msgLen) {
		err = NewCryptError(IllegalBuffer, "plain is too small")
		return
	}

	msg = plaintext[20 : 20+msgLen]
	receiverID = plaintext[20+msgLen:]
	return
}

func (wxMsg *WXBizMsgCrypt) verifyURL(msgSignature, timestamp, nonce, echostr string) ([]byte, *CryptError) {
	signature := wxMsg.calSignature(timestamp, nonce, echostr)
	if strings.Compare(signature, msgSignature) != 0 {
		return nil, NewCryptError(ValidateSignatureError, "signature not equal")
	}

	plaintext, err := wxMsg.cbcDecrypt(echostr)
	if err != nil {
		return nil, err
	}

	msg, receiverID, err := wxMsg.parsePlainText(plaintext)
	if err != nil {
		return nil, err
	}

	if len(wxMsg.receiverID) > 0 && strings.Compare(string(receiverID), wxMsg.receiverID) != 0 {
		fmt.Println(string(receiverID), wxMsg.receiverID, len(receiverID), len(wxMsg.receiverID))
		return nil, NewCryptError(ValidateCorpidError, "receiver_id is not equil")
	}

	return msg, nil
}

func (wxMsg *WXBizMsgCrypt) encryptMsg(replyMsg, timestamp, nonce string) ([]byte, *CryptError) {
	strLen := 16
	randStr := wxMsg.randString(strLen)
	var buffer bytes.Buffer
	buffer.WriteString(randStr)

	msgLenBuf := make([]byte, 4)
	binary.BigEndian.PutUint32(msgLenBuf, uint32(len(replyMsg)))
	buffer.Write(msgLenBuf)
	buffer.WriteString(replyMsg)
	buffer.WriteString(wxMsg.receiverID)

	tmpCiphertext, err := wxMsg.cbcEncrypt(buffer.String())
	if err != nil {
		return nil, err
	}
	ciphertext := string(tmpCiphertext)

	signature := wxMsg.calSignature(timestamp, nonce, ciphertext)

	msg4Send := NewWXBizMsg4Send(ciphertext, signature, timestamp, nonce)
	return wxMsg.protocolProcessor.serialize(msg4Send)
}

func (wxMsg *WXBizMsgCrypt) decryptMsg(msgSignature, timestamp, nonce string, postData []byte) ([]byte, *CryptError) {
	msg4Recv, cryptErr := wxMsg.protocolProcessor.parse(postData)
	if cryptErr != nil {
		return nil, cryptErr
	}

	signature := wxMsg.calSignature(timestamp, nonce, msg4Recv.Encrypt)

	if strings.Compare(signature, msgSignature) != 0 {
		return nil, NewCryptError(ValidateSignatureError, "signature not equal")
	}

	plaintext, cryptErr := wxMsg.cbcDecrypt(msg4Recv.Encrypt)
	if cryptErr != nil {
		return nil, cryptErr
	}

	msg, receiverID, cryptErr := wxMsg.parsePlainText(plaintext)
	if cryptErr != nil {
		return nil, cryptErr
	}

	if len(wxMsg.receiverID) > 0 && strings.Compare(string(receiverID), wxMsg.receiverID) != 0 {
		return nil, NewCryptError(ValidateCorpidError, "receiver_id is not equil")
	}

	return msg, nil
}
