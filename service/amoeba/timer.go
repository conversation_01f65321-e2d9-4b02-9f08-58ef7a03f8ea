package amoeba

import (
	"fmt"
	"strconv"
	"strings"
	"time"
)

func structTime3(date time.Time) string {
	return date.Format("2006-01-02")
}

//func structTime4(date time.Time) string {
//	return date.Format("200601")
//}

func timeToSeconds(strTime interface{}) int {
	switch v := strTime.(type) {
	case int:
		return v
	case string:
		timeParts := strings.Split(v, ":")
		if len(timeParts) != 3 {
			fmt.Println("Invalid time format")
			return 0
		}
		hours, err := strconv.Atoi(timeParts[0])
		if err != nil {
			fmt.Println("Error converting hours:", err)
			return 0
		}
		minutes, err := strconv.Atoi(timeParts[1])
		if err != nil {
			fmt.Println("Error converting minutes:", err)
			return 0
		}
		seconds, err := strconv.Atoi(timeParts[2])
		if err != nil {
			fmt.Println("Error converting seconds:", err)
			return 0
		}
		return hours*3600 + minutes*60 + seconds
	case time.Time:
		return v.Hour()*3600 + v.Minute()*60 + v.Second()
	default:
		fmt.Println("Unknown type:", v)
		return 0
	}
}
