package amoeba

import (
	"fmt"
	"strings"
	"time"
)

// Iteration 迭代
type Iteration struct {
	IterationID string
	Name        string
	Start       int
	End         string
	CreatedAt   int64
	UpdatedAt   int64
}

//func NewIteration(iterationID string) *Iteration {
//	currentTime := time.Now().Unix()
//	return &Iteration{
//		IterationID: iterationID,
//		CreatedAt:   currentTime,
//		UpdatedAt:   currentTime,
//	}
//}

func (i *Iteration) CheckInfo(tup []interface{}) bool {
	if len(tup) != 7 {
		return true
	}
	if i.Name != tup[2].(string) {
		return true
	}
	if i.Start != tup[3].(int) {
		return true
	}
	if i.End != tup[4].(string) {
		return true
	}
	return false
}

// Story 需求
type Story struct {
	StoryID     string
	IterationID string
	Name        string
	Effort      float64
	ProductLine string
	ProductType string
	CreatedAt   int64
	UpdatedAt   int64
}

//func NewStory(storyID string) *Story {
//	currentTime := time.Now().Unix()
//	return &Story{
//		StoryID:   storyID,
//		CreatedAt: currentTime,
//		UpdatedAt: currentTime,
//	}
//}

func (s *Story) CheckInfo(tup []interface{}) bool {
	if len(tup) != 9 {
		return true
	}
	if s.IterationID != tup[2].(string) {
		return true
	}
	if s.Name != tup[3].(string) {
		return true
	}
	if s.Effort != tup[4].(float64) {
		return true
	}
	if s.ProductLine != tup[5].(string) {
		return true
	}
	if s.ProductType != tup[6].(string) {
		return true
	}
	return false
}

// Task 任务
type Task struct {
	TaskID      string
	Begin       int
	Due         int
	Owner       string
	Effort      float64
	ProductLine string
	ProductType string
	StoryID     string
	IterationID string
	CreatedAt   int64
	UpdatedAt   int64
}

//func NewTask(taskID string) *Task {
//	currentTime := time.Now().Unix()
//	return &Task{
//		TaskID:    taskID,
//		CreatedAt: currentTime,
//		UpdatedAt: currentTime,
//	}
//}

func (t *Task) CheckInfo(tup []interface{}) bool {
	if len(tup) != 12 {
		return true
	}
	if t.Owner != tup[2].(string) {
		return true
	}
	if t.Effort != tup[3].(float64) {
		return true
	}
	if t.ProductLine != tup[6].(string) {
		return true
	}
	if t.StoryID != tup[7].(string) {
		return true
	}
	if t.IterationID != tup[8].(string) {
		return true
	}
	if t.ProductType != tup[9].(string) {
		return true
	}
	if t.Begin != tup[10].(int) {
		return true
	}
	if t.Due != tup[11].(int) {
		return true
	}
	return false
}

// Owner 签到信息
type Owner struct {
	Owner       string
	AddEffort   float64
	LeaveEffort float64
	TimeAt      int
	CreateAt    int64
	UpdateAt    int64
	Department  int
}

//func NewOwner() *Owner {
//	currentTime := time.Now().Unix()
//	return &Owner{
//		CreateAt: currentTime,
//		UpdateAt: currentTime,
//	}
//}

type Developer struct {
	Name string
	// 当月一共打卡多久
	Hour float64
	// 哪个月 201312
	Time int
	// 每月日期列表，里面放打过卡的日期
	Date []string
	// 打卡时间列表，里面放 打过卡日期 对应的打卡数据
	SignTime map[string]int
}

//func NewDeveloper(initName string, initDate time.Time, initTime string) *Developer {
//	num, err := strconv.Atoi(structTime4(initDate))
//	if err != nil {
//		num = 0
//	}
//	developer := &Developer{
//		Name:     initName,
//		Time:     num,
//		Date:     []string{structTime3(initDate)},
//		SignTime: make(map[string]int),
//	}
//	dateStr := structTime3(initDate)
//	developer.AddTime(dateStr, initTime)
//	return developer
//}

func (d *Developer) CheckDate(newDate time.Time, newTime string) {
	dateStr := structTime3(newDate)
	if !contains(d.Date, dateStr) {
		d.Date = append(d.Date, dateStr)
	}
	d.AddTime(dateStr, newTime)
}

func (d *Developer) AddTime(newDate string, newTime string) {
	checkSecond := timeToSeconds("14:00:00")
	newSecond := timeToSeconds(newTime)
	var timeKey string
	if newSecond > checkSecond {
		// 下班打卡
		timeKey = fmt.Sprintf("%s_下班", newDate)
	} else {
		// 上班打卡
		timeKey = fmt.Sprintf("%s_上班", newDate)
	}

	if _, exists := d.SignTime[timeKey]; !exists {
		// 没有 key
		d.SignTime[timeKey] = newSecond
	} else {
		oriSecond := d.SignTime[timeKey]
		if strings.Contains(timeKey, "上班") {
			// 上班，谁小用谁
			d.SignTime[timeKey] = min(oriSecond, newSecond)
		} else {
			// 下班，谁大用谁
			d.SignTime[timeKey] = max(oriSecond, newSecond)
		}
	}
}

func (d *Developer) GetHour() {
	var totalSeconds int
	for _, value := range d.Date {
		halfDay := false
		upTime, exists := d.SignTime[fmt.Sprintf("%s_上班", value)]
		if !exists {
			d.SignTime[fmt.Sprintf("%s_上班", value)] = timeToSeconds("13:30:00")
			halfDay = true
		}
		downTime, exists := d.SignTime[fmt.Sprintf("%s_下班", value)]
		if !exists {
			d.SignTime[fmt.Sprintf("%s_下班", value)] = timeToSeconds("12:00:00")
			halfDay = true
		}
		if halfDay {
			totalSeconds += downTime - upTime
		} else {
			totalSeconds += downTime - upTime - timeToSeconds("01:30:00")
		}
	}
	d.Hour = float64(totalSeconds) / 3600
}

func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

// Sign 打卡
type Sign struct {
	Owner    string
	TimeAt   int
	Hour     float64
	CreateAt int64
	UpdateAt int64
}

//func NewSign() *Sign {
//	currentTime := time.Now().Unix()
//	return &Sign{
//		CreateAt: currentTime,
//		UpdateAt: currentTime,
//	}
//}

func (s *Sign) CheckInfo(tup []interface{}) bool {
	if len(tup) != 6 {
		return true
	}
	if s.Hour != tup[3].(float64) {
		return true
	}
	return false
}
