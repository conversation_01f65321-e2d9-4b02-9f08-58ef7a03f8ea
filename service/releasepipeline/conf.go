package releasepipeline

import (
	"encoding/json"
	"fmt"
	"strings"

	"gitlab.dailyyoga.com.cn/rdc/serverops/database"
	"gitlab.dailyyoga.com.cn/rdc/serverops/database/apppackage"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
)

type stageConfirmUser struct {
	Ref     string            `json:"ref,omitempty"`
	Default string            `json:"default,omitempty"`
	Apps    []confirmUser4App `json:"apps,omitempty"`
}

type StageAfterConfirm struct {
	WechatRobot *WechatRobotMessage `json:"wechat_robot,omitempty"`
}

type WechatRobotMessage struct {
	URL           string                     `json:"url"`
	Markdown      string                     `json:"markdown"`
	Images        []*WechatRobotMessageImage `json:"images,omitempty"`
	MentionedList []string                   `json:"mentioned_list,omitempty"`
}

type WechatRobotMessageImage struct {
	Stage       string `json:"stage"`
	ImageIndexs []int  `json:"image_indexs"`
}

type stageInfo struct {
	Stage          string             `json:"stage"`
	Name           string             `json:"name"`
	Desc           []string           `json:"desc"`
	ConfirmUser    *stageConfirmUser  `json:"confirm_user"`
	NeedEvidence   bool               `json:"need_evidence"`
	AllowTerminate bool               `json:"allow_terminate"`
	EvidenceDesc   []string           `json:"evidence_desc"`
	AfterConfirm   *StageAfterConfirm `json:"after_confirm,omitempty"`
}

type confirmUser4App struct {
	App  string `json:"app"`
	User string `json:"user"`
}

type confirmUserInfo struct {
	Default string            `json:"default"`
	Apps    []confirmUser4App `json:"apps"`
}

type Template struct {
	Pipeline    []stageInfo                `json:"pipeline"`
	CC          []string                   `json:"cc"`
	ConfirmUser map[string]confirmUserInfo `json:"confirm_user"`
}

func fetchYAMLWithOS(os apppackage.OS) *Template {
	configKey := fmt.Sprintf("PIPELINE_TEMPLATE_%s", strings.ToUpper(string(os)))
	keyValues := database.TbConfigKeys.Get(configKey)
	if keyValues == nil {
		logger.Errorf("fetch %s failed", configKey)
		return nil
	}

	data := &Template{}
	if err := json.NewDecoder(strings.NewReader(keyValues.Value)).Decode(data); err != nil {
		logger.Errorf("parse %s failed, err: %s", configKey, err)
		return nil
	}
	return data
}

func fetchConfirmUserWithApps(appName, def string, apps []confirmUser4App) string {
	if len(apps) == 0 {
		return def
	}
	for _, appUser := range apps {
		if appUser.App == appName {
			return appUser.User
		}
	}
	return def
}

func getConfirmUser(appName string, stageConfirmUser *stageConfirmUser, confirmUser map[string]confirmUserInfo) string {
	if stageConfirmUser == nil {
		return ""
	}
	if stageConfirmUser.Ref == "" {
		return fetchConfirmUserWithApps(appName, stageConfirmUser.Default, stageConfirmUser.Apps)
	}
	ref := stageConfirmUser.Ref
	if _, ok := confirmUser[ref]; !ok {
		return ""
	}
	data := confirmUser[ref]
	return fetchConfirmUserWithApps(appName, data.Default, data.Apps)
}
