package releasepipeline

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	safelygo "gitlab.dailyyoga.com.cn/gokit/safely-go"
	"gitlab.dailyyoga.com.cn/rdc/serverops/database/apppackage"
	"gitlab.dailyyoga.com.cn/rdc/serverops/database/releasepipeline"
	"gitlab.dailyyoga.com.cn/rdc/serverops/database/wework"
	service "gitlab.dailyyoga.com.cn/rdc/serverops/service/wework"
	"gitlab.dailyyoga.com.cn/rdc/serverops/service/wework/message"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
)

type runner struct {
	initialed bool
	stageChan chan int64
}

var Runner runner

func (r *runner) Run(stageID int64) {
	logger.Infof("runner stage_id: %d", stageID)
	if !r.initialed || r.stageChan == nil || stageID == 0 {
		return
	}
	r.stageChan <- stageID
}

func Init() {
	if Runner.initialed {
		return
	}

	runnerStageBuffer := 5
	Runner.stageChan = make(chan int64, runnerStageBuffer)

	safelygo.Run(func() {
		for stageID := range Runner.stageChan {
			stageRun(stageID)
		}
	})

	Runner.initialed = true
}

func stageRun(id int64) {
	stageItem := releasepipeline.TbStage.GetItem(id)
	if stageItem == nil || stageItem.Status != releasepipeline.StatusWaiting {
		return
	}

	pipelineInfo := releasepipeline.TbPipeline.GetItem(stageItem.PipelineID)
	if pipelineInfo == nil || pipelineInfo.ID == 0 {
		return
	}

	// if is the first stage, change pipeline status running
	if stageItem.Index == 1 {
		pipelineInfo.Status = releasepipeline.StatusRunning
		if err := pipelineInfo.Update(); err != nil {
			logger.Error(err)
			return
		}
	}

	stageItem.Status = releasepipeline.StatusRunning
	if err := stageItem.Update(); err != nil {
		logger.Error(err)
		return
	}

	userids := []string{stageItem.ConfirmUser}
	// append cc users
	if pipelineInfo.CC != "" {
		userids = append(userids, strings.Split(pipelineInfo.CC, ";")...)
	}
	// check exist
	if users := wework.TbUsers.GetUsers(service.ServeropsAgentID, userids); len(users) > 0 {
		sendTemplateCardMessage(users, stageItem, pipelineInfo)
	}
}

func sendTemplateCardMessage(users []*wework.Users,
	stageInfo *releasepipeline.Stage, pipelineInfo *releasepipeline.Pipeline) {
	ctx := context.Background()
	for _, item := range users {
		// send template_card text_notice message
		resp, err := message.TemplateCardTextNoticeMessage(ctx, &message.TemplateCardOptions{
			ToUser:       item.UserID,
			TemplateCard: getTemplateCardContent(stageInfo, pipelineInfo),
		})
		if err != nil {
			logger.Errorf("send application message _user: %s, _name: %s, err: %s",
				item.UserID, item.Name, err.Error())
		} else {
			logger.Infof("send application message _user: %s, _name: %s, _id: %s",
				item.UserID, item.Name, resp.MsgID)
		}
	}
}

func getTemplateCardContent(stage *releasepipeline.Stage, pipelineInfo *releasepipeline.Pipeline) *message.TemplateCardContent {
	content := &message.TemplateCardContent{
		HorizontalContentList: make([]*message.TemplateCardHorizontalContentItem, 0),
	}

	appInfo, ok := apppackage.AppInfos[pipelineInfo.AppName]
	if !ok {
		appInfo = apppackage.AppInfo{Name: string(pipelineInfo.AppName), Icon: ""}
	}

	osName, ok := apppackage.OSName[pipelineInfo.OS]
	if !ok {
		osName = "unknown"
	}

	content.Source = &message.TemplateCardSource{
		IconURL: appInfo.Icon,
		// AppCNName - OS - StageName
		Desc:      fmt.Sprintf("%s - %s - %s", appInfo.Name, osName, stage.Name),
		DescColor: 1,
	}

	content.MainTitle = &message.TemplateCardMainTitle{
		Title: fmt.Sprintf("第 %d 步：%s", stage.Index, stage.Name),
	}

	content.QuoteArea = &message.TemplateCardQuoteArea{
		QuoteText: stage.Description,
	}

	content.HorizontalContentList = append(content.HorizontalContentList,
		&message.TemplateCardHorizontalContentItem{
			KeyName: "流水线编号", Value: strconv.FormatInt(pipelineInfo.ID, 10),
		},
		&message.TemplateCardHorizontalContentItem{KeyName: "应用版本", Value: pipelineInfo.AppVersion},
		&message.TemplateCardHorizontalContentItem{KeyName: "应用标识", Value: appInfo.Name},
		&message.TemplateCardHorizontalContentItem{KeyName: "端", Value: osName})

	pipelineDetailURL := fmt.Sprintf("https://serverops.dailyyoga.com.cn/release/pipeline/?id=%d", stage.PipelineID)
	content.CardAction = &message.TemplateCardActionItem{
		Type: 1, URL: pipelineDetailURL,
	}

	content.JumpList = make([]*message.TemplateCardActionItem, 0)
	content.JumpList = append(content.JumpList,
		&message.TemplateCardActionItem{
			Type: 1, Title: "查看详情", URL: pipelineDetailURL,
		})
	return content
}
