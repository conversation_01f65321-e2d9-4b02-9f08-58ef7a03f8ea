package releasepipeline

import (
	"context"
	"fmt"

	"gitlab.dailyyoga.com.cn/rdc/serverops/database/apppackage"
	"gitlab.dailyyoga.com.cn/rdc/serverops/database/wework"
	service "gitlab.dailyyoga.com.cn/rdc/serverops/service/wework"
	"gitlab.dailyyoga.com.cn/rdc/serverops/service/wework/message"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
)

type sender struct{}

var Sender sender

func (s *sender) SendPreCheckFailedMessage(userids []string,
	appname apppackage.AppName, os apppackage.OS, appversion string) {
	appInfo, ok := apppackage.AppInfos[appname]
	if !ok {
		appInfo = apppackage.AppInfo{Name: string(appname), Icon: ""}
	}
	osName := "Android"
	if os == apppackage.OSAppleiOS {
		osName = "iOS"
	}

	users := wework.TbUsers.GetUsers(service.ServeropsAgentID, userids)
	if len(users) == 0 {
		logger.Errorf("wework application message touser is emtpy: %#v", userids)
		return
	}
	packageInfo := fmt.Sprintf("%s - %s - %s", appInfo.Name, osName, appversion)

	for _, item := range users {
		resp, err := message.TemplateCardTextNoticeMessage(context.Background(), &message.TemplateCardOptions{
			ToUser: item.UserID,
			TemplateCard: &message.TemplateCardContent{
				Source: &message.TemplateCardSource{
					IconURL:   appInfo.Icon,
					Desc:      packageInfo,
					DescColor: 1,
				},
				MainTitle: &message.TemplateCardMainTitle{
					Title: "包体异常未触发流水线通知",
					Desc:  "应用包触发包体告警，发版流水线未开启",
				},
				QuoteArea: &message.TemplateCardQuoteArea{
					QuoteText: "请在修复异常后重新打包并提交，以触发 App 发版流水线！！！",
				},
				CardAction: &message.TemplateCardActionItem{
					Type: 1,
					URL:  fmt.Sprintf("https://serverops.dailyyoga.com.cn/release/pipeline/exception?info=%s", packageInfo),
				},
			},
		})
		if err != nil {
			logger.Errorf("send pre check failed message, _user: %s, _name: %s, err: %s", item.UserID, item.Name, err)
		} else if resp.ErrCode > 0 && resp.ErrMsg != "" {
			logger.Errorf("send pre check failed message, _user: %s, _name: %s, err: %s", item.UserID, item.Name, resp.ErrMsg)
		} else {
			logger.Infof("send pre check failed message, _user: %s, _name: %s, _id: %s", item.UserID, item.Name, resp.MsgID)
		}
	}
}
