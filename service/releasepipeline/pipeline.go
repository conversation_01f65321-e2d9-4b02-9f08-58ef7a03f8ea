package releasepipeline

import (
	_ "embed"
	"encoding/json"

	"gitlab.dailyyoga.com.cn/rdc/serverops/database/apppackage"
	"gitlab.dailyyoga.com.cn/rdc/serverops/database/releasepipeline"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
)

type Pipeline struct {
	Status releasepipeline.Status
	Stages []*stage
	CC     []string
}

type stage struct {
	Index               int
	Key                 string
	Name                string
	Description         []string
	Status              releasepipeline.Status
	AllowTerminate      bool
	NeedEvidence        bool
	EvidenceDescription []string
	ConfirmUser         string
	AfterConfirm        string
}

func GetConfigWithApp(os apppackage.OS, appName string) *Pipeline {
	pp := &Pipeline{
		Status: releasepipeline.StatusWaiting,
		Stages: make([]*stage, 0),
		CC:     make([]string, 0),
	}

	template := fetchYAMLWithOS(os)
	if template == nil {
		return nil
	}

	pp.CC = template.CC
	for index, stageConf := range template.Pipeline {
		stageData := &stage{
			Index:               index,
			Key:                 stageConf.Stage,
			Name:                stageConf.Name,
			Description:         stageConf.Desc,
			Status:              releasepipeline.StatusWaiting,
			AllowTerminate:      stageConf.AllowTerminate,
			NeedEvidence:        stageConf.NeedEvidence,
			EvidenceDescription: stageConf.EvidenceDesc,
			ConfirmUser:         getConfirmUser(appName, stageConf.ConfirmUser, template.ConfirmUser),
		}
		if stageConf.AfterConfirm != nil {
			afterConfirmInfo, err := json.Marshal(stageConf.AfterConfirm)
			if err != nil {
				logger.Error("failed to marshal after confirm info", "error", err)
				continue
			}
			stageData.AfterConfirm = string(afterConfirmInfo)
		}

		pp.Stages = append(pp.Stages, stageData)
	}

	return pp
}
