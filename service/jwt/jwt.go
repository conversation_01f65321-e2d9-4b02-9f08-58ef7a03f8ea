package jwt

import (
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v4"
	"gitlab.dailyyoga.com.cn/rdc/serverops/database/admin"
)

const SecretKey = "DailyYogaServerOpsJWTKey"

type CustomClaims struct {
	ID       int    `json:"id,omitempty"`
	Username string `json:"username,omitempty"`
	Nickname string `json:"nickname,omitempty"`
	jwt.RegisteredClaims
}

type Token struct {
	Token    string
	ExpireAt int64
}

// GenerateToken generate jwt token
func GenerateToken(user *admin.User) *Token {
	tokenExpire := 86400 * time.Second
	jwtToken := &Token{
		ExpireAt: time.Now().Add(tokenExpire).Unix(),
	}

	claims := &CustomClaims{
		ID: user.UID, Username: user.Username, Nickname: user.Nickname,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Unix(jwtToken.ExpireAt, 0)),
		},
	}

	var err error
	jwtToken.Token, err = jwt.NewWithClaims(jwt.SigningMethodHS256, claims).SignedString([]byte(SecretKey))
	if err != nil {
		return nil
	}
	return jwtToken
}

// ParseToken parse jwt token
func ParseToken(tokenString string) (*CustomClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &CustomClaims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(SecretKey), nil
	})
	if err != nil {
		return nil, err
	}
	if claims, ok := token.Claims.(*CustomClaims); ok && token.Valid {
		return claims, nil
	}
	return nil, err
}
