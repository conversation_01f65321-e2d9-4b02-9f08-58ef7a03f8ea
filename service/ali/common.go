package ali

import (
	openapi "github.com/alibabacloud-go/darabonba-openapi/v2/client"
	"github.com/alibabacloud-go/tea/tea"
	"github.com/aliyun/aliyun-oss-go-sdk/oss"
	credential "github.com/aliyun/credentials-go/credentials"
)

const (
	OSSEndponit  = "oss-cn-hangzhou.aliyuncs.com"
	AccessKeyID  = "LTAI4FdAXhwbaBrHrSRgMfpR"
	AccessKeySec = "******************************"
	RDSEndponit  = "rds.aliyuncs.com"
)

const AndroidReleaseBucket = "dailyyoga-android-release"
const SignedURLExpireInSec = 300

type ossOps struct{}

var OSS ossOps

func (o *ossOps) getBucketManager(bucket string) (*oss.Bucket, error) {
	client, err := oss.New(OSSEndponit, AccessKeyID, AccessKeySec)
	if err != nil {
		return nil, err
	}
	return client.Bucket(bucket)
}

func CreateRdsClient() (_result *openapi.Client, _err error) {
	conf := new(credential.Config).SetType("access_key").
		SetAccessKeyId(AccessKeyID).
		SetAccessKeySecret(AccessKeySec)
	// 工程代码建议使用更安全的无AK方式，凭据配置方式请参见：https://help.aliyun.com/document_detail/378661.html。
	c, _err := credential.NewCredential(conf)
	if _err != nil {
		return _result, _err
	}

	config := &openapi.Config{
		Credential: c,
	}
	// Endpoint 请参考 https://api.aliyun.com/product/Rds
	config.Endpoint = tea.String(RDSEndponit)
	_result = &openapi.Client{}
	_result, _err = openapi.NewClient(config)
	return _result, _err
}
