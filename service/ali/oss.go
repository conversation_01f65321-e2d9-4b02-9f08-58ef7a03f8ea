package ali

import (
	"fmt"
	"strconv"
	"strings"

	"github.com/aliyun/aliyun-oss-go-sdk/oss"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
)

type Prefix struct {
	Display string `json:"display"`
	Real    string `json:"real"`
}

type Object struct {
	DisplayKey   string `json:"display_key"`
	RealKey      string `json:"real_key"`
	Size         int64  `json:"size"`
	LastModified int64  `json:"last_modified"`
}

type ObjectDownloadInfo struct {
	DownloadURL string `json:"download_url"`
	Name        string `json:"name"`
	ExpireSec   int    `json:"expire_sec"`
}

type BucketItem struct {
	Prefix *Prefix `json:"prefix,omitempty"`
	Object *Object `json:"object,omitempty"`
}

func (o *ossOps) ListBucket(bucketName, prefix string) []BucketItem {
	objects := make([]BucketItem, 0)

	bucketManager, err := o.getBucketManager(bucketName)
	if err != nil {
		return nil
	}

	marker := ""
	for {
		lsRes, err := bucketManager.ListObjects(oss.Marker(marker), oss.Delimiter("/"), oss.Prefix(prefix))
		if err != nil {
			return objects
		}
		for i := range lsRes.Objects {
			item := lsRes.Objects[i]
			displayKey := strings.Replace(item.Key, prefix, "", -1)
			if displayKey == "" {
				continue
			}
			objects = append(objects, BucketItem{
				Object: &Object{
					DisplayKey: displayKey, RealKey: item.Key, Size: item.Size, LastModified: item.LastModified.Unix(),
				},
			})
		}

		for _, v := range lsRes.CommonPrefixes {
			objects = append(objects, BucketItem{
				Prefix: &Prefix{Display: strings.Replace(v, prefix, "", -1), Real: v},
			})
		}

		if lsRes.IsTruncated {
			marker = lsRes.NextMarker
		} else {
			break
		}
	}
	return objects
}

func (o *ossOps) SearchOriginalAPKInfo(channelName, channelID, version string) (map[string]ObjectDownloadInfo, error) {
	bucketManager, err := o.getBucketManager(AndroidReleaseBucket)
	if err != nil {
		return nil, err
	}

	originalFiles := make(map[string]ObjectDownloadInfo)

	supportArch := []string{
		"all", "arm64-v8a", "armeabi-v7a",
	}

	for _, arch := range supportArch {
		prefixKey := fmt.Sprintf("%s/original/%s_%s_release_%s_%s_",
			version, channelName, channelID, arch, version)
		lsRes, err := bucketManager.ListObjectsV2(oss.Prefix(prefixKey))
		if err != nil {
			return nil, err
		}
		if len(lsRes.Objects) == 0 {
			continue
		}
		object := o.getApkRecentlyFile(prefixKey, lsRes.Objects)
		if object == nil || object.Key == "" {
			continue
		}
		downloadURL, err := bucketManager.SignURL(object.Key, oss.HTTPGet, SignedURLExpireInSec)
		if err != nil {
			continue
		}
		originalFiles[arch] = ObjectDownloadInfo{
			DownloadURL: downloadURL,
			Name:        strings.Replace(object.Key, fmt.Sprintf("%s/original/", version), "", -1),
			ExpireSec:   SignedURLExpireInSec,
		}
	}
	return originalFiles, nil
}

func (o *ossOps) getApkRecentlyFile(prefixKey string, objects []oss.ObjectProperties) *oss.ObjectProperties {
	var recentlyDate int64 = 0
	var currentObject oss.ObjectProperties
	for i := range objects {
		object := objects[i]
		dateStr := strings.Replace(strings.Replace(object.Key, prefixKey, "", -1), ".apk", "", -1)
		date, _ := strconv.ParseInt(dateStr, 10, 64)
		if date > recentlyDate {
			recentlyDate = date
			currentObject = object
		}
	}
	return &currentObject
}

func (o *ossOps) GetDownloadURL(bucket, key string) ObjectDownloadInfo {
	ret := ObjectDownloadInfo{
		Name: key, ExpireSec: SignedURLExpireInSec,
	}
	bucketManager, err := o.getBucketManager(bucket)
	if err != nil {
		logger.Error(err)
		return ret
	}
	if ok, err := bucketManager.IsObjectExist(key); err != nil || !ok {
		logger.Errorf("file not found. key: %s", key)
		return ret
	}
	downloadURL, err := bucketManager.SignURL(key, oss.HTTPGet, SignedURLExpireInSec)
	if err != nil {
		logger.Error(err)
		return ret
	}
	ret.DownloadURL = downloadURL
	return ret
}
