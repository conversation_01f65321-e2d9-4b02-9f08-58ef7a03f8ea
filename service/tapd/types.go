package tapd

type Iterations struct {
	Iteration Iteration `json:"Iteration"`
}

type Iteration struct {
	ID        string `json:"id"`
	Name      string `json:"name"`
	StartDate string `json:"startdate"`
	EndDate   string `json:"enddate"`
	Status    string `json:"status"`
}

type Storys struct {
	Story Story `json:"Story"`
}

type Story struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

type Tasks struct {
	Task Task `json:"Task"`
}

type Task struct {
	Name        string `json:"name"`
	Owner       string `json:"owner"`
	Due         string `json:"due"`
	Status      string `json:"status"`
	DelayedDays int    `json:"-"`
}
