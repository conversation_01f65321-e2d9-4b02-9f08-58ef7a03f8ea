package tapd

import (
	"context"
	"encoding/json"
	"io/ioutil"
	"net/http"
	"net/url"
	"strings"
	"time"
)

const Account = "jzqTCJcw"
const AccountAuthKey = "********-3483-B352-75F0-19B14C3B69FA"
const WorkspaceH2 = "********"

func get(ctx context.Context, path string, params url.Values, bean interface{}) error {
	params.Set("workspace_id", WorkspaceH2)

	addr := "https://api.tapd.cn" + path + "?" + params.Encode()
	c := &http.Client{}
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, addr, nil)
	if err != nil {
		return err
	}
	req.SetBasicAuth(Account, AccountAuthKey)
	resp, err := c.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	if err := json.Unmarshal(body, bean); err != nil {
		return err
	}
	return nil
}

func GetCurrentIteration() ([]Iteration, error) {
	params := url.Values{}
	params.Set("limit", "20")
	params.Set("status", "open")
	params.Set("order", "startdate desc")

	type response struct {
		Data []*Iterations `json:"data"`
	}

	resp := &response{}
	if err := get(context.Background(), "/iterations", params, resp); err != nil {
		return nil, err
	}
	currentIterations := make([]Iteration, 0)
	for _, v := range resp.Data {
		currentIterations = append(currentIterations, v.Iteration)
	}
	return currentIterations, nil
}

func GetIterationStory(id string) ([]Story, error) {
	params := url.Values{}
	params.Set("limit", "200")
	params.Set("iteration_id", id)

	type response struct {
		Data []Storys `json:"data"`
	}
	resp := &response{}
	if err := get(context.Background(), "/stories", params, resp); err != nil {
		return nil, err
	}

	storys := make([]Story, 0)
	for _, v := range resp.Data {
		storys = append(storys, v.Story)
	}
	return storys, nil
}

func GetStoryTasks(iterationID, storyID string) ([]Task, error) {
	params := url.Values{}
	params.Set("limit", "200")
	params.Set("iteration_id", iterationID)
	params.Set("story_id", storyID)

	type response struct {
		Data []Tasks `json:"data"`
	}
	resp := &response{}
	if err := get(context.Background(), "/tasks", params, resp); err != nil {
		return nil, err
	}

	tasks := make([]Task, 0)
	for _, v := range resp.Data {
		v.Task.Owner = strings.Split(v.Task.Owner, ";")[0]
		tasks = append(tasks, v.Task)
	}
	return tasks, nil
}

func ParseDate(date string) int64 {
	t, _ := time.ParseInLocation("2006-01-02", date, time.Local)
	return t.Unix()
}
