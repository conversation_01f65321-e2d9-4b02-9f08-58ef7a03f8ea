package bill

import (
	"fmt"
	"strconv"
	"strings"
	"time"

	bssopenapi20171214 "github.com/alibabacloud-go/bssopenapi-20171214/v3/client"
	"github.com/alibabacloud-go/tea/tea"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
)

func CheckPendingRenewal(client *bssopenapi20171214.Client) (pendingRenewalCount string, err error) {
	// 生成当前 iso8601 UTC 时间 nowIsoTime
	nowUtcTime := time.Now().UTC().String()
	nowDateTime := strings.Split(nowUtcTime, " ")
	nowDate := nowDateTime[0]
	nowTimeMillisec := nowDateTime[1]
	nowTime := strings.Split(nowTimeMillisec, ".")[0]
	nowIsoTime := fmt.Sprintf("%sT%sZ", nowDate, nowTime)
	fmt.Println(nowIsoTime)

	// 生成30天后的 iso8601 UTC 时间 endIsoTime
	endUtcTime := time.Now().AddDate(0, 0, 30).String()
	endDateTime := strings.Split(endUtcTime, " ")
	endDate := endDateTime[0]
	endTime := "16:00:00"
	endIsoTime := fmt.Sprintf("%sT%sZ", endDate, endTime)
	fmt.Println(endIsoTime)

	queryAvailableInstancesRequest := &bssopenapi20171214.QueryAvailableInstancesRequest{
		EndTimeEnd:       tea.String(endIsoTime),
		SubscriptionType: tea.String("Subscription"),
		EndTimeStart:     tea.String(nowIsoTime),
	}
	response, err := client.QueryAvailableInstances(queryAvailableInstancesRequest)
	if err != nil {
		logger.Error(err)
		return
	}

	totalCount := *response.Body.Data.TotalCount
	pendingRenewalCount = strconv.Itoa(int(totalCount))

	return pendingRenewalCount, nil
}
