package bill

import (
	"fmt"
	"strings"
	"time"

	bssopenapi20171214 "github.com/alibabacloud-go/bssopenapi-20171214/v3/client"
	"github.com/alibabacloud-go/tea/tea"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
)

func AliyunGetBill(client *bssopenapi20171214.Client) (totalDailyBill string, _err error) {
	// 获取昨日的日期
	nTime := time.Now()
	yesTime := nTime.AddDate(0, 0, -1).String()
	yesDate := strings.Split(yesTime, " ")
	// 分割 yesDate 获取到昨日对应的月份和日期: yesMonth yesDay
	yesDay := yesDate[0]
	yesMonthSlice := strings.Split(yesDay, "-")
	yesMonth := yesMonthSlice[0] + "-" + yesMonthSlice[1]

	queryAccountBillRequest := &bssopenapi20171214.QueryAccountBillRequest{
		BillingCycle: tea.String(yesMonth),
		Granularity:  tea.String("DAILY"),
		BillingDate:  tea.String(yesDay),
	}
	response, err := client.QueryAccountBill(queryAccountBillRequest)
	if err != nil {
		logger.Error(err)
		return
	}

	var DailyBill float32
	for _, v := range response.Body.Data.Items.Item {
		dailyBill := *(v.PretaxAmount)
		DailyBill += dailyBill
	}
	totalDailyBill = fmt.Sprintf("%.2f", DailyBill)
	return totalDailyBill, nil
}
