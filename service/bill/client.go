package bill

import (
	bssopenapi20171214 "github.com/alibabacloud-go/bssopenapi-20171214/v3/client"
	openapi "github.com/alibabacloud-go/darabonba-openapi/v2/client"
	"github.com/alibabacloud-go/tea/tea"
)

func CreateClient(accessKeyID, accessKeySecret *string) (_result *bssopenapi20171214.Client, _err error) {
	config := &openapi.Config{
		AccessKeyId:     accessKeyID,
		AccessKeySecret: accessKeySecret,
	}
	// 访问的域名
	config.Endpoint = tea.String("business.aliyuncs.com")
	_result, _err = bssopenapi20171214.NewClient(config)
	return _result, _err
}
