package domain

import (
	"encoding/json"

	openapi "github.com/alibabacloud-go/darabonba-openapi/v2/client"
	openapiutil "github.com/alibabacloud-go/openapi-util/service"
	util "github.com/alibabacloud-go/tea-utils/v2/service"
	"github.com/alibabacloud-go/tea/tea"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
)

type aliApi struct {
}

var AliApi aliApi

type Record struct {
	CreateTimestamp int64  `json:"CreateTimestamp"`
	DomainName      string `json:"DomainName"`
	Line            string `json:"Line"`
	Locked          bool   `json:"Locked"`
	RR              string `json:"RR"`
	RecordID        string `json:"RecordId"`
	Status          string `json:"Status"`
	TTL             int    `json:"TTL"`
	Type            string `json:"Type"`
	UpdateTimestamp int64  `json:"UpdateTimestamp"`
	Value           string `json:"Value"`
	Weight          int    `json:"Weight"`
}

// RecordsResponse 表示API的响应结构
type RecordsResponse struct {
	DomainRecords struct {
		Record []Record `json:"Record"`
	} `json:"DomainRecords"`
	PageNumber int    `json:"PageNumber"`
	PageSize   int    `json:"PageSize"`
	RequestId  string `json:"RequestId"`
	TotalCount int    `json:"TotalCount"`
}

func (d *aliApi) GetDomainRecord(domainName, hostRecord, project string) *Record {
	client, err := d.CreateClient(project)
	if err != nil {
		logger.Warn("create client error", err)
		return nil
	}

	params := d.ApiInfo("DescribeDomainRecords")
	// query params
	queries := map[string]interface{}{}
	queries["DomainName"] = tea.String(domainName)
	queries["KeyWord"] = tea.String(hostRecord)
	// runtime options
	runtime := &util.RuntimeOptions{}
	request := &openapi.OpenApiRequest{
		Query: openapiutil.Query(queries),
	}
	// 复制代码运行请自行打印 API 的返回值
	// 返回值实际为 Map 类型，可从 Map 中获得三类数据：响应体 body、响应头 headers、HTTP 返回的状态码 statusCode。
	response, err := client.CallApi(params, request, runtime)
	if err != nil {
		logger.Warn("call api error", err)
		return nil
	}

	// 检查状态码
	statusCode, ok := response["statusCode"].(json.Number)
	if !ok || statusCode != "200" {
		logger.Errorf("Invalid response status code: %v", statusCode)
		return nil
	}

	// 解析返回值
	if body, ok := response["body"].(map[string]interface{}); ok {
		bodyBytes, err := json.Marshal(body)
		if err != nil {
			logger.Errorf("Failed to marshal body: %v", err)
			return nil
		}
		var result RecordsResponse
		if err := json.Unmarshal(bodyBytes, &result); err != nil {
			logger.Errorf("Failed to unmarshal response: %v", err)
			return nil
		}
		// 打印解析后的记录
		// for _, record := range result.DomainRecords.Record {
		// 	logger.Info("Domain Record: %+v", record)
		// }
		if len(result.DomainRecords.Record) > 0 {
			for _, record := range result.DomainRecords.Record {
				if record.RR == hostRecord {
					return &record
				}
			}
		}
	}
	return nil
}

type UpdateDomainResp struct {
	RequestID string `json:"RequestId"`
	RecordID  string `json:"RecordId"`
}

func (d *aliApi) UpdateDomainRecord(recordID, hostRecord, cnameRecord, project string) *UpdateDomainResp {
	client, err := d.CreateClient(project)
	if err != nil {
		logger.Warn("create client error", err)
		return nil
	}
	params := d.ApiInfo("UpdateDomainRecord")

	queries := map[string]interface{}{}
	queries["RecordId"] = tea.String(recordID)
	queries["RR"] = tea.String(hostRecord)
	queries["Type"] = tea.String("CNAME")
	queries["Value"] = tea.String(cnameRecord)
	// runtime options
	runtime := &util.RuntimeOptions{}
	request := &openapi.OpenApiRequest{
		Query: openapiutil.Query(queries),
	}
	response, err := client.CallApi(params, request, runtime)
	if err != nil {
		logger.Warn("call api error", err)
		return nil
	}
	// 检查状态码
	statusCode, ok := response["statusCode"].(json.Number)
	if !ok || statusCode != "200" {
		logger.Errorf("Invalid response status code: %v", statusCode)
		return nil
	}
	// 解析返回值
	if body, ok := response["body"].(map[string]interface{}); ok {
		bodyBytes, err := json.Marshal(body)
		if err != nil {
			logger.Errorf("Failed to marshal body: %v", err)
			return nil
		}
		var result UpdateDomainResp
		if err := json.Unmarshal(bodyBytes, &result); err != nil {
			logger.Errorf("Failed to unmarshal response: %v", err)
			return nil
		}
		return &result
	}
	return nil
}

var projectAccessKeyMap = map[string]AccessKey{
	"dailyyoga": {
		AccessKeyID:     "LTAI5t6T45oAfNrcZDbtxwZr",
		AccessKeySecret: "******************************",
	},
	"fitness": {
		AccessKeyID:     "LTAI5t6qmyZrGxeg4j5XAwYV",
		AccessKeySecret: "******************************",
	},
	"stretch": {
		AccessKeyID:     "LTAI5tNUdzsdFzwyTAc69sA8",
		AccessKeySecret: "******************************",
	},
}

type AccessKey struct {
	AccessKeyID     string
	AccessKeySecret string
}

// Description:
//
// 使用AK&SK初始化账号Client
//
// @return Client"
//
// @throws Exception
func (d *aliApi) CreateClient(project string) (result *openapi.Client, err error) {
	// 建议使用更安全的 STS 方式，更多鉴权访问方式请参见：https://help.aliyun.com/document_detail/378661.html。
	accessKey, ok := projectAccessKeyMap[project]
	if !ok {
		accessKey = projectAccessKeyMap["dailyyoga"]
	}
	config := &openapi.Config{
		// 必填，请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_ID。
		AccessKeyId: tea.String(accessKey.AccessKeyID),
		// 必填，请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_SECRET。
		AccessKeySecret: tea.String(accessKey.AccessKeySecret),
		// Endpoint 请参考 https://api.aliyun.com/product/Alidns
		Endpoint: tea.String("alidns.cn-hangzhou.aliyuncs.com"),
	}
	result, err = openapi.NewClient(config)
	return result, err
}

// @return OpenApi.Params
func (d *aliApi) ApiInfo(action string) (result *openapi.Params) {
	params := &openapi.Params{
		// 接口名称
		Action: tea.String(action),
		// 接口版本
		Version: tea.String("2015-01-09"),
		// 接口协议
		Protocol: tea.String("HTTPS"),
		// 接口 HTTP 方法
		Method:   tea.String("POST"),
		AuthType: tea.String("AK"),
		Style:    tea.String("RPC"),
		// 接口 PATH
		Pathname: tea.String("/"),
		// 接口请求体内容格式
		ReqBodyType: tea.String("json"),
		// 接口响应体内容格式
		BodyType: tea.String("json"),
	}
	result = params
	return result
}
