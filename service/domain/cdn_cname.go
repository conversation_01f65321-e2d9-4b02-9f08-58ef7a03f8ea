package domain

import (
	"errors"

	db "gitlab.dailyyoga.com.cn/rdc/serverops/database/domain"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
)

type cname struct{}

var Cname cname

type CnameItem struct {
	Remark string `json:"remark"`
	CName  string `json:"cname"`
}

type CDNDomainItem struct {
	ID          int64        `json:"id"`
	Remark      string       `json:"remark"`
	Project     string       `json:"project"`
	UpdateTimes string       `json:"update_times"`
	DomainName  string       `json:"domain_name"`
	HostRecord  string       `json:"host_record"`
	CnameRecord string       `json:"cname_record"`
	Domain      string       `json:"domain"`
	ThirdDomain []*CnameItem `json:"third_domain"`
}

func (c cname) GetCnameMap() map[int64][]*CnameItem {
	resp := make(map[int64][]*CnameItem)
	items := db.TbCDNDomainCname.GetList()
	for _, item := range items {
		if _, ok := resp[item.DomainCdnID]; !ok {
			resp[item.DomainCdnID] = make([]*CnameItem, 0)
		}
		resp[item.DomainCdnID] = append(resp[item.DomainCdnID], &CnameItem{
			Remark: item.Remark,
			CName:  item.CName,
		})
	}
	return resp
}

func (c cname) UpdateDomainRecord(id int64, cname string) error {
	domain := db.TbCDNDomain.GetItemByID(id)
	if domain == nil {
		logger.Warn("domain not found")
		return errors.New("domain not found")
	}
	list := db.TbCDNDomainCname.GetListByDCdnID(id)
	var isOk bool
	for k := range list {
		dRecord := list[k]
		if dRecord.CName == cname {
			isOk = true
			break
		}
	}
	if !isOk {
		return errors.New("cname not found")
	}
	resp := AliApi.UpdateDomainRecord(domain.RecordID, domain.HostRecord, cname, domain.Project)
	if resp == nil {
		return errors.New("update domain record failed")
	}
	if resp.RecordID != domain.RecordID {
		domain.RecordID = resp.RecordID
		if err := domain.Update(); err != nil {
			logger.Warn("update domain record failed, err: %v", err)
		}
	}
	return nil
}
