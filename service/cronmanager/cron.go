package cronmanager

import (
	"bytes"
	"context"
	"crypto/md5"
	_ "embed"
	"encoding/json"
	"fmt"
	"strings"
	"text/template"
	"time"

	"github.com/pkg/errors"
	cmdb "gitlab.dailyyoga.com.cn/rdc/serverops/database/cronmanager"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/go-artifact/microsvr"
	"gitlab.dailyyoga.com.cn/server/go-artifact/wework/robot"
	"go.etcd.io/etcd/api/v3/mvccpb"
	clientv3 "go.etcd.io/etcd/client/v3"
)

type Instance struct {
	config *config
	etcd   *clientv3.Client

	services map[string][]*ServiceInfo
}

var instance *Instance

//go:embed alarm.tpl
var alarmTemplate []byte

type templateInfo struct {
	ServiceName string
	ServiceEnv  string
	TaskName    string
	TaskSpec    string
	StartTime   string
	EndTime     string
	ErrorInfo   string
}

func GetInstance() *Instance {
	return instance
}

func (m *Instance) GetServices() map[string][]*ServiceInfo {
	if m == nil {
		return nil
	}
	return m.services
}

func Init(options ...Option) error {
	instance = &Instance{
		config:   &config{endpoints: make([]string, 0)},
		services: make(map[string][]*ServiceInfo),
	}
	for _, option := range options {
		option(instance.config)
	}

	if len(instance.config.endpoints) == 0 {
		return errors.New("[cronmanager]etcd endpoints is empty")
	}
	var err error
	instance.etcd, err = clientv3.New(clientv3.Config{
		Endpoints: instance.config.endpoints, DialTimeout: time.Second * 50,
	})
	if err != nil {
		return err
	}

	ctx := context.Background()
	instance.watchServiceChange(ctx)
	instance.watchTaskChange(ctx)

	microsvr.RegisterStop(func(micro *microsvr.MicroSvr) {
		if instance != nil && instance.etcd != nil {
			logger.Infof("[cronmanager]close etcd client")
			_ = instance.etcd.Close()
		}
	})
	return nil
}

func (m *Instance) watchServiceChange(ctx context.Context) {
	parseServiceInfo := func(value []byte) *ServiceInfo {
		if len(value) == 0 {
			return nil
		}
		info := &ServiceInfo{}
		if err := json.NewDecoder(bytes.NewReader(value)).Decode(info); err != nil {
			logger.Errorf("[cronmanager]parse service value failed, err: %s", err)
			return nil
		}
		return info
	}

	add := func(kv *mvccpb.KeyValue) {
		info := parseServiceInfo(kv.Value)
		if info == nil {
			return
		}
		if _, ok := m.services[info.ID]; !ok {
			m.services[info.ID] = make([]*ServiceInfo, 0)
		}
		m.services[info.ID] = append(m.services[info.ID], info)
	}

	del := func(kv *mvccpb.KeyValue) {
		if len(kv.Value) == 0 {
			// delete from etcd key
			for service, pods := range m.services {
				newData := make([]*ServiceInfo, 0)
				for _, pod := range pods {
					key := fmt.Sprintf("%x", md5.Sum([]byte(fmt.Sprintf("%s:%s", pod.ID, pod.Hostname))))
					if strings.HasSuffix(string(kv.Key), key) {
						continue
					}
					newData = append(newData, pod)
				}
				m.services[service] = newData
			}
			return
		}

		info := parseServiceInfo(kv.Value)
		if info == nil {
			return
		}
		if _, ok := m.services[info.ID]; !ok {
			return
		}
		if len(m.services[info.ID]) == 1 && m.services[info.ID][0].Hostname == info.Hostname {
			delete(m.services, info.ID)
		} else {
			newData := make([]*ServiceInfo, 0)
			for _, v := range m.services[info.ID] {
				if v.Hostname == info.Hostname {
					continue
				}
				newData = append(newData, v)
			}
			m.services[info.ID] = newData
		}
	}

	go m.watch(ctx, "/h2/cron/service/pods/", add, del, true)
}

func (m *Instance) getServiceAndTask(service, hostname, taskID string) (*ServiceInfo, *TaskInfo) {
	pods, ok := m.services[service]
	if !ok {
		return nil, nil
	}
	for _, item := range pods {
		if item.Hostname == hostname {
			if taskID != "" {
				if task, ok := item.Tasks[taskID]; ok {
					return item, task
				}
			} else {
				return item, nil
			}
		}
	}
	return nil, nil
}

func (m *Instance) watchTaskChange(ctx context.Context) {
	parseRunnerInfo := func(value []byte) *runnerInfo {
		info := &runnerInfo{}
		if err := json.NewDecoder(bytes.NewReader(value)).Decode(info); err != nil {
			logger.Errorf("[cronmanager]parse value failed, err: %s", err)
			return nil
		}
		return info
	}

	add := func(kv *mvccpb.KeyValue) {
		info := parseRunnerInfo(kv.Value)
		if info == nil || (info.Status != RunnerStatusSuccess && info.Status != RunnerStatusFailed) {
			return
		}
		service, task := m.getServiceAndTask(info.ServiceID, info.Hostname, info.TaskID)
		if service == nil || task == nil {
			return
		}
		// alarm
		if info.Status == RunnerStatusFailed && !task.AlarmDisabled {
			m.alarmRunFailed(service, task, info)
		}

		// save log
		taskLog := &cmdb.Log{
			ServiceName:   service.Name,
			ServiceEnv:    service.Env,
			ServiceID:     service.ID,
			Hostname:      service.Hostname,
			TaskName:      task.Name,
			TaskID:        task.ID,
			TaskSpec:      task.Spec,
			TaskDesc:      task.Desc,
			StartTime:     info.StartTime,
			EndTime:       info.EndTime,
			Status:        info.Status,
			ErrorInfo:     info.ErrorInfo,
			AlarmDisabled: 0,
		}
		if task.AlarmDisabled {
			taskLog.AlarmDisabled = 1
		}
		if err := taskLog.Save(); err != nil {
			logger.Errorf("[cronmanager]save log failed, err: %s", err)
		}
	}

	del := func(kv *mvccpb.KeyValue) {
		return
	}

	go m.watch(ctx, "/h2/cron/service/task/", add, del, false)
}

func (m *Instance) watch(ctx context.Context, key string, add, del func(kv *mvccpb.KeyValue), withGet bool) {
	logger.Infof("[cronmanager]watch values with key prefix: %s", key)
	if withGet {
		resp, err := m.etcd.Get(ctx, key, clientv3.WithPrefix())

		if err == nil && len(resp.Kvs) > 0 {
			for _, kv := range resp.Kvs {
				logger.Infof("[cronmanager]exist data, key: %s, value: %s", string(kv.Key), string(kv.Value))
				add(kv)
			}
		}
	}

	rch := m.etcd.Watch(ctx, key, clientv3.WithPrefix(), clientv3.WithPrevKV())
	for n := range rch {
		for _, ev := range n.Events {
			if ev.Type == clientv3.EventTypePut {
				logger.Infof("[cronmanager]put event, key: %s, value: %s", string(ev.Kv.Key), string(ev.Kv.Value))
				add(ev.Kv)
			} else if ev.Type == clientv3.EventTypeDelete {
				logger.Infof("[cronmanager]delete event, key: %s, value: %s", string(ev.Kv.Key), string(ev.Kv.Value))
				del(ev.Kv)
			}
		}
	}
}

func (m *Instance) alarmRunFailed(service *ServiceInfo, task *TaskInfo, info *runnerInfo) {
	alarmWebhook := "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=e0224630-75cf-4587-8b87-8800f64ad726"

	templateData := &templateInfo{
		ServiceName: service.Name,
		ServiceEnv:  service.Env,
		TaskName:    task.Name,
		TaskSpec:    task.Spec,
		StartTime:   time.Unix(info.StartTime, 0).Format("2006-01-02 15:04:05"),
		EndTime:     time.Unix(info.EndTime, 0).Format("2006-01-02 15:04:05"),
		ErrorInfo:   info.ErrorInfo,
	}

	tpl, err := template.New("crontask_alarm").Parse(string(alarmTemplate))
	if err != nil {
		logger.Errorf("[cronmanager]parse alarm template failed, err: %s", err)
		return
	}

	var buf bytes.Buffer
	if err := tpl.Execute(&buf, templateData); err != nil {
		logger.Errorf("[cronmanager]execute alarm template failed, err: %s", err)
		return
	}

	robot.Get().AddMessage(&robot.Message{
		RobotURL: alarmWebhook,
		MsgType:  robot.Markdown,
		Markdown: &robot.MarkdownMessage{
			Content: buf.String(),
		},
	})
}
