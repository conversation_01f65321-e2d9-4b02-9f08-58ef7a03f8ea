package cronmanager

import (
	"crypto/md5"
	"fmt"
)

type ServiceInfo struct {
	ID       string               `json:"service_id"`
	Name     string               `json:"service_name"`
	Env      string               `json:"env"`
	Hostname string               `json:"hostname"`
	IP       string               `json:"ip"`
	Tasks    map[string]*TaskInfo `json:"tasks"`
}

func (s *ServiceInfo) buildKey() string {
	return fmt.Sprintf("%x",
		md5.Sum([]byte(fmt.Sprintf("%s:%s", s.ID, s.Hostname))))
}

type TaskInfo struct {
	Name          string       `json:"task_name"`
	ID            string       `json:"task_id"`
	Spec          string       `json:"task_spec"`
	Desc          string       `json:"task_desc"`
	AlarmDisabled bool         `json:"alarm_disabled"`
	Runner        func() error `json:"-"`
}

type runnerInfo struct {
	ServiceID string `json:"service_id"`
	Hostname  string `json:"hostname"`
	TaskID    string `json:"task_id"`
	StartTime int64  `json:"start_time"`
	EndTime   int64  `json:"end_time"`
	Status    int    `json:"status"`
	ErrorInfo string `json:"error_info"`
}

func (r *runnerInfo) buildKey() string {
	return fmt.Sprintf("%x",
		md5.Sum([]byte(fmt.Sprintf("%s:%s:%s", r.ServiceID, r.Hostname, r.TaskID))))
}

const RunnerStatusSuccess = 2
const RunnerStatusFailed = 3
