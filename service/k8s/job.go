package k8s

import (
	"encoding/json"
	"net/http"
	"strconv"
	"time"

	"gitlab.dailyyoga.com.cn/rdc/serverops/database/deployment"

	batchv1 "k8s.io/api/batch/v1"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
)

const BackoffLimit = 0
const ActiveDeadlineSeconds = 7200

type TriggerData struct {
	PushData   PushData `json:"push_data"`
	Repository RepoInfo `json:"repository"`
}
type PushData struct {
	Digest   string `json:"digest"`
	PushedAt string `json:"pushed_at"`
	Tag      string `json:"tag"`
}
type RepoInfo struct {
	DateCreated            string `json:"date_created"`
	Name                   string `json:"name"`
	Namespace              string `json:"namespace"`
	Region                 string `json:"region"`
	RepoAuthenticationType string `json:"repo_authentication_type"`
	RepoFullName           string `json:"repo_full_name"`
	RepoOriginType         string `json:"repo_origin_type"`
	RepoType               string `json:"repo_type"`
}

// GetJobClient 生成用于创建Job的client
func GetJobClient() *kubernetes.Clientset {
	clientset := getClientSet(deployment.ServerlessCluster)
	return clientset
}

// DecoderReq 将HTTP请求body中数据解析到结构体TriggerData
func DecoderReq(w http.ResponseWriter, r *http.Request) TriggerData {
	decoder := json.NewDecoder(r.Body)
	var t TriggerData
	err := decoder.Decode(&t)
	if err != nil {
		http.Error(w, "Bad Request", http.StatusBadRequest)
	}
	return t
}

// GenerateJobInfo 通过解析TriggerData，生成唯一的Job信息
func GenerateJobInfo(w http.ResponseWriter, r *http.Request) *batchv1.Job {
	t := DecoderReq(w, r)
	currentTime := time.Now().Unix()
	currentTimeStr := strconv.Itoa(int(currentTime))
	jobName := "android-build" + "-" + t.PushData.Tag + "-" + currentTimeStr
	// 创建 Job 对象
	job := &batchv1.Job{
		ObjectMeta: metav1.ObjectMeta{
			Name:      jobName,
			Namespace: "default",
		},
		Spec: batchv1.JobSpec{
			ActiveDeadlineSeconds: int64Ptr(ActiveDeadlineSeconds),
			Template: v1.PodTemplateSpec{
				Spec: v1.PodSpec{
					Volumes: []v1.Volume{
						{
							Name: "gradle-cache",
							VolumeSource: v1.VolumeSource{
								PersistentVolumeClaim: &v1.PersistentVolumeClaimVolumeSource{
									ClaimName: "gradle-cache",
								},
							},
						},
					},
					Containers: []v1.Container{
						{
							Name:            "android-build",
							Image:           "registry-vpc.cn-hangzhou.aliyuncs.com/dailyyoga/android-build:" + t.PushData.Tag,
							ImagePullPolicy: "Always",
							Env: []v1.EnvVar{
								{
									Name:  "env",
									Value: t.PushData.Tag,
								},
							},
							Resources: v1.ResourceRequirements{
								Requests: v1.ResourceList{
									v1.ResourceCPU:    resource.MustParse("32"),
									v1.ResourceMemory: resource.MustParse("16Gi"),
								},
							},
							VolumeMounts: []v1.VolumeMount{
								{
									Name:      "gradle-cache",
									MountPath: "/gradle/",
								},
							},
						},
					},
					RestartPolicy: "Never",
				},
			},
			BackoffLimit: int32Ptr(BackoffLimit),
		},
	}
	return job
}

// 辅助函数，将 int32 转换为指针类型
func int32Ptr(i int32) *int32 { return &i }
func int64Ptr(i int64) *int64 { return &i }
