package k8s

import (
	"fmt"

	"gitlab.dailyyoga.com.cn/rdc/serverops/database/deployment"
)

type K8s struct {
	clusterName deployment.ClusterName
	namespace   deployment.KubernetesNamespace
}

var gK8sClient map[string]*K8s

func New(clusterName deployment.ClusterName, namespace deployment.KubernetesNamespace) *K8s {
	key := fmt.Sprintf("%s-%s", clusterName, namespace)
	if gK8sClient == nil {
		gK8sClient = make(map[string]*K8s)
	}
	if _, ok := gK8sClient[key]; !ok {
		gK8sClient[key] = &K8s{
			clusterName: clusterName,
			namespace:   namespace,
		}
	}
	return gK8sClient[key]
}
