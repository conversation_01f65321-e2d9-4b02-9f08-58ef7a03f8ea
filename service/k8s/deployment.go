package k8s

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"gitlab.dailyyoga.com.cn/rdc/serverops/database/deployment"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	appsv1 "k8s.io/api/apps/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	v1 "k8s.io/client-go/kubernetes/typed/apps/v1"
	"k8s.io/client-go/util/retry"
)

type DeploymentClient struct {
	c v1.DeploymentInterface
}

func (k *K8s) GetDeploymentClient() *DeploymentClient {
	if k.clusterName == "" || k.namespace == "" {
		return nil
	}
	return getAppV1DeploymentClient(k.clusterName, k.namespace)
}

func getAppV1DeploymentClient(clusterName deployment.ClusterName,
	namespace deployment.KubernetesNamespace) *DeploymentClient {
	client := &DeploymentClient{}
	if clientset := getClientSet(clusterName); clientset != nil {
		client.c = clientset.AppsV1().Deployments(string(namespace))
	}
	return client
}

func (dc *DeploymentClient) Restart(ctx context.Context, name string) error {
	if dc == nil {
		return errors.New("client is nil")
	}
	retryErr := retry.RetryOnConflict(retry.DefaultRetry, func() error {
		result, err := dc.c.Get(ctx, name, metav1.GetOptions{})
		if err != nil {
			return errors.Errorf("failed to get latest version of deployment err: %v", err)
		}

		if result.Spec.Paused {
			return errors.New("can't restart paused deployment")
		}

		if result.Spec.Template.ObjectMeta.Annotations == nil {
			result.Spec.Template.ObjectMeta.Annotations = make(map[string]string)
		}
		result.Spec.Template.ObjectMeta.Annotations["kubectl.kubernetes.io/restartedAt"] = time.Now().Format(time.RFC3339)

		_, err = dc.c.Update(ctx, result, metav1.UpdateOptions{})
		return err
	})
	if retryErr != nil {
		return errors.Errorf("restart deployment failed. deployment: %s, error: %v", name, retryErr)
	}
	return nil
}

func (dc *DeploymentClient) Get(ctx context.Context, name string) (*appsv1.Deployment, error) {
	if dc == nil {
		return nil, errors.New("client is nil")
	}
	return dc.c.Get(ctx, name, metav1.GetOptions{})
}

type DeploymentInfo struct {
	Name       string   `json:"name"`
	Namespace  string   `json:"namespace"`
	Generation int64    `json:"generation"`
	Replicas   *int32   `json:"replicas"`
	Paused     bool     `json:"paused"`
	Pod        *PodItem `json:"pods"`
}

func (dc *DeploymentClient) Lists(ctx context.Context) []*DeploymentInfo {
	lists, err := dc.c.List(ctx, metav1.ListOptions{})
	if err != nil {
		logger.Error(err)
		return nil
	}

	infos := make([]*DeploymentInfo, 0)
	for i := range lists.Items {
		v := lists.Items[i]
		item := &DeploymentInfo{
			Name:       v.Name,
			Namespace:  v.Namespace,
			Generation: v.Generation,
			Replicas:   v.Spec.Replicas,
			Paused:     v.Spec.Paused,
			Pod: &PodItem{
				Name:       v.Spec.Template.Name,
				Containers: make([]ContainerItem, 0),
			},
		}
		for i := range v.Spec.Template.Spec.InitContainers {
			val := v.Spec.Template.Spec.InitContainers[i]
			item.Pod.Containers = append(item.Pod.Containers, ContainerItem{
				InitContainer: true,
				Name:          val.Name,
				Image:         val.Image,
			})
		}
		for i := range v.Spec.Template.Spec.Containers {
			val := v.Spec.Template.Spec.Containers[i]
			item.Pod.Containers = append(item.Pod.Containers, ContainerItem{
				Name: val.Name, Image: val.Image,
			})
		}
		infos = append(infos, item)
	}
	return infos
}
