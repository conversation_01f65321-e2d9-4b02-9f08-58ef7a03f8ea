package k8s

import (
	_ "embed" // justifying it

	"github.com/pkg/errors"
	"gitlab.dailyyoga.com.cn/rdc/serverops/database/deployment"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
	"k8s.io/client-go/tools/clientcmd/api"
)

//go:embed kubeconfig/dev-cluster.kube.json
var devClusterKubeConfig []byte

//go:embed kubeconfig/prod-cluster.kube.json
var prodClusterKubeConfig []byte

//go:embed kubeconfig/serverless-cluster.kube.json
var serverlessClusterKubeConfig []byte

var clusterClientSet map[deployment.ClusterName]*kubernetes.Clientset

func InitClients() {
	initfns := []func() error{
		initClientSet,
	}

	for _, fn := range initfns {
		if err := fn(); err != nil {
			logger.Error(err)
		}
	}
}

func getClusterConfig(cluster deployment.ClusterName) (*rest.Config, error) {
	var data []byte
	if cluster == deployment.ProdCluster {
		data = prodClusterKubeConfig
	} else if cluster == deployment.ServerlessCluster {
		data = serverlessClusterKubeConfig
	} else {
		data = devClusterKubeConfig
	}
	if len(data) == 0 {
		return nil, errors.New("kube config is empty")
	}
	conf, err := clientcmd.BuildConfigFromKubeconfigGetter("", func() (*api.Config, error) {
		return clientcmd.Load(data)
	})
	if err != nil {
		return nil, err
	}
	return conf, nil
}

func initClientSet() error {
	if clusterClientSet == nil {
		clusterClientSet = make(map[deployment.ClusterName]*kubernetes.Clientset)
	}
	clusters := []deployment.ClusterName{deployment.DevCluster, deployment.ProdCluster, deployment.ServerlessCluster}

	for _, cluster := range clusters {
		if _, ok := clusterClientSet[cluster]; ok {
			continue
		}
		conf, err := getClusterConfig(cluster)
		if err != nil {
			return err
		}
		clientset, err := kubernetes.NewForConfig(conf)
		if err != nil {
			return err
		}
		clusterClientSet[cluster] = clientset
	}
	return nil
}

func getClientSet(cluster deployment.ClusterName) *kubernetes.Clientset {
	if clientset, ok := clusterClientSet[cluster]; ok {
		return clientset
	}
	return nil
}
