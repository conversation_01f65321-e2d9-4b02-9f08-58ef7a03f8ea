package qiniu

import (
	"github.com/qiniu/go-sdk/v7/auth"
	"github.com/qiniu/go-sdk/v7/auth/qbox"
	"github.com/qiniu/go-sdk/v7/storage"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
)

type Storage struct {
	accessKey     string
	secretKey     string
	mac           *qbox.Mac
	bucketManager *storage.BucketManager
}

func (s *Storage) GetMac() *qbox.Mac {
	return s.mac
}

func NewStorage() *Storage {
	s := &Storage{
		accessKey: "hMKiHX5MycpVhnXDpexfdl5oJlslkYIAbJTGJguJ",
		secretKey: "mBwc37b2qZqh9Ey9LO5BT82vPv5J6VFJZooVSwp9",
	}
	storageConf := &storage.Config{UseHTTPS: false}
	s.mac = auth.New(s.accessKey, s.secretKey)
	s.bucketManager = storage.NewBucketManager(s.mac, storageConf)
	return s
}

type listOptions struct {
	prefix    string
	delimiter string
	marker    string
	limit     int
}

type ListOption func(o *listOptions)

func WithListOptionPrefix(prefix string) ListOption {
	return func(o *listOptions) {
		o.prefix = prefix
	}
}

func WithListOptionDelimiter(delimiter string) ListOption {
	return func(o *listOptions) {
		o.delimiter = delimiter
	}
}

func WithListOptionMarker(marker string) ListOption {
	return func(o *listOptions) {
		o.marker = marker
	}
}

func WithListOptionLimit(limit int) ListOption {
	return func(o *listOptions) {
		o.limit = limit
	}
}

type ListFile struct {
	Items          []storage.ListItem
	CommonPrefixes []string
}

func (s *Storage) List(bucket string, options ...ListOption) ListFile {
	data := ListFile{
		Items:          make([]storage.ListItem, 0),
		CommonPrefixes: make([]string, 0),
	}

	option := listOptions{limit: 1000}
	for _, opt := range options {
		opt(&option)
	}

	for {
		entries, commonPrefixes, nextMarker, hasNext, err :=
			s.bucketManager.ListFiles(bucket, option.prefix, option.delimiter, option.marker, option.limit)
		if err != nil {
			logger.Errorf("list qiniu cloud bucket object failed, err: %s", err)
			break
		}
		if len(commonPrefixes) > 0 {
			data.CommonPrefixes = append(data.CommonPrefixes, commonPrefixes...)
		}
		for _, entry := range entries {
			data.Items = append(data.Items, entry)
		}
		if hasNext {
			option.marker = nextMarker
		} else {
			break
		}
	}
	return data
}
