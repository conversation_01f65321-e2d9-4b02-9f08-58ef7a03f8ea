package apppackage

import (
	"archive/zip"
	"context"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"strings"

	"github.com/pkg/errors"
	"k8s.io/utils/strings/slices"
)

type size struct{}

var Size size

var iOSResourceExt = []string{
	".webp", ".jpg", ".jpeg", ".png", ".gif", ".cr2", ".tif", ".bmp", ".heif", ".jxr", ".psd", ".ico", ".dwg", ".avif", // audio
	".mov", ".mp4", ".m4v", ".webm", ".avi", ".mkv", ".wmv", ".mpg", ".flv", ".3gp", // video
	".mp3", ".wav", ".aac", ".ogg", ".mid", ".m4a", ".flac", ".amr", ".aiff", // audio
	".epub", ".zip", ".tar", ".rar", ".gz", ".bz2", ".7z", ".xz", ".pdf", ".zstd", ".exe", ".xz", ".swf", ".rtf",
	".iso", ".eot", ".ps", ".sqlite", ".nex", ".crx", ".cab", ".deb", ".ar", ".Z", ".lz", ".rpm", ".elf", ".dcm", // archive
	".doc", ".docx", ".xls", ".xlsx", ".csv", ".ppt", ".pptx", // documents
	".woff", ".woff2", ".ttf", ".otf", // font
	".wasm", ".dex", ".dey", // application
	".json", ".js", ".html", ".htm", // web document
}

type androidAPKSizeInfo struct {
	All      int64 `json:"all"`
	Assets   int64 `json:"assets"`
	Lib      int64 `json:"lib"`
	Res      int64 `json:"res"`
	DexFiles int64 `json:"dex_files"`
}

type iOSIPASizeInfo struct {
	All           int64 `json:"all"`
	Assets        int64 `json:"assets"`
	FlutterAssets int64 `json:"flutter_assets"`
	Resource      int64 `json:"resource"`
	Frameworks    int64 `json:"frameworks"`
	Binary        int64 `json:"binary"`
}

type file struct {
	DirName string
	ExtName string
	Name    string
	Size    int64
}

type SizeInfo struct {
	Name    string
	Files   []*file
	APKInfo *androidAPKSizeInfo
	IPAInfo *iOSIPASizeInfo
}

func (s *size) GetSizeInfo(packageFile string) (*SizeInfo, error) {
	local := packageFile
	if s.isRemotePackage(packageFile) {
		local = filepath.Base(packageFile)
		if err := s.downloadRemotePackage(packageFile, local); err != nil {
			return nil, err
		}
		defer func() {
			_ = os.Remove(local)
		}()
	}
	if _, err := os.Stat(local); os.IsNotExist(err) {
		return nil, err
	}

	files, err := s.extract(local)
	if err != nil {
		return nil, err
	}

	data := &SizeInfo{Name: local, Files: files, APKInfo: nil, IPAInfo: nil}
	switch filepath.Ext(local) {
	case ".apk":
		data.APKInfo = s.statsAPK(files)
	case ".ipa":
		data.IPAInfo = s.statsIPA(files)
	default:
	}
	return data, nil
}

func (s *size) statsAPK(files []*file) *androidAPKSizeInfo {
	sizeInfo := &androidAPKSizeInfo{}
	for _, item := range files {
		sizeInfo.All += item.Size
		if strings.HasPrefix(item.DirName, "assets") {
			sizeInfo.Assets += item.Size
		}
		if strings.HasPrefix(item.DirName, "lib") {
			sizeInfo.Lib += item.Size
		}
		if strings.HasPrefix(item.DirName, "res") {
			sizeInfo.Res += item.Size
		}
		if item.ExtName == ".dex" {
			sizeInfo.DexFiles += item.Size
		}
	}
	return sizeInfo
}

func (s *size) statsIPA(files []*file) *iOSIPASizeInfo {
	sizeInfo := &iOSIPASizeInfo{}
	if len(files) == 0 {
		return sizeInfo
	}
	// fetch path prefix and binary filename
	binaryFilename := ""
	pathPrefix := ""
	for _, item := range files {
		if strings.Contains(item.DirName, ".app") {
			binaryFilename = strings.ReplaceAll(strings.Split(item.DirName, ".app")[0], "Payload/", "")
			pathPrefix = fmt.Sprintf("Payload/%s.app", binaryFilename)
			break
		}
	}

	if binaryFilename == "" || pathPrefix == "" {
		return sizeInfo
	}

	for _, item := range files {
		sizeInfo.All += item.Size
		if filepath.Base(item.Name) == "Assets.car" {
			sizeInfo.Assets += item.Size
		}
		if strings.HasPrefix(item.DirName, fmt.Sprintf("%s/Frameworks", pathPrefix)) {
			if strings.Contains(item.DirName, "flutter_assets") {
				sizeInfo.FlutterAssets += item.Size
			} else {
				sizeInfo.Frameworks += item.Size
			}
		}
		if item.DirName == pathPrefix && slices.Contains(iOSResourceExt, item.ExtName) {
			sizeInfo.Resource += item.Size
		}
		if filepath.Base(item.Name) == binaryFilename {
			sizeInfo.Binary = item.Size
		}
	}
	return sizeInfo
}

func (s *size) extract(packageFile string) ([]*file, error) {
	r, err := zip.OpenReader(packageFile)
	if err != nil {
		return nil, err
	}
	defer r.Close()

	files := make([]*file, 0)
	for _, item := range r.File {
		fileSize := item.FileInfo().Size()
		files = append(files, &file{
			Name: item.Name, Size: fileSize,
			ExtName: filepath.Ext(item.Name),
			DirName: filepath.Dir(item.Name),
		})
	}
	return files, nil
}

func (s *size) isRemotePackage(packageFile string) bool {
	ext := filepath.Ext(packageFile)
	return slices.Contains([]string{".ipa", ".apk"}, ext) &&
		(strings.HasPrefix(packageFile, "https://") || strings.HasPrefix(packageFile, "http://"))
}

func (s *size) downloadRemotePackage(remote, local string) error {
	if !(strings.HasPrefix(remote, "https://") || strings.HasPrefix(remote, "http://")) {
		return errors.Errorf("file %s is not remote file", remote)
	}

	fp, err := os.Create(local)
	if err != nil {
		return err
	}

	req, err := http.NewRequestWithContext(context.Background(), http.MethodGet, remote, http.NoBody)
	if err != nil {
		return err
	}

	client := http.Client{
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			req.URL.Opaque = req.URL.Path
			return nil
		},
	}

	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	if resp.StatusCode >= http.StatusBadRequest {
		return errors.Errorf("error downloading remote file (%s), status_code: %d", remote, resp.StatusCode)
	}
	if _, err = io.Copy(fp, resp.Body); err != nil {
		return err
	}
	if err := resp.Body.Close(); err != nil {
		return err
	}
	return fp.Close()
}
