package jenkins

import (
	"net/http"
	"net/url"
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
)

const jenkinsAddress = "http://admin:1172a4c2290bdac39454988f182a710a96@************:8080"
const jenkinsToken = "9pRFJQhYc6dfvCWHzcpmA25A1sYOx4L4IOiXQcka9inv1tsaP1KPp7MkaBXEIyTs" // nolint

type Job struct {
	Name            string
	Params          url.Values
	NeedFetchResult bool
	ResultHandler   func(result string)
}

type fetchItem struct {
	id      int
	job     string
	handler func(result string)
}

type jenkins struct {
	triggerJob chan *Job
	fetchLoop  chan *fetchItem
}

type buildResp struct {
	FullDisplayName string `json:"fullDisplayName"`
	Building        bool   `json:"building"`
	Number          int    `json:"number"`
	Result          string `json:"result"`
	Timestamp       int64  `json:"timestamp"`
}

var gJenkins *jenkins

func Init() {
	defaultPool := 10
	gJenkins = &jenkins{
		triggerJob: make(chan *Job, defaultPool),
		fetchLoop:  make(chan *fetchItem, defaultPool),
	}
	go run()
}

func AddJob(job *Job) {
	if job == nil {
		return
	}
	gJenkins.triggerJob <- job
}

func run() {
	sleepDuration := 10 * time.Second
	for {
		select {
		case job := <-gJenkins.triggerJob:
			logger.Infof("receive trigger job: %s, params: %s", job.Name, job.Params.Encode())
			var lastBuildID int
			if job.NeedFetchResult {
				last, code := fetchBuildInfo(job.Name, lastBuildID)
				if code != http.StatusOK {
					continue
				}
				if last != nil && last.Number > 0 {
					lastBuildID = last.Number + 1
				}
			}
			if ok := trigger(job.Name, job.Params); ok && lastBuildID > 0 {
				fetch := &fetchItem{
					job:     job.Name,
					id:      lastBuildID,
					handler: job.ResultHandler,
				}
				gJenkins.fetchLoop <- fetch
			}
		case fetchJob := <-gJenkins.fetchLoop:
			logger.Infof("receive fetch job: %s, build_id: %d", fetchJob.job, fetchJob.id)
			job := fetchJob.job
			id := fetchJob.id
			for {
				time.Sleep(sleepDuration)
				info, code := fetchBuildInfo(job, id)
				if code == http.StatusNotFound {
					info, code = fetchBuildInfo(job, id)
				}
				if info == nil {
					logger.Errorf("build exception; job: %s, id: %d", job, id)
					break
				}
				if info.Building {
					continue
				} else {
					logger.Infof("fetch code: %d, data: %+v", code, info)
					if fetchJob.handler != nil {
						go fetchJob.handler(info.Result)
					}
					break
				}
			}
		default:
			time.Sleep(sleepDuration)
		}
	}
}
