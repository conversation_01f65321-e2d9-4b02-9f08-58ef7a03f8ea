package jenkins

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
	"strconv"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
)

func fetchBuildInfo(job string, id int) (res *buildResp, code int) {
	build := "lastBuild"
	if id > 0 {
		build = strconv.Itoa(id)
	}
	infoURI := fmt.Sprintf("%s/job/%s/%s/api/json", jenkinsAddress, job, build)
	resp, err := http.Get(infoURI) //nolint
	if err != nil {
		logger.Error(err)
		return nil, 0
	}
	defer resp.Body.Close()

	logger.Infof("fetch build resp: %s, build: %s, status_code: %d", job, build, resp.StatusCode)
	if resp.StatusCode > http.StatusBadRequest || resp.StatusCode < http.StatusOK {
		return nil, resp.StatusCode
	}

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		logger.Error(err)
		return nil, resp.StatusCode
	}
	if err := json.Unmarshal(body, &res); err != nil {
		logger.Error(err)
		return nil, resp.StatusCode
	}
	return res, resp.StatusCode
}

func trigger(job string, params url.Values) bool {
	jobURL := jenkinsAddress + "/view/all/job/" + job
	if len(params) > 0 {
		jobURL += "/buildWithParameters?token=" + jenkinsToken + "&" + params.Encode()
	} else {
		jobURL += "/build?token=" + jenkinsToken
	}

	resp, err := http.Get(jobURL) // nolint
	if err != nil {
		logger.Error(err)
		return false
	}
	defer resp.Body.Close()

	logger.Infof("trigger Job:%s, params: %s, status_code: %d", job, params.Encode(), resp.StatusCode)
	return resp.StatusCode == http.StatusCreated || resp.StatusCode == http.StatusOK
}
