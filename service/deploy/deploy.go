package deploy

import (
	"context"
	"net/url"

	"github.com/pkg/errors"
	"gitlab.dailyyoga.com.cn/rdc/serverops/database/deployment"
	"gitlab.dailyyoga.com.cn/rdc/serverops/service/jenkins"
	"gitlab.dailyyoga.com.cn/rdc/serverops/service/k8s"
)

type channel struct{}

var Channel channel

func (c *channel) KubernetesDeployment(ctx context.Context, item *deployment.Mapping) {
	conf := item.KubernetesDeployParams
	if conf == nil {
		Notify.FailedMessage(item.ProjectName, item.ProjectBranch,
			errors.New("deployment config info was not found"))
		return
	}
	err := k8s.New(conf.ClusterName, conf.Namespace).GetDeploymentClient().Restart(ctx, conf.DeploymentName)
	if err != nil {
		Notify.FailedMessage(item.ProjectName, item.ProjectBranch, err)
		return
	}
	Notify.SuccessMessage(item.ProjectName, item.ProjectBranch)
}

func (c *channel) JenkinsJobDeployment(ctx context.Context, item *deployment.Mapping) {
	conf := item.JenkinsDeployParams
	if conf == nil {
		Notify.FailedMessage(item.ProjectName, item.ProjectBranch,
			errors.New("deployment config info was not found"))
		return
	}
	resultHandler := func(result string) {
		if result == "FAILURE" {
			Notify.FailedMessage(item.ProjectName, item.ProjectBranch,
				errors.New("jenkins job exec return FAILURE"))
		}
		if result == "SUCCESS" {
			Notify.SuccessMessage(item.ProjectName, item.ProjectBranch)
		}
	}

	params := url.Values{}
	for k, v := range conf.Params {
		params.Set(k, v)
	}

	jenkins.AddJob(&jenkins.Job{
		Name:            conf.JobName,
		Params:          params,
		NeedFetchResult: true,
		ResultHandler:   resultHandler,
	})
}

func (c *channel) GitlabCIDeployment(ctx context.Context, item *deployment.Mapping) {
	Notify.SuccessMessage(item.ProjectName, item.ProjectBranch)
}
