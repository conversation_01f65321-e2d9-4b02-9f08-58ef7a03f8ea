package deploy

import (
	"context"
	"fmt"
	"strings"

	"github.com/pkg/errors"
	"gitlab.dailyyoga.com.cn/rdc/serverops/database/deployment"
	"gitlab.dailyyoga.com.cn/rdc/serverops/database/wework"
	"gitlab.dailyyoga.com.cn/rdc/serverops/service/gitlab"
	wework2 "gitlab.dailyyoga.com.cn/rdc/serverops/service/wework"
	message2 "gitlab.dailyyoga.com.cn/rdc/serverops/service/wework/message"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/go-artifact/wework/robot"
)

type notify struct{}

var Notify notify

const publisherRobotURL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=48acbc93-5f6e-4452-9dbf-3e9b9951d873"

var projectEnvName = map[string]string{
	"release/dev":    "qa",
	"release/mirror": "mirror",
	"master":         "product",
}

func (n *notify) FailedMessage(project, ref string, err error) {
	message := fmt.Sprintf(`> %s 项目 %s 分支部署结果：<font color="warning">FAILED</font> 
>错误信息：%s`,
		project, n.getProjectEnvName(ref), err.Error())
	n.addMessage(message)
}

func (n *notify) SuccessMessage(project, ref string) {
	message := fmt.Sprintf(`> %s 项目 %s 分支部署结果：<font color="info">SUCCESS</font>`,
		project, n.getProjectEnvName(ref))
	n.addMessage(message)
}

func (n *notify) ConfirmMessage(ctx context.Context,
	info *deployment.Mapping, payload *gitlab.MergeRequestPayload, qaName, taskID string) error {
	// 发送企业微信按钮选项卡消息，需要处理后续回调
	user, err := wework.TbUsers.GetItemByName(wework2.ServeropsAgentID, qaName)
	if err != nil {
		return errors.Errorf("qa info not found. qa: %s, err: %v", qaName, err)
	}
	if user == nil {
		return errors.Errorf("the QA wework info not found in app user list, please sync new list")
	}

	// new record
	bean := wework.MessageRecord{
		TaskID: taskID, MsgType: "template_card",
		RelatedObject: &wework.RelatedObject{
			MappingID: info.ID, MergeRequestIID: payload.ObjectAttributes.IID,
		},
	}
	if err := bean.Save(); err != nil {
		return err
	}

	// send message and update record
	title := fmt.Sprintf("%s 项目 %s 分支部署请求", info.ProjectName, info.ProjectBranch)
	if env, ok := projectEnvName[info.ProjectBranch]; ok {
		title = fmt.Sprintf("%s 项目 %s 环境部署请求", info.ProjectName, env)
	}
	descriptions := []string{
		fmt.Sprintf("上线信息： %s", payload.ObjectAttributes.Title),
		fmt.Sprintf("合并请求：%d", payload.ObjectAttributes.IID),
		fmt.Sprintf("提交开发：%s", payload.User.Name),
	}
	taskMessage := &message2.InteractiveTaskCardOptions{
		ToUser:  user.UserID,
		MsgType: "interactive_taskcard",
		AgentID: wework2.ServeropsAgentID,
		InteractiveTaskCard: message2.InteractiveTaskCard{
			Title:       title,
			Description: strings.Join(descriptions, "\n"),
			TaskID:      taskID,
			Button: []message2.InteractiveTaskCardButton{
				{Key: message2.TemplateCardButtonKeyConfirm, Name: "确 认"},
				{Key: message2.TemplateCardButtonKeyReject, Name: "驳 回", Color: "red"},
			},
		},
	}

	resp, err := message2.InteractiveTaskCardMessage(ctx, taskMessage)
	if err != nil {
		logger.Error(err)
	}
	if resp.ErrCode == 0 && resp.MsgID != "" {
		bean.MsgID = resp.MsgID
		bean.RespCode = resp.ResponseCode
		if err := bean.Update(); err != nil {
			logger.Error(err)
		}
	}
	return nil
}

func (n *notify) getProjectEnvName(ref string) string {
	if name, ok := projectEnvName[ref]; ok {
		return name
	}
	return ref
}

func (n *notify) addMessage(message string) {
	robot.Get().AddMessage(&robot.Message{
		RobotURL: publisherRobotURL,
		MsgType:  robot.Markdown,
		Markdown: &robot.MarkdownMessage{
			Content: message,
		},
	})
}
