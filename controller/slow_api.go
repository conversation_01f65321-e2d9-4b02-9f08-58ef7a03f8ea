package controller

import (
	"fmt"
	"sort"
	"strconv"
	"time"

	"gitlab.dailyyoga.com.cn/rdc/serverops/database/indicator"
	"gitlab.dailyyoga.com.cn/rdc/serverops/library"
	"gitlab.dailyyoga.com.cn/server/go-artifact/http"
)

type slowAPI struct{}

var SlowAPI slowAPI

type item struct {
	Name     string                                           `json:"name,omitempty"`
	Platform string                                           `json:"platform"`
	Service  string                                           `json:"service"`
	Date     int                                              `json:"date"`
	Stats    map[library.SlowApiStatRecordType]recordTypeData `json:"stats"`
}

type recordTypeData struct {
	Count      int64   `json:"count"`
	Percentage float64 `json:"percentage,omitempty"`
}

func (s *slowAPI) Lists(t *http.Tools) {
	req := &indicator.ApiStatSearchPayload{}

	if len(t.GetRequest().URL.Query()) > 0 {
		t.ParseBodyObject(req)
	}

	if req.Project == "" {
		req.Project = indicator.ProjectYoga
	}
	if req.Platform == "" {
		req.Platform = string(library.IndicatorPlatformH2)
	}
	if req.StartDate == 0 {
		req.StartDate, _ = strconv.Atoi(time.Now().AddDate(0, 0, -1).Format("20060102"))
	}
	if req.EndDate == 0 {
		req.EndDate, _ = strconv.Atoi(time.Now().AddDate(0, 0, -1).Format("20060102"))
	}

	res := make(map[int]map[string]*item)
	dates := make([]int, 0)

	// get all data
	totalRes := indicator.TbApiStatRecord.Search(req)
	for _, v := range totalRes {
		if v.Name == "" {
			continue
		}
		if _, ok := res[v.Date]; !ok {
			dates = append(dates, v.Date) // new date, push to sort
			res[v.Date] = make(map[string]*item)
		}
		if _, ok := res[v.Date][v.Name]; !ok {
			res[v.Date][v.Name] = &item{
				Date:     v.Date,
				Platform: v.Platform,
				Name:     v.Name,
				Service:  v.Service,
				Stats:    make(map[library.SlowApiStatRecordType]recordTypeData),
			}
		}
		if _, ok := res[v.Date][v.Name].Stats[v.RecordType]; !ok {
			res[v.Date][v.Name].Stats[v.RecordType] = recordTypeData{Count: v.Count}
		}
	}

	// sort date
	sort.Ints(dates)

	ret := make([]*item, 0)
	for _, v := range dates {
		for _, apiData := range res[v] {
			stats := make(map[library.SlowApiStatRecordType]recordTypeData)

			for recordType, val := range apiData.Stats {
				if recordType == library.SlowApiStatGT300 || recordType == library.SlowApiStatGT500 ||
					recordType == library.SlowApiStatGT500Upstream {
					count := float64(res[v][apiData.Name].Stats[library.SlowApiStatTotal].Count)
					if count > 0 {
						val.Percentage = s.convertFloat(float64(val.Count) / count)
					}
				}
				if _, ok := stats[recordType]; !ok {
					stats[recordType] = val
				}
			}
			ret = append(ret, &item{
				Name: apiData.Name, Platform: apiData.Platform, Date: apiData.Date,
				Service: apiData.Service,
				Stats:   stats,
			})
		}
	}
	t.Result(ret)
}

func (s *slowAPI) Summary(t *http.Tools) {
	req := &indicator.ApiStatSearchPayload{}
	if len(t.GetRequest().URL.Query()) > 0 {
		t.ParseBodyObject(req)
	}
	if req.Project == "" {
		req.Project = indicator.ProjectYoga
	}
	if req.Platform == "" {
		req.Platform = string(library.IndicatorPlatformH2)
	}
	if req.StartDate == 0 {
		req.StartDate, _ = strconv.Atoi(time.Now().AddDate(0, 0, -30).Format("20060102"))
	}
	if req.EndDate == 0 {
		req.EndDate, _ = strconv.Atoi(time.Now().AddDate(0, 0, -1).Format("20060102"))
	}

	res := make(map[int]*item)
	dates := make([]int, 0)

	summary := indicator.TbAPIStatSummary.Summary(req)
	if len(summary) == 0 {
		t.Result(res)
		return
	}

	// 计算其他类型的占比，需要先设置总数
	for _, v := range summary {
		if _, ok := res[v.Date]; !ok {
			dates = append(dates, v.Date) // new date, push sort slice
			res[v.Date] = &item{
				Platform: req.Platform,
				Date:     v.Date,
				Stats:    make(map[library.SlowApiStatRecordType]recordTypeData),
			}
		}
		if v.RecordType != library.SlowApiStatTotal {
			continue
		}
		if _, ok := res[v.Date].Stats[library.SlowApiStatTotal]; !ok {
			res[v.Date].Stats[library.SlowApiStatTotal] = recordTypeData{Count: v.Count}
		}
	}

	for _, v := range summary {
		if v.RecordType == library.SlowApiStatTotal {
			continue
		}
		if _, ok := res[v.Date]; !ok {
			continue
		}
		if _, ok := res[v.Date].Stats[library.SlowApiStatTotal]; !ok {
			continue
		}

		data := recordTypeData{
			Count: v.Count,
		}
		if v.RecordType == library.SlowApiStatGT300 || v.RecordType == library.SlowApiStatGT500 ||
			v.RecordType == library.SlowApiStatGT500Upstream {
			count := float64(res[v.Date].Stats[library.SlowApiStatTotal].Count)
			if count > 0 {
				data.Percentage = s.convertFloat(float64(v.Count) / count)
			}
		}
		// 处理曲线图异常图片
		if v.RecordType == library.SlowApiStatGT300 && (v.Date == 20231122 || v.Date == 20231123) {
			data.Count = 52252
		}
		res[v.Date].Stats[v.RecordType] = data
	}

	// sort by date
	sort.Ints(dates)
	ret := make([]*item, 0)
	for _, v := range dates {
		ret = append(ret, res[v])
	}
	t.Result(ret)
}

func (s *slowAPI) convertFloat(f float64) float64 {
	i, _ := strconv.ParseFloat(fmt.Sprintf("%.2f", f*100), 64) // nolint
	return i
}
