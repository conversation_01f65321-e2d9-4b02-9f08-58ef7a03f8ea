package controller

import (
	"bytes"
	"encoding/json"
	"fmt"
	"sort"
	"strings"

	"gitlab.dailyyoga.com.cn/rdc/serverops/database"
	"gitlab.dailyyoga.com.cn/server/go-artifact/http"
)

type configKeys struct{}

var ConfigKeys configKeys

func (c *configKeys) FigmaMcpToken(t *http.Tools) {
	account := t.GetFormStringD("account", "")
	if account == "" {
		t.Result(-1, "account is required")
		return
	}
	// 只支持 h2o 和 h2 两个账号
	if account != "h2o" && account != "h2" {
		t.Result(-1, "account is invalid")
		return
	}

	key := fmt.Sprintf("FIGMA_MCP_TOKEN_%s", strings.ToUpper(account))

	val := database.TbConfigKeys.Get(key)
	if val == nil {
		t.Result(-1, "查询错误，未找到配置")
		return
	}
	type tokenRet struct {
		Token string `json:"token"`
	}

	var ret tokenRet
	if err := json.NewDecoder(bytes.NewReader([]byte(val.Value))).Decode(&ret); err != nil {
		t.Result(-1, fmt.Sprintf("json decoded error: %s", err))
		return
	}
	t.Result(ret)
}

func (c *configKeys) Lists(t *http.Tools) {
	res := database.TbConfigKeys.FetchAll()
	ret := make([]*database.ConfigKeys, 0)
	for _, v := range res {
		if v.Key == "" {
			continue
		}
		ret = append(ret, v)
	}
	// 固定排序，避免每一次输出结果顺序都会变
	sort.Slice(ret, func(i, j int) bool {
		return ret[i].Key > ret[j].Key
	})
	t.Result(ret)
}

func (c *configKeys) Update(t *http.Tools) {
	req := &database.ConfigKeys{}
	t.ParseBodyObject(req)
	if req.Key == "" || req.Value == "" {
		t.Result(-1, "参数异常")
		return
	}
	if err := req.Update(); err != nil {
		t.Result(-1, err.Error())
		return
	}
	t.Result("update success")
}
