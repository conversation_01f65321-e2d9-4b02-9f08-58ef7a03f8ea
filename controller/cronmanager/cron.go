package cronmanager

import (
	cmdb "gitlab.dailyyoga.com.cn/rdc/serverops/database/cronmanager"
	cm "gitlab.dailyyoga.com.cn/rdc/serverops/service/cronmanager"
	"gitlab.dailyyoga.com.cn/server/go-artifact/http"
)

type cron struct{}

var Cron cron

func (c *cron) GetServiceInfo(t *http.Tools) {
	services := cm.GetInstance().GetServices()
	if services == nil {
		services = make(map[string][]*cm.ServiceInfo)
	}

	t.Result(services)
}

func (c *cron) GetTaskLogs(t *http.Tools) {
	taskID := t.GetFormStringD("task_id", "")
	serviceID := t.GetFormStringD("service_id", "")
	if taskID == "" && serviceID == "" {
		t.Result(-1, "params is undefined, task_id or service_id MUST be set")
		return
	}

	limit := t.GetFormIntD("limit", 100)

	options := []cmdb.FetchOption{
		cmdb.FetchWithLimit(limit),
	}
	if taskID != "" {
		options = append(options, cmdb.FetchWithTask(taskID))
	}
	if serviceID != "" {
		options = append(options, cmdb.FetchWithService(serviceID))
	}

	data := cmdb.TbLog.FetchTaskLogs(options...)
	if data == nil {
		data = make([]cmdb.Log, 0)
	}
	t.Result(data)
}
