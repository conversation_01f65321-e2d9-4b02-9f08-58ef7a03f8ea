package controller

import (
	"fmt"

	"gitlab.dailyyoga.com.cn/rdc/serverops/database/admin"
	"gitlab.dailyyoga.com.cn/rdc/serverops/service/jwt"
	"gitlab.dailyyoga.com.cn/server/go-artifact/http"
)

type login struct{}

var Login login

type loginData struct {
	WelcomeMessage string           `json:"welcome"`
	User           loginUser        `json:"user"`
	Token          string           `json:"token"`
	ExpireAt       int64            `json:"expireAt"`
	Permissions    []permissionItem `json:"permissions"`
	Roles          []permissionItem `json:"roles"`
}

type loginUser struct {
	Name   string `json:"name"`
	Avatar string `json:"avatar"`
}

type permissionItem struct {
	ID        string   `json:"id"`
	Operation []string `json:"operation"`
}

type loginRequest struct {
	Name     string `json:"name"`
	Password string `json:"password"`
}

func (l *login) Login(t *http.Tools) {
	req := &loginRequest{}
	t.ParseBodyObject(req)
	if req.Name == "" || req.Password == "" {
		t.Result(-1, "账号密码错误")
		return
	}

	data := &loginData{
		User: loginUser{}, Permissions: make([]permissionItem, 0), Roles: make([]permissionItem, 0),
	}

	user, err := admin.TbUser.CheckUser(req.Name, req.Password)
	if err != nil {
		t.Result(-1, err.Error())
		return
	}

	data.Roles = append(data.Roles, permissionItem{
		ID:        user.RoleID,
		Operation: admin.RoleOperation[user.RoleID],
	})

	data.WelcomeMessage = fmt.Sprintf("%s, 欢迎回来！！", user.Nickname)
	data.User = loginUser{Name: user.Nickname}

	// generate token
	tokenInfo := jwt.GenerateToken(user)
	if tokenInfo == nil {
		t.Result(-1, "token generate failed.")
		return
	}

	data.Token = tokenInfo.Token
	data.ExpireAt = tokenInfo.ExpireAt

	t.Result(data)
}
