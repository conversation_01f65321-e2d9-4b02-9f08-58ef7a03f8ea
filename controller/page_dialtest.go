package controller

import (
	"encoding/json"

	db "gitlab.dailyyoga.com.cn/rdc/serverops/database/indicator"
	"gitlab.dailyyoga.com.cn/server/go-artifact/http"
)

type dialTest struct{}

var DialTest dialTest

type dialtestTask struct {
	ID         int64                  `xorm:"int(11) autoincr pk 'id'" json:"id"`
	URL        string                 `xorm:"not null varchar(128) 'url'" json:"url"`
	AppName    string                 `xorm:"not null varchar(50) 'app_name'" json:"app_name"`
	Name       string                 `xorm:"not null varchar(50) 'name'" json:"name"`
	Method     string                 `xorm:"not null varchar(50) 'method'" json:"method"`
	Headers    map[string]interface{} `xorm:"not null varchar(128) 'headers'" json:"headers"`
	Body       map[string]interface{} `xorm:"not null varchar(128) 'body'" json:"body"`
	StatusCode int64                  `xorm:"int(11) 'status_code'" json:"status_code"`
	CreateAt   int64                  `xorm:"int(11) 'created_at'" json:"-"`
	UpdateAt   int64                  `xorm:"int(11) 'updated_at'" json:"-"`
}

func (d *dialTest) Lists(t *http.Tools) {
	opts := make([]db.ListOption, 0)
	appName := t.GetFormStringD("app_name", "")
	if appName != "" {
		opts = append(opts, db.ListWithAppName(appName))
	}

	t.Result(db.TbDialtest.Lists(opts...))
}

func (d *dialTest) Set(t *http.Tools) {
	bean := &db.DialtestTask{}
	tmpBean := &dialtestTask{}

	if err := json.Unmarshal(t.GetBody(), tmpBean); err != nil {
		t.Result(-1, err.Error())
		return
	}
	bean = MapToString(tmpBean, bean)

	if err := bean.Save(); err != nil {
		t.Result(-1, err.Error())
		return
	}

	t.Result("save success")
}

func (d *dialTest) Delete(t *http.Tools) {
	bean := &db.DialtestTask{}
	if err := json.Unmarshal(t.GetBody(), bean); err != nil {
		t.Result(-1, err.Error())
		return
	}

	if err := bean.Delete(); err != nil {
		t.Result(-1, err.Error())
		return
	}
	t.Result("delete success")
}

func MapToString(tmpBean *dialtestTask, bean *db.DialtestTask) *db.DialtestTask {
	headerJSON, _ := json.Marshal(tmpBean.Headers)
	bean.Headers = string(headerJSON)
	bodyJSON, _ := json.Marshal(tmpBean.Body)
	bean.Body = string(bodyJSON)
	bean.URL = tmpBean.URL
	bean.Method = tmpBean.Method
	bean.AppName = tmpBean.AppName
	bean.Name = tmpBean.Name
	return bean
}
