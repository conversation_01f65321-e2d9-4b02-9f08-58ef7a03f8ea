package controller

import (
	"sort"
	"strings"

	"gitlab.dailyyoga.com.cn/rdc/serverops/service/ali"
	"gitlab.dailyyoga.com.cn/server/go-artifact/http"
)

type androidRelease struct{}

var AndroidRelease androidRelease

func (a *androidRelease) prefixPadding(prefix string) string {
	prefix = strings.TrimRight(prefix, "/")
	ret := make([]string, 0)
	for _, v := range strings.Split(prefix, ".") {
		if len(v) == 1 {
			v = "0" + v
		}
		ret = append(ret, v)
	}
	return strings.Join(ret, ".")
}

func (a *androidRelease) DownloadURL(t *http.Tools) {
	key := t.GetFormStringD("key", "")

	downloadInfo := ali.OSS.GetDownloadURL(ali.AndroidReleaseBucket, key)
	if downloadInfo.DownloadURL == "" {
		t.Result(-1, "获取下载连接时错误")
		return
	}
	t.Result(downloadInfo)
}

func (a *androidRelease) Lists(t *http.Tools) {
	prefix := t.GetFormStringD("prefix", "")
	res := ali.OSS.ListBucket(ali.AndroidReleaseBucket, prefix)

	onlyDirectory := false // 纯目录标识
	if len(res) > 0 && res[0].Prefix != nil && res[0].Prefix.Real == res[0].Prefix.Display {
		onlyDirectory = true
	}

	if onlyDirectory {
		// 只排 prefix
		prefixMap := make(map[string]ali.BucketItem)
		sortKeys := make([]string, 0)
		for _, item := range res {
			key := a.prefixPadding(item.Prefix.Display)
			if _, ok := prefixMap[key]; !ok {
				prefixMap[key] = item
				sortKeys = append(sortKeys, key)
			}
		}
		sort.Slice(sortKeys, func(i, j int) bool {
			return sortKeys[i] > sortKeys[j]
		})

		ret := make([]ali.BucketItem, 0)
		for _, key := range sortKeys {
			ret = append(ret, prefixMap[key])
		}
		t.Result(ret)
		return
	}
	// object 根据最后更新时间排序
	sort.Slice(res, func(i, j int) bool {
		if res[i].Object != nil && res[j].Object != nil {
			return res[i].Object.LastModified > res[j].Object.LastModified
		}
		return false
	})
	t.Result(res)
}
