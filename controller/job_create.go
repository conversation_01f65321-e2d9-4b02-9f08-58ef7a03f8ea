package controller

import (
	"context"
	"fmt"
	"log"
	http2 "net/http"

	"gitlab.dailyyoga.com.cn/rdc/serverops/service/k8s"
	"gitlab.dailyyoga.com.cn/server/go-artifact/http"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

type job struct{}

var Job job

func (j *job) Create(t *http.Tools) {
	// 创建 Serverless集群的 Kubernetes 客户端
	clientset := k8s.GetJobClient()

	// 生成 Job 对象信息
	job := k8s.GenerateJobInfo(t.GetResponseWriter(), t.GetRequest())

	// 创建 Job 对象并提交到 Kubernetes 集群
	result, err := clientset.BatchV1().Jobs("default").Create(context.Background(), job, metav1.CreateOptions{})
	if err != nil {
		log.Println(err)
		http2.Error(t.GetResponseWriter(), "Bad Request", http2.StatusBadRequest)
	}
	_, err = fmt.Fprintf(t.GetResponseWriter(), "Created job %s.\n", result.GetObjectMeta().GetName())
	if err != nil {
		log.Println(err)
	}
}
