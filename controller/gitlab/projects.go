package gitlab

import (
	gogitlab "github.com/xanzy/go-gitlab"
	"gitlab.dailyyoga.com.cn/rdc/serverops/service/gitlab"
	"gitlab.dailyyoga.com.cn/server/go-artifact/http"
)

type projects struct{}

var Projects projects

func (p *projects) Lists(t *http.Tools) {
	gid := t.GetFormIntD("gid", 0)

	rets := make([]*gitlab.SimpleProjectItem, 0)

	page := 1
	for {
		opt := &gogitlab.ListGroupProjectsOptions{}
		opt.Page = page
		opt.PerPage = 200
		projectsData, _, err := gitlab.GetApiClient().Groups.ListGroupProjects(gid, opt)
		if err != nil {
			t.Result(-1, err.Error())
			return
		}
		if len(projectsData) == 0 {
			break
		}

		for _, v := range projectsData {
			rets = append(rets, &gitlab.SimpleProjectItem{
				ID:                v.ID,
				Name:              v.Name,
				PathWithNamespace: v.PathWithNamespace,
				Description:       v.Description,
				DefaultBranch:     v.DefaultBranch,
				Visibility:        v.Visibility,
				WebURL:            v.WebURL,
			})
		}
		page++
	}

	t.Result(rets)
}

func (p *projects) Branches(t *http.Tools) {
	id := t.GetFormIntD("id", 0)

	rets := make([]*gitlab.SimpleBranchItem, 0)

	page := 1
	for {
		opt := &gogitlab.ListBranchesOptions{}
		opt.Page = page
		opt.PerPage = 200
		branches, _, err := gitlab.GetApiClient().Branches.ListBranches(id, opt)
		if err != nil {
			t.Result(-1, err.Error())
			return
		}
		if len(branches) == 0 {
			break
		}

		for _, v := range branches {
			rets = append(rets, &gitlab.SimpleBranchItem{
				Name: v.Name, Protected: v.Protected,
			})
		}
		page++
	}

	t.Result(rets)
}
