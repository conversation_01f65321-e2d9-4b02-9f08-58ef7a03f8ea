package gitlab

import (
	gogitlab "github.com/xanzy/go-gitlab"
	"gitlab.dailyyoga.com.cn/rdc/serverops/service/gitlab"
	"gitlab.dailyyoga.com.cn/server/go-artifact/http"
)

type groups struct{}

var Groups groups

func (g *groups) Lists(t *http.Tools) {
	opt := &gogitlab.ListGroupsOptions{}
	opt.PerPage = 200
	groupsData, _, err := gitlab.GetApiClient().Groups.ListGroups(opt)
	if err != nil {
		t.Result(-1, err.Error())
		return
	}

	rets := make([]*gitlab.SimpleGroupItem, 0)
	for _, v := range groupsData {
		rets = append(rets, &gitlab.SimpleGroupItem{
			ID:          v.ID,
			Name:        v.Name,
			FullName:    v.FullName,
			Description: v.Description,
			Visibility:  v.Visibility,
			WebURL:      v.WebURL,
		})
	}

	t.Result(rets)
}
