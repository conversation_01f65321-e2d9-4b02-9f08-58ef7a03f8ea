package gitlab

import (
	"context"
	"encoding/json"

	"gitlab.dailyyoga.com.cn/rdc/serverops/service/gitlab"
	"gitlab.dailyyoga.com.cn/server/go-artifact/http"
)

type webhook struct{}

var Webhook webhook

var hookRunner = map[gitlab.ObjectKindName]func(ctx context.Context, b []byte) error{
	gitlab.ObjectKindPipeline:     pipeline,
	gitlab.ObjectKindMergeRequest: mergerRequest,
}

func (w *webhook) Handle(t *http.Tools) {
	payload := &gitlab.HookPayload{}
	body := t.GetBody()
	ctx := t.GetContext()
	if err := json.Unmarshal(body, payload); err != nil {
		t.Result(-1, err.Error())
		return
	}

	runner, ok := hookRunner[payload.ObjectKind]
	if !ok {
		t.Result(-1, "object kind not support")
		return
	}
	if err := runner(ctx, body); err != nil {
		t.Result(-1, err.Error())
		return
	}
	t.Result(1, "success")
}
