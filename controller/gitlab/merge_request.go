package gitlab

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"github.com/pkg/errors"
	"gitlab.dailyyoga.com.cn/rdc/serverops/database/deployment"
	"gitlab.dailyyoga.com.cn/rdc/serverops/service/deploy"
	"gitlab.dailyyoga.com.cn/rdc/serverops/service/gitlab"
	"gitlab.dailyyoga.com.cn/server/go-artifact/artifact"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
)

var mergeRequestDeploymentRunner = map[deployment.DeployChannel]func(ctx context.Context,
	item *deployment.Mapping){
	deployment.DeployJenkinsChannel: deploy.Channel.JenkinsJobDeployment,
}

func mergerRequest(ctx context.Context, body []byte) error {
	payload := &gitlab.MergeRequestPayload{}
	if err := json.Unmarshal(body, payload); err != nil {
		return err
	}

	state := payload.ObjectAttributes.State
	action := payload.ObjectAttributes.Action
	ref := payload.ObjectAttributes.TargetBranch
	project := payload.Project.Name

	info, err := deployment.TbMapping.GetMItem(payload.Project.ID, ref)
	if err != nil {
		deploy.Notify.FailedMessage(project, ref, err)
		return nil
	}

	if info == nil {
		err := errors.New("the deployment process management method id not defined")
		deploy.Notify.FailedMessage(project, ref, err)
		return err
	}

	// 只有action是open的时候才发确认消息，update不发
	if state == "opened" && ref == "master" && action == "open" {
		return openMasterMergeRequest(ctx, info, payload)
	}

	if state != "merged" {
		return errors.New("not support merge request state")
	}

	if fn, ok := mergeRequestDeploymentRunner[info.DeployChannel]; ok {
		fn(ctx, info)
	}
	return nil
}

func openMasterMergeRequest(ctx context.Context, info *deployment.Mapping, payload *gitlab.MergeRequestPayload) error {
	project := int(payload.Project.ID)
	iid := payload.ObjectAttributes.IID

	qaName := getMergeRequestLabelQA(payload.Labels)
	if qaName == "" {
		comment := "未选择负责更改的测试，请选择后重新提交合并请求"
		if err := gitlab.MergeRequest.Comment(project, iid, comment); err != nil {
			logger.Error(err)
			return err
		}
		if err := gitlab.MergeRequest.Close(project, iid); err != nil {
			logger.Error(err)
			return err
		}
	}

	taskIDLen := 16
	taskID := artifact.GetRandomString(taskIDLen)

	// send confirm message
	if err := deploy.Notify.ConfirmMessage(ctx, info, payload, qaName, taskID); err != nil {
		logger.Error(err)
		return err
	}
	// start merge request discussion, block merge operate
	note := fmt.Sprintf("上线请求已发送给指定的 QA 确认，请等待测试确认。TaskID:%s", taskID)
	if err := gitlab.MergeRequest.StartDiscussion(project, iid, note); err != nil {
		logger.Error(err)
		return err
	}
	return nil
}

func getMergeRequestLabelQA(labels []gitlab.LabelItem) string {
	if len(labels) == 0 {
		return ""
	}

	for i := range labels {
		title := labels[i].Title
		if strings.HasPrefix(title, "QA-") {
			return strings.ReplaceAll(title, "QA-", "")
		}
	}
	return ""
}
