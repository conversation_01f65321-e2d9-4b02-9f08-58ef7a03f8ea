package gitlab

import (
	"bytes"
	"context"
	"encoding/json"

	"github.com/pkg/errors"
	"gitlab.dailyyoga.com.cn/rdc/serverops/database/deployment"
	"gitlab.dailyyoga.com.cn/rdc/serverops/service/deploy"
	"gitlab.dailyyoga.com.cn/rdc/serverops/service/gitlab"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
)

var pipelineDeploymentRunner = map[deployment.DeployChannel]func(ctx context.Context, item *deployment.Mapping){
	deployment.DeployJenkinsChannel:    deploy.Channel.JenkinsJobDeployment,
	deployment.DeployKubernetesChannel: deploy.Channel.KubernetesDeployment,
	deployment.DeployGitlabCIChannel:   deploy.Channel.GitlabCIDeployment,
}

func pipeline(ctx context.Context, body []byte) error {
	payload := gitlab.PipelinePayload{}
	if err := json.NewDecoder(bytes.NewReader(body)).Decode(&payload); err != nil {
		logger.Error(err)
		return err
	}

	if payload.ObjectAttributes.Status == "running" || payload.ObjectAttributes.Status == "pending" {
		return errors.New("not support pipeline status")
	}
	if payload.ObjectAttributes.Status == "success" {
		pipelineSuccess(ctx, &payload)
	} else {
		pipelineFailed(&payload)
	}
	return nil
}

func pipelineSuccess(ctx context.Context, payload *gitlab.PipelinePayload) {
	project := payload.Project.Name
	ref := payload.ObjectAttributes.Ref
	info, err := deployment.TbMapping.GetMItem(payload.Project.ID, ref)
	if err != nil {
		deploy.Notify.FailedMessage(project, ref, err)
		return
	}

	if info != nil {
		if fn, ok := pipelineDeploymentRunner[info.DeployChannel]; ok {
			fn(ctx, info)
		}
	}
}

func pipelineFailed(payload *gitlab.PipelinePayload) {
	stageErrs := map[string]string{
		"linter": "代码检测未通过",
		"lint":   "代码检测未通过",
		"build":  "项目编译失败",
		"images": "容器镜像构建失败",
		"deploy": "发布部署失败",
	}
	var errMessage error
	for _, v := range payload.Builds {
		if v.Status != "failed" {
			continue
		}
		if err, ok := stageErrs[v.Stage]; ok {
			errMessage = errors.New(err)
		} else {
			errMessage = errors.Errorf("stage: %s 执行失败", v.Stage)
		}
		break
	}
	if errMessage == nil {
		return
	}
	deploy.Notify.FailedMessage(payload.Project.Name, payload.ObjectAttributes.Ref, errMessage)
}
