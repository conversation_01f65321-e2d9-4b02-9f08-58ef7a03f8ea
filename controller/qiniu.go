package controller

import (
	"github.com/qiniu/go-sdk/v7/storage"
	"gitlab.dailyyoga.com.cn/rdc/serverops/service/qiniu"
	"gitlab.dailyyoga.com.cn/server/go-artifact/http"
)

type qiniuApi struct{}

var QiNiu qiniuApi

func (q *qiniuApi) GetUploadToken(t *http.Tools) {
	bucket := t.GetFormStringD("bucket", "")
	if bucket == "" {
		t.Result(-1, "bucket is empty")
		return
	}

	putPolicy := storage.PutPolicy{Scope: bucket}
	upToken := putPolicy.UploadToken(qiniu.NewStorage().GetMac())
	t.Result(struct {
		UploadToken string `json:"uploadToken"`
	}{
		UploadToken: upToken,
	})
}
