package controller

import (
	"fmt"
	"strings"

	"gitlab.dailyyoga.com.cn/rdc/serverops/cron"
	weworkdb "gitlab.dailyyoga.com.cn/rdc/serverops/database/wework"
	weworkservice "gitlab.dailyyoga.com.cn/rdc/serverops/service/wework"
	"gitlab.dailyyoga.com.cn/rdc/serverops/service/wework/auth"
	"gitlab.dailyyoga.com.cn/rdc/serverops/service/wework/callback"
	"gitlab.dailyyoga.com.cn/server/go-artifact/http"
	"gitlab.dailyyoga.com.cn/server/go-artifact/http/response"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
)

type wework struct{}

var WeWork wework

func (w *wework) CallbackInvalid(t *http.Tools) {
	req := callback.VerifyURLReq{
		MsgSignature: t.GetFormStringD("msg_signature", ""),
		Timestamp:    t.GetFormStringD("timestamp", ""),
		Nonce:        t.GetFormStringD("nonce", ""),
		EchoStr:      t.GetFormStringD("echostr", ""),
	}

	manager := callback.NewCallbackManager(weworkservice.CropID, weworkservice.CallbackToken, weworkservice.CallbackEncodingAESKey)
	str, err := manager.CallbackVerifyURL(req)
	if err != nil {
		logger.Error(err)
	} else {
		rw := response.NewWriter(t.GetResponseWriter())
		rw.DisableGzip()
		_, err := rw.Write([]byte(str))
		if err != nil {
			logger.Error(err)
		}
	}
}

func (w *wework) CallbackHandle(t *http.Tools) {
	ctx := t.GetContext()
	body := t.GetBody()
	req := callback.DecryptUserMsgReq{
		MsgSignature: t.GetFormStringD("msg_signature", ""),
		Timestamp:    t.GetFormStringD("timestamp", ""),
		Nonce:        t.GetFormStringD("nonce", ""),
	}
	logger.Infof("callback uri: %s", t.GetRequest().RequestURI)
	logger.Infof("callback payload: %s", string(body))
	req.Data = body

	manager := callback.NewCallbackManager(weworkservice.CropID, weworkservice.CallbackToken, weworkservice.CallbackEncodingAESKey)
	messageProcessor, err := manager.DecryptMsg(req)
	if err != nil {
		logger.Warn(err)
		return
	}
	if messageProcessor == nil {
		logger.Warn("message processor is nil")
		return
	}

	data, ts, nonce, err := messageProcessor.Returns(ctx)
	if err != nil {
		logger.Error(err)
		return
	}
	if len(data) == 0 {
		return
	}

	logger.Infof("reply %s", string(data))
	resp, err := manager.EncryptUserMsg(&callback.EncryptUserMsgReq{
		Timestamp: ts,
		Nonce:     nonce,
		Data:      string(data),
	})
	if err != nil {
		logger.Error(err)
		return
	}
	logger.Infof("resp: %s", string(resp))

	rw := t.GetResponseWriter()
	if writer, ok := rw.(*response.Writer); ok {
		writer.DisableGzip()
		_, err = writer.Write(resp)
	} else {
		_, err = rw.Write(resp)
	}
	if err != nil {
		logger.Error(err)
		return
	}

	go messageProcessor.Process(ctx)
}

func (w *wework) AgentUsers(_ *http.Tools) {
	err := cron.SyncWeworkAgentUsers()
	if err != nil {
		logger.Error(err)
		return
	}
}

func (w *wework) GetAuthUserInfo(t *http.Tools) {
	ctx := t.GetContext()
	code := t.GetFormStringD("code", "")
	if code == "" {
		t.Result(-1, "code is empty")
		return
	}

	authResponse, err := auth.GetUserInfo(ctx, code)
	if err != nil || authResponse == nil {
		err := fmt.Errorf("auth error: %s", err)
		logger.Errorf("code: %s, err: %s", code, err)
		t.Result(-1, err.Error())
		return
	}

	if authResponse.ErrCode != 0 {
		err := fmt.Errorf("auth error: %d, message: %s", authResponse.ErrCode, authResponse.ErrMessage)
		logger.Warnf("code: %s, err: %s", code, err)
		t.Result(-1, err.Error())
		return
	}
	if authResponse.UserID == "" {
		err := fmt.Errorf("invalid user, empty user id")
		logger.Errorf("code: %s, err: %s", code, err)
		t.Result(-1, err.Error())
		return
	}

	logger.Infof("code: %s, user: %s", code, authResponse.UserID)
	t.Result(struct {
		UserID string `json:"userid"`
	}{
		UserID: authResponse.UserID,
	})
}

func (w *wework) GetUseInfo(t *http.Tools) {
	ids := t.GetFormStringD("user_ids", "")
	if ids == "" {
		t.Result(-1, "user ids is empty")
		return
	}
	fetchedUsers := weworkdb.TbUsers.GetUsers(weworkservice.ServeropsAgentID, strings.Split(ids, "|"))
	t.Result(fetchedUsers)
}
