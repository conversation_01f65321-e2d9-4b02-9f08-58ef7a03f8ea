package controller

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"gitlab.dailyyoga.com.cn/rdc/serverops/config"
	"gitlab.dailyyoga.com.cn/rdc/serverops/library"
	"gitlab.dailyyoga.com.cn/server/go-artifact/http"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"go.etcd.io/etcd/api/v3/mvccpb"
	clientv3 "go.etcd.io/etcd/client/v3"
)

type etcd struct{}

var Etcd etcd

type clusterStatusRow struct {
	Endpoint  string `json:"endpoint"`
	ID        uint64 `json:"id"`
	Version   string `json:"version"`
	DBSize    int64  `json:"db_size"`
	IsLeader  string `json:"is_leader"`
	RaftTerm  uint64 `json:"raft_term"`
	RaftIndex uint64 `json:"raft_index"`
}

type servicePayload struct {
	Key             string               `json:"key"`
	Name            string               `json:"name"`
	StartTime       int64                `json:"start_time"`
	ListenAddresses []serviceAddressItem `json:"listen_addresses"`
	TTL             int64                `json:"ttl"`
}

type serviceAddressItem struct {
	Address string `json:"address"`
	Tag     string `json:"tag"`
}

func (e *etcd) ClusterStatus(t *http.Tools) {
	c := e.getClient(t)
	if c == nil {
		t.Result(-1, "client is nil")
		return
	}

	ctx := context.Background()

	data := make([]clusterStatusRow, 0)

	for _, endpoint := range c.Endpoints() {
		resp, err := c.Status(ctx, endpoint)
		if err != nil {
			continue
		}
		isLeader := "False"
		if resp.Leader == resp.Header.MemberId {
			isLeader = "True"
		}
		data = append(data, clusterStatusRow{
			Endpoint:  endpoint,
			ID:        resp.Header.MemberId,
			Version:   resp.Version,
			DBSize:    resp.DbSize,
			IsLeader:  isLeader,
			RaftTerm:  resp.RaftTerm,
			RaftIndex: resp.RaftIndex,
		})
	}
	t.Result(data)
}

func (e *etcd) ServiceFetch(t *http.Tools) {
	c := e.getClient(t)
	if c == nil {
		t.Result(-1, "client is nil")
		return
	}

	serviceName := t.GetFormStringD("service_name", "")
	if serviceName == "" {
		t.Result(-1, "service_name is empty")
		return
	}

	key := fmt.Sprintf("/H2/Service/RPCAddress/%s/", serviceName)
	ctx := context.Background()

	resp, err := c.Get(ctx, key, clientv3.WithPrefix())
	if err != nil {
		logger.Error(err)
		t.Result(-1, err.Error())
		return
	}

	data := make([]servicePayload, 0)

	if len(resp.Kvs) == 0 {
		t.Result(data)
		return
	}

	for _, kv := range resp.Kvs {
		payload := servicePayload{}
		if err := json.Unmarshal(kv.Value, &payload); err != nil {
			continue
		}
		payload.Key = string(kv.Key)
		ttlRes, _ := c.TimeToLive(ctx, clientv3.LeaseID(kv.Lease))
		if ttlRes != nil {
			payload.TTL = ttlRes.TTL
		}

		data = append(data, payload)
	}
	t.Result(data)
}

type setPayload struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}

func (e *etcd) Set(t *http.Tools) {
	c := e.getClient(t)
	if c == nil {
		t.Result(-1, "client is nil")
		return
	}

	req := &setPayload{}
	t.ParseBodyObject(req)

	if req.Key == "" || req.Value == "" {
		t.Result(-1, "key or value is empty")
		return
	}

	ctx := context.Background()
	if _, err := c.Put(ctx, req.Key, req.Value); err != nil {
		t.Result(-1, err.Error())
		return
	}

	t.Result(&library.OperationResponse{Result: true})
}

func (e *etcd) Get(t *http.Tools) {
	c := e.getClient(t)
	if c == nil {
		t.Result(-1, "client is nil")
		return
	}

	key := t.GetFormStringD("key", "")
	if key == "" {
		t.Result(-1, "key is empty")
		return
	}

	ctx := context.Background()
	resp, err := c.Get(ctx, key)
	if err != nil {
		t.Result(-1, err.Error())
		return
	}

	type retPayload struct {
		Key   string `json:"key"`
		Value string `json:"value"`
	}

	ret := &retPayload{Key: key, Value: ""}

	if len(resp.Kvs) > 0 {
		ret.Value = string(resp.Kvs[0].Value)
	}
	t.Result(ret)
}

func (e *etcd) Del(t *http.Tools) {
	c := e.getClient(t)
	if c == nil {
		t.Result(-1, "client is nil")
		return
	}

	req := &setPayload{}
	t.ParseBodyObject(req)
	if req.Key == "" {
		t.Result(-1, "key is empty")
		return
	}

	ctx := context.Background()
	if _, err := c.Delete(ctx, req.Key); err != nil {
		t.Result(-1, err.Error())
		return
	}

	t.Result(&library.OperationResponse{Result: true})
}

func (e *etcd) Range(t *http.Tools) {
	c := e.getClient(t)
	if c == nil {
		t.Result(-1, "client is nil")
		return
	}
	ctx := context.Background()

	prefix := t.GetFormStringD("prefix", "")
	if prefix == "" {
		t.Result(-1, "key prefix is empty")
		return
	}

	directoryMode := t.GetFormBoolD("directory_mode", true)

	opts := []clientv3.OpOption{
		clientv3.WithPrefix(),
		clientv3.WithSort(clientv3.SortByKey, clientv3.SortAscend),
		clientv3.WithKeysOnly(),
	}
	resp, err := c.Get(ctx, prefix, opts...)
	if err != nil {
		t.Result(-1, err.Error())
		return
	}

	var ret = make([]string, 0)
	for _, v := range resp.Kvs {
		ret = append(ret, string(v.Key))
	}
	if !directoryMode {
		t.Result(ret)
		return
	}
	t.Result(e.KvDirectoryMode(resp.Kvs, prefix))
}

type kvDirectoryItem struct {
	Title    string             `json:"title"`
	Key      string             `json:"key"`
	Children []*kvDirectoryItem `json:"children,omitempty"`
	IsLeaf   bool               `json:"isLeaf,omitempty"`
}

func (e *etcd) KvDirectoryMode(kvs []*mvccpb.KeyValue, root string) []*kvDirectoryItem {
	keyList := make([]string, 0)
	for _, v := range kvs {
		keyList = append(keyList, strings.Replace(string(v.Key), root, "", -1))
	}
	rootNode := &kvDirectoryItem{
		Title: root, Key: root, Children: make([]*kvDirectoryItem, 0),
	}

	sep := "/"
	all := make(map[int]map[string]*kvDirectoryItem)
	max := 1
	for _, key := range keyList {
		if key == sep || key == "" {
			continue
		}
		keys := strings.Split(key, sep)
		if len(keys)-1 > max {
			max = len(keys)
		}
		keyMapping := e.changeMode(keys, sep)
		for level, value := range keyMapping {
			if _, ok := all[level]; !ok {
				all[level] = make(map[string]*kvDirectoryItem)
			}
			value.Key = root + value.Key
			all[level][value.Key] = value
		}
	}

	for i := max; i > 1; i-- {
		for _, current := range all[i] {
			for index, parent := range all[i-1] {
				if strings.HasPrefix(current.Key, parent.Key) {
					all[i-1][index].Children = append(all[i-1][index].Children, current)
				}
			}
		}
	}

	for _, v := range all[1] {
		rootNode.Children = append(rootNode.Children, v)
	}
	return []*kvDirectoryItem{rootNode}
}

func (e *etcd) changeMode(keys []string, separator string) map[int]*kvDirectoryItem {
	ret := make(map[int]*kvDirectoryItem)
	for i, v := range keys {
		if v == "" || v == separator {
			continue
		}
		if _, ok := ret[i]; ok {
			continue
		}
		ret[i] = &kvDirectoryItem{
			Title:    v,
			Key:      strings.Join(keys[:i+1], separator),
			Children: make([]*kvDirectoryItem, 0),
		}
		if i == len(keys)-1 {
			ret[i].IsLeaf = true
		}
	}
	return ret
}

func (e *etcd) getClient(t *http.Tools) *clientv3.Client {
	environment := t.GetFormStringD("env", "")
	if environment == "" {
		return nil
	}
	c, err := clientv3.New(clientv3.Config{
		Endpoints: strings.Split(config.Get().EtcdEndpoints, ";"),
	})
	if err != nil {
		logger.Error(err)
		return nil
	}
	return c
}
