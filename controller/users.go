package controller

import (
	"gitlab.dailyyoga.com.cn/rdc/serverops/database/admin"
	"gitlab.dailyyoga.com.cn/server/go-artifact/http"
)

type users struct{}

var Users users

type operationUserResponse struct {
	Result bool `json:"result"`
}

type operationUserRequest struct {
	Nickname string `json:"nickname"`
	Username string `json:"username"`
	Email    string `json:"email"`
	RoleID   string `json:"role_id"`
	Password string `json:"password"`
}

func (u *users) List(t *http.Tools) {
	t.Result(admin.TbUser.GetAllUsers())
}

func (u *users) New(t *http.Tools) {
	user := &operationUserRequest{}
	t.ParseBodyObject(user)

	if user.Password == "" {
		t.Result(-1, "Please input password")
		return
	}

	if user.Email == "" {
		t.Result(-1, "Email is empty")
		return
	}
	if user.Username == "" {
		t.Result(-1, "username is empty")
		return
	}
	if user.Nickname == "" {
		user.Nickname = user.Username
	}
	if user.RoleID == "" {
		user.RoleID = admin.RoleIDReadonly
	}

	bean := &admin.User{
		Nickname: user.Nickname,
		RoleID:   user.RoleID,
		Username: user.Username,
		Email:    user.Email,
		Password: user.Password,
	}

	if err := bean.Save(); err != nil {
		t.Result(-1, err)
		return
	}
	t.Result(&operationUserResponse{
		Result: true,
	})
}

func (u *users) Delete(t *http.Tools) {
	id := t.GetFormIntD("admin_id", 0)
	if id <= 1 { // 1 不能删除
		t.Result(-1, "user not found")
		return
	}

	bean := admin.TbUser.GetItemByUID(id)
	if bean == nil {
		t.Result(-1, "user not found")
		return
	}

	if err := bean.Delete(); err != nil {
		t.Result(-1, err)
		return
	}
	t.Result(&operationUserResponse{
		Result: true,
	})
}

func (u *users) Update(t *http.Tools) {
	id := t.GetFormIntD("admin_id", 0)
	if id == 0 {
		t.Result(-1, "user not found")
		return
	}

	user := &operationUserRequest{}
	t.ParseBodyObject(user)

	bean := admin.TbUser.GetItemByUID(id)
	if bean == nil {
		t.Result(-1, "user not found")
		return
	}
	if user.Username != bean.Username {
		bean.Username = user.Username
	}
	if user.Nickname != bean.Nickname {
		bean.Nickname = user.Nickname
	}
	if user.Password != bean.Password {
		bean.Password = user.Password
	}
	if user.Email != bean.Email {
		bean.Email = user.Email
	}
	if user.RoleID != bean.RoleID {
		bean.RoleID = user.RoleID
	}

	if err := bean.Update(); err != nil {
		t.Result(-1, err)
		return
	}
	t.Result(&operationUserResponse{
		Result: true,
	})
}
