package controller

import (
	"bytes"
	_ "embed" //#nolint
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	safelygo "gitlab.dailyyoga.com.cn/gokit/safely-go"
	"gitlab.dailyyoga.com.cn/rdc/serverops/database"
	"gitlab.dailyyoga.com.cn/rdc/serverops/database/apppackage"
	pipelinedb "gitlab.dailyyoga.com.cn/rdc/serverops/database/releasepipeline"
	"gitlab.dailyyoga.com.cn/rdc/serverops/library"
	"gitlab.dailyyoga.com.cn/rdc/serverops/service/qiniu"
	"gitlab.dailyyoga.com.cn/rdc/serverops/service/releasepipeline"
	"gitlab.dailyyoga.com.cn/server/go-artifact/http"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/go-artifact/wework/robot"
	"golang.org/x/exp/slices"
)

type appPackage struct{}

var AppPackage appPackage

//go:embed manifest.plist
var plistTpl []byte

func (a *appPackage) GetDownloadURL(key string) string {
	return fmt.Sprintf("https://qnapppackage.dailyyoga.com.cn/%s", key)
}

func (a *appPackage) GetPlistURL(id int64, new bool) string {
	manifestURL := fmt.Sprintf("https://serveropsapi.dailyyoga.com.cn/ipa/%d/mainifest.plist", id)
	if new {
		manifestURL = fmt.Sprintf("https://serveropsapi.dailyyoga.com.cn/ipanew/%d/mainifest.plist", id)
	}
	return fmt.Sprintf("itms-services://?action=download-manifest&url=%s", manifestURL)
}

func (a *appPackage) PlistNew(t *http.Tools) {
	id := t.GetFormIntD("id", 0)
	if id <= 1 {
		t.Result(-1, "invalid id")
		return
	}
	info := apppackage.TbPackageNew.GetItem(int64(id))
	if info == nil || info.ID < 1 {
		t.Result(-1, "record not found")
		return
	}

	packageInfo := &apppackage.AppPackage{
		Key:      info.Key,
		Version:  strings.ReplaceAll(info.AppVersion, ".", "_"),
		Env:      string(info.Env),
		AppName:  string(info.AppName),
		Platform: string(info.BusinessLine),
	}
	tpl, err := a.buildPlist(packageInfo)
	if err != nil {
		t.Result(-1, errors.Errorf("build plist content failed, err: %s", err))
		return
	}
	_, _ = t.GetResponseWriter().Write([]byte(tpl))
}

func (a *appPackage) Plist(t *http.Tools) {
	id := t.GetFormIntD("id", 0)
	if id <= 1 {
		t.Result(-1, "invalid id")
		return
	}
	info := apppackage.TbAppPackage.GetItem(int64(id))
	if info == nil || info.ID < 1 {
		t.Result(-1, "record not found")
		return
	}
	tpl, err := a.buildPlist(info)
	if err != nil {
		t.Result(-1, errors.Errorf("build plist content failed, err: %s", err))
		return
	}
	_, _ = t.GetResponseWriter().Write([]byte(tpl))
}

func (a *appPackage) buildPlist(info *apppackage.AppPackage) (string, error) {
	downloadURL := a.GetDownloadURL(info.Key)
	if downloadURL == "" {
		return "", errors.Errorf("download url empty")
	}

	version := strings.ReplaceAll(info.Version, "_", ".")

	image := "https://qiniucdn.dailyyoga.com.cn/app_package_release.png"
	if info.Env == "dev" {
		image = "https://qiniucdn.dailyyoga.com.cn/app_package_dev.png"
	}

	title := "DailyYoga"
	if info.AppName != "dailyyoga" {
		title = info.AppName
	}

	const dancefitLogo = "https://apppackage.dailyyoga.com.cn/dancefit_h2.png"
	const fitnessLogo = "https://apppackage.dailyyoga.com.cn/fitness_h2.png"
	const stretchLogo = "https://apppackage.dailyyoga.com.cn/stretch_h2.png"
	const childrenLogo = "https://apppackage.dailyyoga.com.cn/children_h2.png"

	bundleID := "yogadaily"
	if info.Platform == library.AppPackagePlatformH2O {
		bundleID = "yogaen"
		if info.AppName == "DanceFit" {
			bundleID = "com.dancefitme.loseweight"
			image = dancefitLogo
		}
		if info.AppName == "MuscleFitness" {
			bundleID = "musclemonster.fitness.workout"
			image = fitnessLogo
		}
		if info.AppName == "JustStretch" {
			bundleID = "dailybend.bend.stretch"
			image = stretchLogo
		}
		if info.AppName == "WalkUp" {
			bundleID = "com.walkup.weightloss"
			image = "https://apppackage.dailyyoga.com.cn/walkup.png"
		}
	}

	if info.Platform == library.AppPackagePlatformH2 {
		if info.AppName == "dancefit" {
			bundleID = "com.nnm.dancefit"
			image = dancefitLogo
		} else if info.AppName == "musclefitness" || info.AppName == "fitness" {
			bundleID = "com.longnanming.musclefitness"
			image = fitnessLogo
		} else if info.AppName == "stretch" {
			bundleID = "com.yinniu.stretch"
			image = stretchLogo
		} else if info.AppName == "children" {
			bundleID = "com.yinniu.children.ios"
			image = childrenLogo
		}
	}

	tpl := string(plistTpl)
	replaces := map[string]string{
		"{{downloadURL}}": downloadURL,
		"{{version}}":     version,
		"{{image}}":       image,
		"{{title}}":       title,
		"{{bundleID}}":    bundleID,
	}
	for m, v := range replaces {
		tpl = strings.ReplaceAll(tpl, m, v)
	}
	return tpl, nil
}

type packageItemInfo struct {
	Name        string `json:"name"`
	Size        string `json:"size"`
	ENV         string `json:"env"`
	Platform    string `json:"platform"`
	OS          string `json:"os"`
	Version     string `json:"version"`
	BuildTime   string `json:"mod_time"`
	Channel     string `json:"channel"`
	DownloadURL string `json:"download_url"`
	AppName     string `json:"app_name"`
	ExtraInfo   string `json:"extra_info"`
}

func (a *appPackage) List(t *http.Tools) {
	search := &apppackage.SearchRequest{
		OS:       t.GetFormStringD("os", ""),
		Platform: t.GetFormStringD("platform", ""),
		ENV:      t.GetFormStringD("env", ""),
		Version:  t.GetFormStringD("version", ""),
		Channel:  t.GetFormStringD("channel", ""),
		AppName:  t.GetFormStringD("app_name", ""),
	}

	res := make([]packageItemInfo, 0)
	data := apppackage.TbAppPackage.FetchPackages(search)
	for i := range data {
		v := data[i]
		size := v.Size / 1024 / 1024
		item := packageItemInfo{
			Name:      v.Name,
			Size:      fmt.Sprintf("%dM", size),
			ENV:       v.Env,
			Platform:  v.Platform,
			OS:        v.OS,
			Channel:   v.Channel,
			AppName:   v.AppName,
			ExtraInfo: v.ExtraInfo,
			Version:   strings.ReplaceAll(v.Version, "_", "."),
			BuildTime: time.Unix(v.ModTime, 0).Format("01-02 15:04:05"),
		}
		item.DownloadURL = a.GetDownloadURL(v.Key)
		if v.OS == "iOS" {
			item.DownloadURL = a.GetPlistURL(v.ID, false)
		}
		res = append(res, item)
	}
	t.Result(res)
}

func (a *appPackage) SizeInfo(t *http.Tools) {
	os := t.GetFormStringD("os", "android")
	appname := t.GetFormStringD("app_name", "dailyyoga")
	businessLine := t.GetFormStringD("business_line", "h2")
	limit := t.GetFormIntD("limit", 100)

	data := apppackage.TbSize.Find(
		apppackage.SizeFilterWithOS(apppackage.OS(os)),
		apppackage.SizeFilterWithAppName(apppackage.AppName(appname)),
		apppackage.SizeFilterWithBusinessLine(apppackage.BusinessLine(businessLine)),
		apppackage.SizeFilterWithLimit(limit),
	)
	t.Result(data)
}

type reportChannelFile struct {
	Channel  string `json:"channel"`
	Filename string `json:"filename"`
	Size     int64  `json:"size"`
	ModTime  int64  `json:"mod_time"`
	Key      string `json:"key"`
	Hash     string `json:"hash"`
}

type reportInfo struct {
	BusinessLine  apppackage.BusinessLine `json:"business_line"`
	Env           apppackage.Env          `json:"env"`
	AppName       apppackage.AppName      `json:"app_name"`
	AppVersion    string                  `json:"app_version"`
	MarketVersion string                  `json:"market_version"`
	OS            apppackage.OS           `json:"os"`
	ExtendParams  map[string]string       `json:"extend_params"`
	ChannelFiles  []*reportChannelFile    `json:"channel_files"`
	SizeAnalyzed  *packageSizeInfo        `json:"size_analyzed,omitempty"`
}

type packageSizeInfo struct {
	Name    string                  `json:"name"`
	Hash    string                  `json:"hash"`
	APKInfo *apppackage.AndroidSize `json:"apk_info,omitempty"`
	IPAInfo *apppackage.AppleOSSize `json:"ipa_info,omitempty"`
}

func (r *reportInfo) validation() error {
	if !slices.Contains(apppackage.SupportAppName, r.AppName) {
		return errors.Errorf("unsupport app_name")
	}
	if !slices.Contains(apppackage.SupportBusinessLine, r.BusinessLine) {
		return errors.Errorf("unsupport business_line")
	}
	if !slices.Contains(apppackage.SupportEnv, r.Env) {
		return errors.Errorf("unsupport env")
	}
	if !slices.Contains(apppackage.SupportOS, r.OS) {
		return errors.Errorf("unsupport os")
	}
	// 非小程序逻辑，校验 channel_files 字段
	if !slices.Contains(apppackage.MiniAppOS, r.OS) && len(r.ChannelFiles) == 0 {
		return errors.Errorf("package fileinfo not found")
	}
	// 小程序逻辑，校验 extend_params 字段
	if slices.Contains(apppackage.MiniAppOS, r.OS) {
		if r.ExtendParams == nil {
			return errors.Errorf("extend_params is empty")
		}
		if _, ok := r.ExtendParams["miniapp"]; !ok {
			return errors.Errorf("extend_params.miniapp is not true")
		}
		if _, ok := r.ExtendParams["qrcode_url"]; !ok {
			return errors.Errorf("extend_params.qrcode_url is empty")
		}
	}
	return nil
}

func (a *appPackage) ReportNew(t *http.Tools) {
	bean := &reportInfo{}
	t.ParseBodyObject(bean)

	if err := bean.validation(); err != nil {
		t.Result(-1, err.Error())
		return
	}

	var insertData []*apppackage.PackageNew
	var code int
	var err error
	if slices.Contains(apppackage.MiniAppOS, bean.OS) {
		insertData, code, err = a.getMiniAppPackageSaveInfo(bean)
	} else {
		insertData, code, err = a.getAppPackageSaveInfo(bean)
	}
	if err != nil {
		t.Result(code, err.Error())
		return
	}

	if len(insertData) > 0 {
		if err := apppackage.TbPackageNew.BatchInsert(insertData); err != nil {
			logger.Error(err)
			t.Result(-10, err)
			return
		}
	}

	if bean.Env == apppackage.EnvReleaseMarket {
		safelygo.Run(func() {
			if slices.Contains(apppackage.MiniAppOS, bean.OS) {
				a.updateMiniAppReleaseMarketVersion(bean)
			} else {
				a.updateReleaseMarketVersion(bean)
			}
		})
		safelygo.Run(func() {
			status := PackageSizeAlarmYetToHappened // 小程序逻辑，不触发包体大小告警逻辑
			if !slices.Contains(apppackage.MiniAppOS, bean.OS) {
				status = a.saveAndAlarmPackageSizeInfo(bean.SizeAnalyzed) // 非小程序逻辑，关联包体大小告警逻辑
			}
			a.startReleasePipeline(bean, status) // 触发流水线
		})
	}

	t.Result("save success")
}

func (a *appPackage) getMiniAppPackageSaveInfo(bean *reportInfo) (data []*apppackage.PackageNew, code int, err error) {
	data = make([]*apppackage.PackageNew, 0)

	item := &apppackage.PackageNew{
		BusinessLine:  bean.BusinessLine,
		Env:           bean.Env,
		AppName:       bean.AppName,
		AppVersion:    bean.AppVersion,
		MarketVersion: bean.MarketVersion,
		OS:            bean.OS,
		ExtendParams:  "",
		CreateTime:    time.Now().Unix(),
		UpdateTime:    time.Now().Unix(),
		Hash:          "", // 随机一个，对小程序没有用，避免 hash 重复导致的无法入库的问题
	}

	item.Hash = uuid.New().String()

	b := &bytes.Buffer{}
	encoder := json.NewEncoder(b)
	encoder.SetEscapeHTML(false)
	if err := encoder.Encode(bean.ExtendParams); err != nil {
		return nil, -8, err
	}
	item.ExtendParams = b.String()

	if err := item.ValidationMiniApp(); err != nil {
		return nil, -9, err
	}
	data = append(data, item)
	return
}

func (a *appPackage) getAppPackageSaveInfo(bean *reportInfo) (data []*apppackage.PackageNew, code int, err error) {
	data = make([]*apppackage.PackageNew, 0)
	for _, channelFileInfo := range bean.ChannelFiles {
		if ok, err := apppackage.TbPackageNew.Exist(channelFileInfo.Hash); err != nil {
			return nil, -6, err
		} else if ok {
			return nil, -7, errors.Errorf("package exist.")
		}
		// fitness_1000001_release_arm64_v8a_ft  不入库
		if channelFileInfo.Channel == "fitness" && bean.Env == apppackage.EnvReleaseMarket &&
			strings.Contains(channelFileInfo.Filename, "fitness_1000001_release_arm64_v8a_ft") {
			continue
		}
		dbItem := &apppackage.PackageNew{
			BusinessLine:  bean.BusinessLine,
			Env:           bean.Env,
			AppName:       bean.AppName,
			AppVersion:    bean.AppVersion,
			MarketVersion: bean.MarketVersion,
			OS:            bean.OS,
			ExtendParams:  "",
			ChannelName:   channelFileInfo.Channel,
			Filename:      channelFileInfo.Filename,
			Size:          channelFileInfo.Size,
			ModTime:       channelFileInfo.ModTime,
			Key:           channelFileInfo.Key,
			Hash:          channelFileInfo.Hash,
			CreateTime:    time.Now().Unix(),
			UpdateTime:    time.Now().Unix(),
		}
		if len(bean.ExtendParams) > 0 {
			b := &bytes.Buffer{}
			encoder := json.NewEncoder(b)
			encoder.SetEscapeHTML(false)
			if err := encoder.Encode(bean.ExtendParams); err != nil {
				return nil, -8, err
			}
			dbItem.ExtendParams = b.String()
		}
		if err := dbItem.Validation(); err != nil {
			return nil, -9, err
		}
		data = append(data, dbItem)
	}
	return
}

func (a *appPackage) StartPipeline(t *http.Tools) {
	bean := &reportInfo{}
	t.ParseBodyObject(bean)

	if err := bean.validation(); err != nil {
		t.Result(-1, err.Error())
		return
	}

	a.startReleasePipeline(bean, PackageSizeAlarmHappened)
}

func (a *appPackage) startReleasePipeline(data *reportInfo, packageSizeAlarmStatus PackageSizeAlarmStatus) {
	if isGray, ok := data.ExtendParams["is_gray"]; ok && isGray == "true" {
		return
	}
	if packageSizeAlarmStatus == PackageSizeAlarmValidationFailed {
		logger.Infof("release pipeline nonstart, alarm status is %d", packageSizeAlarmStatus)
		return
	}

	// 获取流水线配置
	pipelineConf := releasepipeline.GetConfigWithApp(data.OS, string(data.AppName))
	if pipelineConf == nil {
		logger.Errorf("pipeline_config not found, platform: %s, app_name: %s", data.OS, data.AppName)
		return
	}

	// 包体大小告警，触发流水线，发送预检失败消息
	if packageSizeAlarmStatus == PackageSizeAlarmHappened {
		logger.Infof("release pipeline nonstart, alarm status is %d", packageSizeAlarmStatus)
		userids := pipelineConf.CC
		if len(pipelineConf.Stages) > 0 {
			if confirmUser := pipelineConf.Stages[0].ConfirmUser; confirmUser != "" && !slices.Contains(userids, confirmUser) {
				userids = append(userids, confirmUser)
			}
		}
		releasepipeline.Sender.SendPreCheckFailedMessage(userids, data.AppName, data.OS, data.AppVersion)
		return
	}

	// 保存流水线
	dbpipeline := &pipelinedb.Pipeline{
		BusinessLine:  data.BusinessLine,
		OS:            data.OS,
		AppName:       data.AppName,
		AppVersion:    data.AppVersion,
		MarketVersion: data.MarketVersion,
		Status:        pipelineConf.Status,
		CC:            strings.Join(pipelineConf.CC, ";"),
		ExtraParams:   "",
	}
	if buildVersion, ok := data.ExtendParams["build_version"]; ok {
		dbpipeline.BuildVersion = buildVersion
	}

	// 保存流水线额外参数
	b := &bytes.Buffer{}
	encoder := json.NewEncoder(b)
	encoder.SetEscapeHTML(false)
	if err := encoder.Encode(data.ExtendParams); err != nil {
		logger.Errorf("encode_extend_params_failed, err: %s, data: %#v", err, data.ExtendParams)
		return
	}
	dbpipeline.ExtraParams = b.String()

	if err := dbpipeline.Save(); err != nil {
		logger.Error(err)
		return
	}

	// 保存流水线各个阶段
	stages := make([]*pipelinedb.Stage, 0)
	for _, v := range pipelineConf.Stages {
		stage := &pipelinedb.Stage{
			PipelineID:          dbpipeline.ID,
			Index:               v.Index + 1, // 顺序从 1 开始
			Key:                 v.Key,
			Name:                v.Name,
			Description:         strings.Join(v.Description, "\n"),
			Status:              v.Status,
			AllowTerminate:      v.AllowTerminate,
			NeedEvidence:        v.NeedEvidence,
			EvidenceDescription: strings.Join(v.EvidenceDescription, "\n"),
			ConfirmUser:         v.ConfirmUser,
			AfterConfirm:        v.AfterConfirm,
		}
		stages = append(stages, stage)
	}
	if len(stages) > 0 {
		if err := pipelinedb.TbStage.BatchInsert(stages); err != nil {
			logger.Error(err)
			return
		}
	}

	// 获取流水线当前阶段
	stageRows := pipelinedb.TbStage.FetchWithPipeline(dbpipeline.ID)
	var stageID int64
	for _, v := range stageRows {
		if v.Status == pipelinedb.StatusWaiting {
			stageID = v.ID
			break
		}
	}

	// 执行流水线
	releasepipeline.Runner.Run(stageID)
}

func (a *appPackage) saveAndAlarmPackageSizeInfo(data *packageSizeInfo) PackageSizeAlarmStatus {
	if data == nil {
		logger.Errorf("package_size_info data nil")
		return PackageSizeAlarmValidationFailed
	}
	packageInfo := apppackage.TbPackageNew.GetItemByHash(data.Hash)
	if packageInfo == nil || packageInfo.ID == 0 {
		logger.Errorf("package_info not found, hash: %s", data.Hash)
		return PackageSizeAlarmValidationFailed
	}

	if data.APKInfo != nil {
		data.APKInfo.NewID = packageInfo.ID
		data.APKInfo.BusinessLine = packageInfo.BusinessLine
		data.APKInfo.AppName = packageInfo.AppName
		if err := data.APKInfo.Save(); err != nil {
			logger.Errorf("save_package_info_failed, err: %s, data: %#v", err, data.APKInfo)
			return PackageSizeAlarmValidationFailed
		}
		return a.packageSizeAlarm(apppackage.OSAndroid, data.APKInfo.AppName, data.APKInfo.NewID)
	} else if data.IPAInfo != nil {
		data.IPAInfo.NewID = packageInfo.ID
		data.IPAInfo.BusinessLine = packageInfo.BusinessLine
		data.IPAInfo.AppName = packageInfo.AppName
		if err := data.IPAInfo.Save(); err != nil {
			logger.Error("save_package_info_failed, err: %s, data: %#v", err, data.IPAInfo)
			return PackageSizeAlarmValidationFailed
		}
		return a.packageSizeAlarm(apppackage.OSAppleiOS, data.IPAInfo.AppName, data.IPAInfo.NewID)
	}
	logger.Errorf("package_size_info data not matched, apk and ipa info is empty")
	return PackageSizeAlarmValidationFailed
}

type packageSizeBaselineOS struct {
	IOS     *packageSizeBaselineOSItem `json:"ios"`
	Android *packageSizeBaselineOSItem `json:"android"`
}

type packageSizeBaselineOSItem struct {
	Default int                        `json:"default,omitempty"`
	Apps    map[apppackage.AppName]int `json:"apps,omitempty"`
}

type packageSizeBaseline struct {
	Baseline *packageSizeBaselineOS `json:"baseline"`
	Alarm    *packageSizeBaselineOS `json:"alarm"`
}

func (a *appPackage) getPackageSizeBaseline() *packageSizeBaseline {
	conf := database.TbConfigKeys.Get("APP_PACKAGE_SIZE_BASELINE")
	if conf == nil || conf.Value == "" {
		logger.Errorf("package_size_baseline_conf is empty")
		return nil
	}

	baseline := &packageSizeBaseline{}
	if err := json.NewDecoder(strings.NewReader(conf.Value)).Decode(baseline); err != nil {
		logger.Errorf("parse baseline, err: %s, conf: %#v", err, baseline)
		return nil
	}
	return baseline
}

type PackageSizeAlarmStatus int

const (
	PackageSizeAlarmHappened PackageSizeAlarmStatus = iota + 1 // 包体大小告警, 已触发告警
	PackageSizeAlarmYetToHappened                               // 包体大小告警, 未触发告警
	PackageSizeAlarmValidationFailed                            // 包体大小告警, 校验失败, 数据异常
)

func (a *appPackage) packageSizeAlarm(os apppackage.OS, appName apppackage.AppName, packageID int64) PackageSizeAlarmStatus {
	baseline := a.getPackageSizeBaseline()
	if baseline == nil {
		return PackageSizeAlarmValidationFailed
	}

	var robotURL string

	var alarmConf *packageSizeBaselineOSItem
	var baselineConf *packageSizeBaselineOSItem
	if os == apppackage.OSAppleiOS {
		alarmConf = baseline.Alarm.IOS
		baselineConf = baseline.Baseline.IOS
		robotURL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=10575779-0acd-40e7-8709-bfad1d9355f9"
	} else if os == apppackage.OSAndroid {
		alarmConf = baseline.Alarm.Android
		baselineConf = baseline.Baseline.Android
		robotURL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=35d4cc35-9020-4221-8458-3fd743682b16"
	}

	if alarmConf == nil || baselineConf == nil {
		return PackageSizeAlarmValidationFailed
	}
	alarmSize, ok := alarmConf.Apps[appName]
	if !ok {
		alarmSize = alarmConf.Default
	}
	alarmSize = alarmSize * 1024 * 1024
	baselineSize := baselineConf.Apps[appName]

	packageInfo := apppackage.TbPackageNew.GetItem(packageID)
	if packageInfo == nil || packageInfo.ID == 0 {
		logger.Errorf("package_info not found, package_id %d", packageID)
		return PackageSizeAlarmValidationFailed
	}

	diff := packageInfo.Size - int64(baselineSize*1024*1024)
	if diff < int64(alarmSize) {
		return PackageSizeAlarmYetToHappened
	}

	markdown := "<font color=\"warning\">[WARNING]</font> 包体大小告警，超出基准包大小 " +
		fmt.Sprintf("%.2f", float64(diff)/float64(1024*1204)) + " M"
	markdown += "\n应用标识：" + string(appName)
	markdown += "\n压缩后大小：" + fmt.Sprintf("%.2f", float64(packageInfo.Size)/float64(1024*1024)) + " M"
	markdown += "\n基准大小：" + fmt.Sprintf("%.2f", float64(baselineSize)) + " M"

	msg := &robot.Message{
		RobotURL: robotURL,
		MsgType:  robot.Markdown,
		Markdown: &robot.MarkdownMessage{Content: markdown},
	}
	robot.Get().AddMessage(msg)
	return PackageSizeAlarmHappened
}

func (a *appPackage) updateMiniAppReleaseMarketVersion(data *reportInfo) {
	key := fmt.Sprintf("MINIAPP_LATEST_VERSION_%s", strings.ToUpper(string(data.AppName)))
	val := database.TbConfigKeys.Get(key)
	if val == nil || val.Value == "" {
		return
	}
	versionInfo := &miniAppVersion{}
	if err := json.NewDecoder(bytes.NewReader([]byte(val.Value))).Decode(&versionInfo); err != nil {
		logger.Errorf("update miniapp release_market app_version failed, decode_data err: %s", err)
		return
	}

	version := ""
	if data.OS == apppackage.OSDouYin {
		version = a.compareVersion(data.AppVersion, versionInfo.DouYin)
		if version == data.AppVersion && data.AppVersion != versionInfo.DouYin {
			versionInfo.DouYin = version
		}
	} else if data.OS == apppackage.OSWechat {
		version = a.compareVersion(data.AppVersion, versionInfo.Wechat)
		if version == data.AppVersion && data.AppVersion != versionInfo.Wechat {
			versionInfo.Wechat = version
		}
	} else if data.OS == apppackage.OSAlipay {
		version = a.compareVersion(data.AppVersion, versionInfo.Alipay)
		if version == data.AppVersion && data.AppVersion != versionInfo.Alipay {
			versionInfo.Alipay = version
		}
	}
	if version != data.AppVersion {
		return
	}
	valData := &bytes.Buffer{}
	if err := json.NewEncoder(valData).Encode(versionInfo); err != nil {
		logger.Errorf("update release_market app_version failed, encode_data err: %s", err)
		return
	}
	val.Value = valData.String()
	if err := val.Update(); err != nil {
		logger.Errorf("update release_market app_version failed, update_row err: %s", err)
		return
	}
}

func (a *appPackage) updateReleaseMarketVersion(data *reportInfo) {
	key := fmt.Sprintf("APP_LATEST_VERSION_%s", strings.ToUpper(string(data.AppName)))
	val := database.TbConfigKeys.Get(key)
	if val == nil || val.Value == "" {
		return
	}
	versionInfo := &appVersion{}
	if err := json.NewDecoder(bytes.NewReader([]byte(val.Value))).Decode(&versionInfo); err != nil {
		logger.Errorf("update release_market app_version failed, decode_data err: %s", err)
		return
	}

	version := ""
	if data.OS == apppackage.OSAndroid {
		version = a.compareVersion(data.AppVersion, versionInfo.Android)
		if version == data.AppVersion && data.AppVersion != versionInfo.Android {
			versionInfo.Android = version
		}
	} else if data.OS == apppackage.OSAppleiOS {
		version = a.compareVersion(data.AppVersion, versionInfo.IOS)
		if version == data.AppVersion && data.AppVersion != versionInfo.IOS {
			versionInfo.IOS = version
		}
	}
	if version != data.AppVersion {
		return
	}
	valData := &bytes.Buffer{}
	if err := json.NewEncoder(valData).Encode(versionInfo); err != nil {
		logger.Errorf("update release_market app_version failed, encode_data err: %s", err)
		return
	}
	val.Value = valData.String()
	if err := val.Update(); err != nil {
		logger.Errorf("update release_market app_version failed, update_row err: %s", err)
		return
	}
}

// compareVersion string v1 and v2, return a higher version string
func (a *appPackage) compareVersion(v1, v2 string) string {
	v1s := strings.Split(v1, ".")
	v2s := strings.Split(v2, ".")

	v1l, v2l := len(v1s), len(v2s)
	l := v1l
	overflow := make([]string, 0)
	if v2l < l {
		l = v2l
		overflow = v1s[l:]
	} else if v2l > l {
		overflow = v2s[l:]
	}

	for i := 0; i < l; i++ {
		v1i, _ := strconv.Atoi(v1s[i])
		v2i, _ := strconv.Atoi(v2s[i])
		if v1i > v2i {
			return v1
		} else if v1i < v2i {
			return v2
		}
	}

	for j, v := range overflow {
		vi, _ := strconv.Atoi(v)
		if vi != 0 {
			if v1l > v2l {
				return v1
			}
			if v1l < v2l {
				return v2
			}
		}
		if vi == 0 && j == len(overflow)-1 {
			return v1
		}
	}
	if v1l == v2l {
		return v1
	}
	return ""
}

func (a *appPackage) ListDirectory(t *http.Tools) {
	prefix := t.GetFormStringD("prefix", "")
	if prefix == "" {
		t.Result(-1, "prefix is empty, no files found")
		return
	}

	mode := t.GetFormIntD("mode", 1) // 1 directory，2 files

	opts := []qiniu.ListOption{
		qiniu.WithListOptionPrefix(prefix),
	}
	if mode == 1 {
		opts = append(opts, qiniu.WithListOptionDelimiter("/"))
	}

	// list qiniu bucket prefix directory or files
	data := qiniu.NewStorage().List("dailyyoga-app-package", opts...)

	type ListItem struct {
		Key         string                 `json:"key"`
		Hash        string                 `json:"hash"`
		PutTime     int64                  `json:"put_time"`
		Fsize       int64                  `json:"fsize"`
		MimeType    string                 `json:"mime_type"`
		DownloadURL string                 `json:"download_url"`
		PackageInfo *apppackage.PackageNew `json:"package_info,omitempty"`
	}

	type ListInfo struct {
		CommonPrefixes []string    `json:"common_prefixes"`
		Items          []*ListItem `json:"items"`
	}
	ret := &ListInfo{
		CommonPrefixes: data.CommonPrefixes,
		Items:          make([]*ListItem, 0),
	}
	for _, v := range data.Items {
		itemInfo := &ListItem{
			Key:         v.Key,
			Hash:        v.Hash,
			PutTime:     v.PutTime,
			Fsize:       v.Fsize,
			MimeType:    v.MimeType,
			DownloadURL: a.GetDownloadURL(v.Key),
		}

		packageInfo := apppackage.TbPackageNew.GetItemByHash(v.Hash)
		if packageInfo != nil && packageInfo.Hash == v.Hash {
			itemInfo.PackageInfo = packageInfo
			if strings.ToLower(string(packageInfo.OS)) == "ios" {
				itemInfo.DownloadURL = a.GetPlistURL(packageInfo.ID, true)
			}
		}
		ret.Items = append(ret.Items, itemInfo)
	}

	t.Result(ret)
}

type appVersion struct {
	Android string `json:"android"`
	IOS     string `json:"ios"`
}

type miniAppVersion struct {
	DouYin string `json:"douyin"`
	Wechat  string `json:"wechat"`
	Alipay  string `json:"alipay"`
}

func (a *appPackage) Version(t *http.Tools) {
	appName := t.GetFormStringD("app_name", "")
	if appName == "" {
		t.Result(-1, "未指定应用")
		return
	}
	miniapp := t.GetFormBoolD("miniapp", false)

	var key string
	if miniapp {
		key = fmt.Sprintf("MINIAPP_LATEST_VERSION_%s", strings.ToUpper(appName))
	} else {
		key = fmt.Sprintf("APP_LATEST_VERSION_%s", strings.ToUpper(appName))
	}
	val := database.TbConfigKeys.Get(key)
	if val == nil {
		t.Result(-1, "查询错误，未找到配置")
		return
	}

	var ret interface{}
	if miniapp {
		ret = &miniAppVersion{}
	} else {
		ret = &appVersion{}
	}
	if err := json.NewDecoder(bytes.NewReader([]byte(val.Value))).Decode(&ret); err != nil {
		t.Result(-1, fmt.Sprintf("json decoded error: %s", err))
		return
	}
	t.Result(ret)
}