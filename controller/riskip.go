package controller

import (
	"context"

	"gitlab.dailyyoga.com.cn/rdc/serverops/cache"
	"gitlab.dailyyoga.com.cn/rdc/serverops/library"
	"gitlab.dailyyoga.com.cn/server/go-artifact/http"
)

const highRiskKey = "ngx:high_risk_ips"

type riskIP struct{}

var RiskIP riskIP

type operationRiskIPRequest struct {
	IP string `json:"ip"`
}

func (w *riskIP) Find(t *http.Tools) {
	ip := t.GetFormStringD("ip", "")
	if ip == "" {
		t.Result(-1, "ip is empty")
		return
	}

	c := cache.GetYogaNewRedis()
	ctx := context.Background()

	res := &library.OperationResponse{}
	res.Result = c.SIsMember(ctx, highRiskKey, ip).Val()
	t.Result(res)
}

func (w *riskIP) Delete(t *http.Tools) {
	req := &operationRiskIPRequest{}
	t.ParseBodyObject(req)
	if req.IP == "" {
		t.Result(-1, "ip is empty")
		return
	}

	c := cache.GetYogaNewRedis()

	res := &library.OperationResponse{Result: false}
	ctx := context.Background()
	if err := c.SRem(ctx, highRiskKey, req.IP).Err(); err != nil {
		t.Result(-1, err)
		return
	}
	res.Result = true
	t.Result(res)
}

func (w *riskIP) Count(t *http.Tools) {
	c := cache.GetYogaNewRedis()
	ctx := context.Background()
	cnt := c.SCard(ctx, highRiskKey).Val()
	t.Result(struct {
		Cnt int64 `json:"cnt"`
	}{Cnt: cnt})
}

func (w *riskIP) Set(t *http.Tools) {
	req := &operationRiskIPRequest{}
	t.ParseBodyObject(req)
	if req.IP == "" {
		t.Result(-1, "ip is empty")
		return
	}

	c := cache.GetYogaNewRedis()
	ctx := context.Background()

	res := &library.OperationResponse{Result: false}
	if err := c.SAdd(ctx, highRiskKey, req.IP).Err(); err != nil {
		t.Result(-1, err)
		return
	}
	res.Result = true
	t.Result(res)
}
