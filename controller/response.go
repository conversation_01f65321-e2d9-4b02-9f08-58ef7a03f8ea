package controller

import (
	"bytes"
	"encoding/json"
	"io"
	"net"
	"net/http"
	"os"
	"reflect"
	"syscall"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
)

type Response struct {
	Code    int         `json:"code"`
	Message string      `json:"message,omitempty"`
	Data    interface{} `json:"data,omitempty"`
}

func (r *Response) Render(w http.ResponseWriter, args []interface{}) {
	w.<PERSON>er().Set("Content-Type", "application/json; charset=utf-8")
	response := &Response{}

	if code, ok := r.assertionCode(args[0]); ok {
		response.Code = code
		if hasCustomMessageArgsLen := 2; len(args) >= hasCustomMessageArgsLen {
			if message, ok := args[1].(string); ok {
				response.Message = message
			}
		}
		if hasErrorDataArgsLen := 3; len(args) >= hasErrorDataArgsLen {
			response.Data = args[2]
		}
	} else {
		response.Data = args[0]
	}
	if response.Data == nil {
		response.Data = new(struct{})
	}

	b, err := json.Marshal(response)
	if err != nil {
		logger.Error(err)
		w.WriteHeader(http.StatusInternalServerError)
		return
	}
	r.write(w, r.safeEncodingJSON(b))
}

func (r *Response) write(w io.Writer, data []byte) {
	if _, err := w.Write(data); err != nil {
		if opErr, ok := err.(*net.OpError); ok {
			if sysErr, is := opErr.Err.(*os.SyscallError); is && sysErr.Err == syscall.EPIPE {
				return
			}
		}
		logger.Errorf("err: %s, err_type: %T %#v", err, err, err)
	}
}

func (r *Response) safeEncodingJSON(data []byte) []byte {
	for k, v := range map[string]string{
		"\\u003c": "<", "\\u003e": ">", "\\u0026": "&",
	} {
		data = bytes.Replace(data, []byte(k), []byte(v), -1)
	}
	return data
}

func (r *Response) assertionCode(a interface{}) (code int, ok bool) {
	val := reflect.ValueOf(a)
	supportKind := []reflect.Kind{
		reflect.Int, reflect.Int32, reflect.Int64,
	}
	for _, kind := range supportKind {
		if kind == val.Kind() {
			return int(val.Int()), true
		}
	}
	return
}
