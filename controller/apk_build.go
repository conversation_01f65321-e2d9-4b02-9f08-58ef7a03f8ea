package controller

import (
	"fmt"

	"gitlab.dailyyoga.com.cn/rdc/serverops/library"
	"gitlab.dailyyoga.com.cn/server/go-artifact/http"
)

const (
	gitlabPipelineURL = "https://gitlab.dailyyoga.com.cn/api/v4/projects/1/trigger/pipeline"
	gitlabToken       = "glptt-1d2ec2a16578bcd531f4764e6968ab3fa690c230"
)

type apkBuilder struct{}

var ApkBuilder apkBuilder

// triggerPipeline 触发GitLab Pipeline的通用方法
func (a *apkBuilder) triggerPipeline(t *http.Tools, ref string, variables map[string]string) {
	cmd := fmt.Sprintf("curl --request POST \"%s\" --form \"token=%s\" --form \"ref=%s\"",
		gitlabPipelineURL, gitlabToken, ref)

	// 添加额外的变量
	for key, value := range variables {
		cmd += fmt.Sprintf(" --form \"variables[%s]=%s\"", key, value)
	}

	// 执行curl命令
	_, err := library.ExecCommand(cmd)
	if err != nil {
		t.Result(-1, err.Error())
		return
	}
	t.Result(library.OperationResponse{Result: true, Message: "流水线触发成功"})
}

func (a *apkBuilder) SetCommon(t *http.Tools) {
	a.triggerPipeline(t, "develop", nil)
}

func (a *apkBuilder) SetOmnichannel(t *http.Tools) {
	variables := map[string]string{
		"BUILD_TYPE": "release_market",
		"IS_GRAY":    "true",
	}
	a.triggerPipeline(t, "master", variables)
}

func (a *apkBuilder) SetCanary(t *http.Tools) {
	variables := map[string]string{
		"BUILD_TYPE": "release_market",
		"IS_GRAY":    "false",
	}
	a.triggerPipeline(t, "master", variables)
}
