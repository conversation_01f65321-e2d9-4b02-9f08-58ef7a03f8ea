package controller

import (
	"gitlab.dailyyoga.com.cn/rdc/serverops/database/channel"
	"gitlab.dailyyoga.com.cn/server/go-artifact/http"
)

type apkChannel struct{}

var ApkChannel apkChannel

func (a *apkChannel) Lists(t *http.Tools) {
	lists := channel.TbApkChannel.Lists()
	if len(lists) == 0 {
		lists = make([]*channel.ApkChannel, 0)
	}
	t.Result(lists)
}

func (a *apkChannel) Delete(t *http.Tools) {
	bean := &channel.ApkChannel{}
	t.ParseBodyObject(bean)
	if bean.ID == 0 {
		t.Result(-1, "record `id` is empty")
		return
	}

	item := channel.TbApkChannel.GetItem(bean.ID)
	if item == nil {
		t.Result(-1, "record not found")
		return
	}

	if err := item.Delete(); err != nil {
		t.Result(-1, err.Error())
		return
	}
	t.Result("delete success")
}

func (a *apkChannel) Save(t *http.Tools) {
	bean := &channel.ApkChannel{}
	t.ParseBodyObject(bean)

	if bean.ChannelName == "" || bean.ChannelID == "" {
		t.Result(-1, "invaild params")
		return
	}

	// set default values
	if bean.Platform == "" { // h2/h2o
		bean.Platform = "h2"
	}
	if bean.OS == "" { // android/iOS
		bean.OS = "android"
	}

	if err := bean.Save(); err != nil {
		t.Result(-1, err.Error())
		return
	}
	t.Result("save success")
}
