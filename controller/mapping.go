package controller

import (
	"encoding/json"

	"github.com/pkg/errors"
	db "gitlab.dailyyoga.com.cn/rdc/serverops/database/deployment"
	"gitlab.dailyyoga.com.cn/rdc/serverops/service/gitlab"
	"gitlab.dailyyoga.com.cn/server/go-artifact/http"
)

type mapping struct{}

var Mapping mapping

func (m *mapping) Lists(t *http.Tools) {
	opts := make([]db.ListOption, 0)
	project := t.GetFormStringD("project", "")
	if project != "" {
		opts = append(opts, db.ListWithProject(project))
	}
	branch := t.GetFormStringD("branch", "")
	if branch != "" {
		opts = append(opts, db.ListWithBranch(branch))
	}
	channel := t.GetFormStringD("channel", "")
	if channel != "" {
		opts = append(opts, db.ListWithChannel(db.DeployChannel(channel)))
	}

	t.Result(db.TbMapping.Lists(opts...))
}

func (m *mapping) Delete(t *http.Tools) {
	bean := &db.Mapping{}
	if err := json.Unmarshal(t.GetBody(), bean); err != nil {
		t.Result(-1, err.Error())
		return
	}
	if err := bean.Delete(); err != nil {
		t.Result(-1, err.Error())
		return
	}
	t.Result("delete success")
}

func (m *mapping) Set(t *http.Tools) {
	bean := &db.Mapping{}
	if err := json.Unmarshal(t.GetBody(), bean); err != nil {
		t.Result(-1, err.Error())
		return
	}

	var params []byte
	var err error
	if bean.DeployChannel == db.DeployKubernetesChannel {
		params, err = json.Marshal(bean.KubernetesDeployParams)
	}
	if bean.DeployChannel == db.DeployJenkinsChannel {
		params, err = json.Marshal(bean.JenkinsDeployParams)
	}
	if err != nil {
		t.Result(-1, err.Error())
		return
	}
	bean.DeployParams = string(params)

	if err := bean.Save(); err != nil {
		t.Result(-1, err.Error())
		return
	}

	// check and save webhook
	if err := gitlab.Webhook.CheckAndSave(bean.ProjectID); err != nil {
		errMsg := errors.Errorf("save data success. but set gitlab repo webhook failed. error: %v", err)
		t.Result(-1, errMsg.Error())
		return
	}
	// save merge request check
	if err := gitlab.Project.UpdateMergeRequestCheck(bean.ProjectID); err != nil {
		t.Result(-1, err.Error())
		return
	}
	// set branch to protected
	if err := gitlab.Project.SetProtectedBranch(bean.ProjectID, bean.ProjectBranch); err != nil {
		t.Result(-1, err.Error())
		return
	}

	t.Result("save success")
}
