package controller

import (
	"fmt"
	"sort"
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"

	"gitlab.dailyyoga.com.cn/server/go-artifact/http"

	db "gitlab.dailyyoga.com.cn/rdc/serverops/database/domain"
	srv "gitlab.dailyyoga.com.cn/rdc/serverops/service/domain"
)

type domainService struct{}

var DomainService domainService

// DomainRecord 表示单条DNS记录

func (d *domainService) GetCDNDomainList(t *http.Tools) {
	project := t.GetFormStringD("project", "")
	var items []*db.CDNDomain
	if project == "all" {
		items = db.TbCDNDomain.GetList()
	} else {
		items = db.TbCDNDomain.GetListByProject(project)
	}
	resp := make([]*srv.CDNDomainItem, 0)
	cName := srv.Cname.GetCnameMap()

	// 使用 channel 来控制并发
	type result struct {
		domain *db.CDNDomain
		record *srv.Record
	}
	resultChan := make(chan result, len(items))
	semaphore := make(chan struct{}, 5) // 控制最大并发数为5

	for _, domain := range items {
		go func(domain *db.CDNDomain) {
			semaphore <- struct{}{} // 获取信号量
			record := srv.AliApi.GetDomainRecord(domain.DomainName, domain.HostRecord, domain.Project)
			resultChan <- result{domain: domain, record: record}
			<-semaphore // 释放信号量
		}(domain)
	}

	// 收集结果
	for range items {
		result := <-resultChan
		if result.record != nil {
			timestamp := result.record.UpdateTimestamp
			nanoseconds := timestamp * 1e6
			updateTime := time.Unix(0, nanoseconds)
			thirdDomain, ok := cName[result.domain.ID]
			if !ok {
				thirdDomain = make([]*srv.CnameItem, 0)
			}
			resp = append(resp, &srv.CDNDomainItem{
				ID:          result.domain.ID,
				Project:     result.domain.Project,
				Remark:      result.domain.Remark,
				UpdateTimes: updateTime.Format("2006-01-02 15:04:05"),
				DomainName:  result.record.DomainName,
				HostRecord:  result.record.RR,
				CnameRecord: result.record.Value,
				Domain:      fmt.Sprintf("%s.%s", result.record.RR, result.record.DomainName),
				ThirdDomain: thirdDomain,
			})
			if result.domain.RecordID == "" || result.domain.RecordID != result.record.RecordID {
				result.domain.RecordID = result.record.RecordID
				if err := result.domain.Update(); err != nil {
					logger.Warn("update domain record failed, err: %v", err)
				}
			}
		}
	}
	close(resultChan)
	close(semaphore)
	sort.Slice(resp, func(i, j int) bool {
		return resp[i].ID < resp[j].ID
	})
	t.Result(resp)
}

type updateCDNDomainRecordReq struct {
	ID    int64  `json:"id"`
	Cname string `json:"cname"`
}

func (d *domainService) UpdateCDNDomainRecord(t *http.Tools) {
	payload := &updateCDNDomainRecordReq{}
	t.ParseBodyObject(payload)
	if payload.ID < 1 {
		t.Result(-1, "invalid params")
		return
	}
	if payload.Cname == "" {
		t.Result(-1, "invalid params")
		return
	}
	resp := srv.Cname.UpdateDomainRecord(payload.ID, payload.Cname)
	if resp != nil {
		t.Result(-1, "update domain record failed")
		return
	}
	t.Result(resp)
}
