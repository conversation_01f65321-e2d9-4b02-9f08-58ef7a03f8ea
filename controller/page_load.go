package controller

import (
	"strconv"
	"time"

	"gitlab.dailyyoga.com.cn/rdc/serverops/database/indicator"
	"gitlab.dailyyoga.com.cn/server/go-artifact/http"
)

type pageLoad struct{}

var PageLoad pageLoad

var fetchMetrics = []string{
	indicator.MetricType.AvgLoad, indicator.MetricType.AvgTCP,
	indicator.MetricType.AvgReady, indicator.MetricType.AvgDNS,
	indicator.MetricType.AvgFpt,
}

func (p *pageLoad) TaskLists(t *http.Tools) {
	ret := make([]indicator.PageLoadTask, 0)

	res := indicator.TbPageLoadTask.GetAll()
	for _, v := range res {
		ret = append(ret, v)
	}
	t.Result(ret)
}

type pageLoadQueryReqeust struct {
	PID       string `json:"pid"`
	StartDate int    `json:"start_date"`
	EndDate   int    `json:"end_date"`
}

func (p *pageLoad) QueryMetrics(t *http.Tools) {
	req := &pageLoadQueryReqeust{}
	t.ParseBodyObject(req)
	if req.StartDate == 0 {
		req.StartDate, _ = strconv.Atoi(time.Now().AddDate(0, 0, -30).Format("20060102"))
	}
	if req.EndDate == 0 {
		req.EndDate, _ = strconv.Atoi(time.Now().AddDate(0, 0, -1).Format("20060102"))
	}
	fetchRequest := indicator.FetchPageLoadMetricsRequest{
		StartTime: int64(req.StartDate), EndTime: int64(req.EndDate),
		Metric: fetchMetrics,
		PID:    []string{},
	}
	if req.PID != "" {
		fetchRequest.PID = append(fetchRequest.PID, req.PID)
	}
	ret := indicator.TbPageLoadMetrics.FetchData(&fetchRequest)
	t.Result(ret)
}

func (p *pageLoad) AllTaskMetrics(t *http.Tools) {
	req := &pageLoadQueryReqeust{}
	t.ParseBodyObject(req)
	if req.StartDate == 0 {
		req.StartDate, _ = strconv.Atoi(time.Now().AddDate(0, 0, -30).Format("20060102"))
	}
	if req.EndDate == 0 {
		req.EndDate, _ = strconv.Atoi(time.Now().AddDate(0, 0, -1).Format("20060102"))
	}

	// fetch all task
	tasks := indicator.TbPageLoadTask.GetAll()
	if len(tasks) == 0 {
		t.Result([]interface{}{})
		return
	}
	fetchRequest := indicator.FetchPageLoadMetricsRequest{
		StartTime: int64(req.StartDate), EndTime: int64(req.EndDate),
		Metric: fetchMetrics,
		PID:    make([]string, 0),
	}
	for _, v := range tasks {
		fetchRequest.PID = append(fetchRequest.PID, v.PID)
	}

	ret := indicator.TbPageLoadMetrics.FetchData(&fetchRequest)
	for i, v := range ret {
		if taskItem, ok := tasks[v.PID]; ok {
			ret[i].AppName = taskItem.AppName
		}
	}
	t.Result(ret)
}
