package controller

import (
	"gitlab.dailyyoga.com.cn/rdc/serverops/database/indicator"
	"gitlab.dailyyoga.com.cn/rdc/serverops/library"
	"gitlab.dailyyoga.com.cn/server/go-artifact/http"
)

type crashStuck struct{}

var CrashStuck crashStuck

func (c *crashStuck) Lists(t *http.Tools) {
	req := &indicator.CrashStuckSearchPayload{}
	t.ParseBodyObject(req)
	if req.TableType == "" {
		req.TableType = indicator.TableCrash
	}

	res := indicator.TbCrashStuck.Search(req)
	if len(res) == 0 {
		t.Result([]indicator.CrashStuck{})
		return
	}
	t.Result(res)
}

func (c *crashStuck) Save(t *http.Tools) {
	req := &indicator.CrashStuck{}
	t.ParseBodyObject(req)
	if req.TableType == "" || req.TableType == indicator.TableUnknown {
		t.Result(-1, "未指定记录类型")
		return
	}
	if req.Day == "" || req.OnlineVersionUser < 1 || req.TotalUser < 1 {
		t.Result(-1, "参数异常")
		return
	}
	if err := req.Save(); err != nil {
		t.Result(-1, err.Error())
		return
	}
	t.Result(library.OperationResponse{Result: true})
}

func (c *crashStuck) Delete(t *http.Tools) {
	req := &indicator.CrashStuck{}
	t.ParseBodyObject(req)
	if req.TableType == "" || req.TableType == indicator.TableUnknown {
		t.Result(-1, "未指定要删除的记录类型")
		return
	}
	if req.ID <= 0 {
		t.Result(-1, "未指定需要删除的记录ID")
		return
	}

	if err := req.Delete(); err != nil {
		t.Result(-1, err.Error())
		return
	}
	t.Result(library.OperationResponse{Result: true})
}
