package controller

import (
	"gitlab.dailyyoga.com.cn/rdc/serverops/database/tapd"
	"gitlab.dailyyoga.com.cn/server/go-artifact/http"
)

type iteration struct{}

var Iteration iteration

func (i *iteration) GetItem(h *http.Tools) {
	bean := &tapd.Iteration{}
	h.ParseBodyObject(bean)

	item := tapd.TbIteration.GetItem(bean.IterationID)
	if item == nil {
		h.Result(-1, "iteration not found")
		return
	}
	h.Result(item)
}

type story struct{}

var Story story

func (s *story) FetchStoriesWithIteration(t *http.Tools) {
	bean := &checkTask{}
	t.ParseBodyObject(bean)
	if bean.Start == 0 && bean.End == 0 {
		if bean.Year == 0 {
			t.Result(-1, "year is 0")
			return
		}

		if bean.Month == 0 {
			t.Result(-1, "month is 0")
			return
		}
	} else {
		if bean.Start == 0 {
			t.Result(-1, "start is 0")
			return
		}

		if bean.End < bean.Start {
			t.Result(-1, "end < start")
			return
		}
	}

	lists := tapd.TbStory.FetchStories(bean.Start, bean.End, bean.Year, bean.Month)
	if len(lists) == 0 {
		t.Result([]*tapd.StoriesItem{})
		return
	}
	t.Result(lists)
}

type task struct{}

var Task task

func (t *task) Lists(h *http.Tools) {
	bean := &checkTask{}
	h.ParseBodyObject(bean)
	if bean.Start == 0 && bean.End == 0 {
		if bean.Year == 0 {
			h.Result(-1, "year is 0")
			return
		}

		if bean.Month == 0 {
			h.Result(-1, "month is 0")
			return
		}
	} else {
		if bean.Start == 0 {
			h.Result(-1, "start is 0")
			return
		}

		if bean.End < bean.Start {
			h.Result(-1, "end < start")
			return
		}
	}

	lists := tapd.TbTask.Lists(bean.Start, bean.End, bean.Year, bean.Month)
	if len(lists) == 0 {
		lists = make([]*tapd.Task, 0)
	}
	h.Result(lists)
}

type checkTask struct {
	Start int64 `json:"start"`
	End   int64 `json:"end"`
	Year  int64 `json:"year"`
	Month int64 `json:"month"`
}

type owner struct{}

var Owner owner

func (o *owner) Lists(h *http.Tools) {
	bean := &checkOwner{}
	h.ParseBodyObject(bean)

	lists := tapd.TbOwner.List(bean.Start, bean.End)
	if len(lists) == 0 {
		lists = make([]*tapd.Owner, 0)
	}
	h.Result(lists)
}

type checkOwner struct {
	Start int64 `json:"start"`
	End   int64 `json:"end"`
}

type sign struct{}

var Sign sign

func (o *sign) Lists(h *http.Tools) {
	bean := &checkSign{}
	h.ParseBodyObject(bean)

	lists := tapd.TbSign.List(bean.Start, bean.End)
	if len(lists) == 0 {
		lists = make([]*tapd.Sign, 0)
	}
	h.Result(lists)
}

type checkSign struct {
	Start int64 `json:"start"`
	End   int64 `json:"end"`
}
