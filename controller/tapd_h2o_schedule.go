package controller

import (
	"gitlab.dailyyoga.com.cn/rdc/serverops/database/tapd"
	"gitlab.dailyyoga.com.cn/server/go-artifact/http"
)

type h2oTask struct{}

var H2oTask h2oTask

func (t *h2oTask) Lists(h *http.Tools) {
	bean := &checkTask{}
	h.ParseBodyObject(bean)

	if bean.Start == 0 {
		h.Result(-1, "start is 0")
		return
	}

	if bean.End < bean.Start {
		h.Result(-1, "end < start")
		return
	}

	lists := tapd.TbH2oTask.Lists(bean.Start, bean.End)
	if len(lists) == 0 {
		lists = make([]*tapd.H2oTask, 0)
	}
	h.Result(lists)
}

type h2oOwner struct{}

var H2oOwner h2oOwner

func (o *h2oOwner) Lists(h *http.Tools) {
	bean := &checkOwner{}
	h.ParseBodyObject(bean)

	lists := tapd.TbH2oOwner.List(bean.Start, bean.End)
	if len(lists) == 0 {
		lists = make([]*tapd.H2oOwner, 0)
	}
	h.Result(lists)
}

type h2oSign struct{}

var H2oSign h2oSign

func (o *h2oSign) Lists(h *http.Tools) {
	bean := &checkSign{}
	h.ParseBodyObject(bean)

	lists := tapd.TbH2oSign.List(bean.Start, bean.End)
	if len(lists) == 0 {
		lists = make([]*tapd.H2oSign, 0)
	}
	h.Result(lists)
}
