package controller

import (
	"crypto/md5"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	netHttp "net/http"
	"strings"
	"time"

	"github.com/pkg/errors"
	safelygo "gitlab.dailyyoga.com.cn/gokit/safely-go"
	"gitlab.dailyyoga.com.cn/rdc/serverops/database/releasepipeline"
	"gitlab.dailyyoga.com.cn/rdc/serverops/library"
	runner "gitlab.dailyyoga.com.cn/rdc/serverops/service/releasepipeline"
	"gitlab.dailyyoga.com.cn/server/go-artifact/http"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/go-artifact/wework/robot"
	"golang.org/x/exp/slices"
)

type releasePipeline struct{}

var ReleasePipeline releasePipeline

type pipelineResponse struct {
	PipelineInfo *releasepipeline.Pipeline `json:"pipeline_info"`
	Stages       []*releasepipeline.Stage  `json:"stages"`
}

func (r *releasePipeline) List(t *http.Tools) {
	search := &releasepipeline.SearchRequest{}
	t.ParseBodyObject(&search)

	data := releasepipeline.TbPipeline.Search(search)
	ret := make([]*pipelineResponse, 0)
	pipelineIDs := make([]int64, 0)
	for _, item := range data {
		pipelineIDs = append(pipelineIDs, item.ID)
	}

	pipelineStages := releasepipeline.TbStage.FetchWithMultiPipeline(pipelineIDs)
	for _, item := range data {
		resp := &pipelineResponse{
			PipelineInfo: item,
			Stages:       make([]*releasepipeline.Stage, 0),
		}
		if pipelineStages != nil {
			if stages, ok := pipelineStages[item.ID]; ok {
				resp.Stages = stages
			}
		}
		ret = append(ret, resp)
	}
	t.Result(struct {
		Data []*pipelineResponse `json:"data"`
	}{
		Data: ret,
	})
}

func (r *releasePipeline) Get(t *http.Tools) {
	id := t.GetFormInt64D("id", 0)
	if id < 1 {
		t.Result(-1, "invalid id")
		return
	}

	// get pipeline base info
	pipelineInfo := releasepipeline.TbPipeline.GetItem(id)
	if pipelineInfo == nil || pipelineInfo.ID == 0 {
		t.Result(-1, "record not found")
		return
	}

	// fetch pipeline stages
	stages := releasepipeline.TbStage.FetchWithPipeline(pipelineInfo.ID)
	if len(stages) == 0 {
		t.Result(-1, "pipeline stages not found")
		return
	}
	t.Result(&pipelineResponse{PipelineInfo: pipelineInfo, Stages: stages})
}

type confirmRequest struct {
	Operation         string   `json:"operation"`
	PipelineID        int64    `json:"pipeline_id"`
	StageID           int64    `json:"stage_id"`
	ConfirmUser       string   `json:"confirm_user"`
	EvidenceMaterials []string `json:"evidence_materials,omitempty"`
	Remark            string   `json:"remark,omitempty"`
}

func (c *confirmRequest) validation() (*releasepipeline.Stage, error) {
	if c.PipelineID == 0 {
		return nil, errors.New("invalid pipeline id")
	}
	if c.StageID == 0 {
		return nil, errors.New("invalid stage id")
	}
	stage := releasepipeline.TbStage.GetItem(c.StageID)
	if stage == nil || stage.ID == 0 {
		return nil, errors.New("invalid stage id")
	}
	if stage.Status != releasepipeline.StatusRunning {
		return nil, errors.New("invalid stage status")
	}
	if c.Operation == "confirm" && stage.NeedEvidence && len(c.EvidenceMaterials) == 0 {
		return nil, errors.New("invalid evidence")
	}
	if c.ConfirmUser == "" {
		return nil, errors.New("invalid confirm user")
	}
	return stage, nil
}

func (r *releasePipeline) Cancel(t *http.Tools) {
	request := &confirmRequest{}
	t.ParseBodyObject(&request)

	if request.Operation != "cancel" {
		t.Result(-1, "invalid operation")
		return
	}
	if request.PipelineID == 0 {
		t.Result(-1, "invalid pipeline id")
		return
	}
	pipelineInfo := releasepipeline.TbPipeline.GetItem(request.PipelineID)
	if pipelineInfo == nil || pipelineInfo.ID == 0 {
		t.Result(-1, "pipeline record not found")
		return
	}
	if pipelineInfo.Status != releasepipeline.StatusRunning {
		t.Result(-1, "invalid pipeline status")
		return
	}

	pipelineInfo.Status = releasepipeline.StatusCancelled
	if err := pipelineInfo.Update(); err != nil {
		t.Result(-1, fmt.Sprintf("failed to update pipeline info: %v", err))
		return
	}
	t.Result(library.OperationResponse{Result: true, Message: "cancel successfully"})
}

func (r *releasePipeline) Terminate(t *http.Tools) {
	request := &confirmRequest{}
	t.ParseBodyObject(&request)
	if request.Operation != "terminate" {
		t.Result(-1, "invalid operation")
		return
	}

	stage, err := request.validation()
	if err != nil {
		t.Result(-1, fmt.Sprintf("invalid request: %v", err))
		return
	}
	if !stage.AllowTerminate {
		t.Result(-1, "not allow terminate")
		return
	}

	pipelineInfo := releasepipeline.TbPipeline.GetItem(stage.PipelineID)
	if pipelineInfo == nil || pipelineInfo.ID == 0 {
		t.Result(-1, "pipeline not found")
		return
	}
	if pipelineInfo.Status != releasepipeline.StatusRunning {
		t.Result(-1, "invalid pipeline status")
		return
	}

	hasPermission := request.ConfirmUser == stage.ConfirmUser ||
		slices.Contains(strings.Split(pipelineInfo.CC, ";"), request.ConfirmUser)
	if !hasPermission {
		t.Result(-1, "no permission")
		return
	}

	pipelineInfo.Status = releasepipeline.StatusTerminate
	if err := pipelineInfo.Update(); err != nil {
		t.Result(-1, fmt.Sprintf("update pipeline info failed: %v", err))
		return
	}

	if request.Remark != "" {
		stage.Remark = request.Remark
	}
	stage.RealConfirmUser = request.ConfirmUser
	stage.Status = releasepipeline.StatusTerminate
	if err := stage.Update(); err != nil {
		t.Result(-1, fmt.Sprintf("update pipeline stage failed: %v", err))
		return
	}
	t.Result(library.OperationResponse{Result: true, Message: "terminate successfully"})
}

func (r *releasePipeline) Confirm(t *http.Tools) {
	request := &confirmRequest{}
	t.ParseBodyObject(&request)

	if request.Operation != "confirm" {
		t.Result(-1, "invalid operation")
		return
	}

	stage, err := request.validation()
	if err != nil {
		t.Result(-1, fmt.Sprintf("invalid confirm request: %v", err))
		return
	}

	pipelineInfo := releasepipeline.TbPipeline.GetItem(stage.PipelineID)
	if pipelineInfo == nil || pipelineInfo.ID == 0 {
		t.Result(-1, "pipeline not found")
		return
	}
	if pipelineInfo.Status != releasepipeline.StatusRunning {
		t.Result(-1, "invalid pipeline status")
		return
	}

	hasPermission := request.ConfirmUser == stage.ConfirmUser ||
		slices.Contains(strings.Split(pipelineInfo.CC, ";"), request.ConfirmUser)
	if !hasPermission {
		t.Result(-1, "no permission")
		return
	}

	if request.Remark != "" {
		stage.Remark = request.Remark
	}
	stage.Status = releasepipeline.StatusCompleted
	stage.ConfirmedAt = time.Now().Unix()
	stage.RealConfirmUser = stage.ConfirmUser
	stage.EvidenceMaterials = strings.Join(request.EvidenceMaterials, ";||;")
	if err := stage.Update(); err != nil {
		t.Result(-1, fmt.Sprintf("failed to update stage, err: %v", err))
		return
	}

	if stage.AfterConfirm != "" {
		afterConfirm := &runner.StageAfterConfirm{}
		if err := json.Unmarshal([]byte(stage.AfterConfirm), afterConfirm); err != nil {
			logger.Error("failed to unmarshal after confirm", "error", err)
		}
		if afterConfirm.WechatRobot != nil {
			safelygo.Run(func() {
				r.execAfterConfirmWechatRobotNotify(pipelineInfo, stage, afterConfirm.WechatRobot)
			})
		}
	}

	nextStage, isLast := r.next(stage)
	if isLast {
		r.completePipeline(pipelineInfo)
	}
	if nextStage != nil {
		runner.Runner.Run(nextStage.ID)
	}
	t.Result(library.OperationResponse{Result: true, Message: "confirm successfully"})
}

func (r *releasePipeline) completePipeline(pipelineInfo *releasepipeline.Pipeline) {
	if pipelineInfo == nil || pipelineInfo.ID == 0 {
		return
	}
	pipelineInfo.Status = releasepipeline.StatusCompleted
	if err := pipelineInfo.Update(); err != nil {
		logger.Error(err)
	}
}

func (r *releasePipeline) execAfterConfirmWechatRobotNotify(
	pipelineInfo *releasepipeline.Pipeline, stage *releasepipeline.Stage, msg *runner.WechatRobotMessage) {
	if msg == nil || msg.URL == "" || msg.Markdown == "" {
		return
	}

	stages := releasepipeline.TbStage.FetchWithPipeline(pipelineInfo.ID)
	if len(stages) < 2 {
		return
	}

	getImageStage := func(stageKey string) *releasepipeline.Stage {
		for _, stage := range stages {
			if stage.Key == stageKey {
				return stage
			}
		}
		return nil
	}

	getStageImageURL := func(stage *releasepipeline.Stage, index int) string {
		imageURLs := strings.Split(stage.EvidenceMaterials, ";||;")
		if index > len(imageURLs) {
			return ""
		}
		if index < 0 && len(imageURLs) > 1 && index >= -len(imageURLs) {
			return imageURLs[len(imageURLs)+index]
		}
		return imageURLs[index]
	}

	imageURLs := make([]string, 0)
	if msg.Images != nil {
		for _, msgImage := range msg.Images {
			if msgImage.Stage == stage.Key || strings.ToUpper(msgImage.Stage) == "CURRENT" {
				for _, index := range msgImage.ImageIndexs {
					imageURLs = append(imageURLs, getStageImageURL(stage, index))
				}
				continue
			}
			imageStage := getImageStage(msgImage.Stage)
			if imageStage == nil || imageStage.ID == 0 {
				continue
			}
			for _, index := range msgImage.ImageIndexs {
				imageURLs = append(imageURLs, getStageImageURL(imageStage, index))
			}
		}
	}

	// images
	if len(imageURLs) > 0 {
		for _, imageURL := range imageURLs {
			imageInfo, err := r.getImageMessage(imageURL)
			if err != nil {
				logger.Error(err)
			}
			robot.Get().AddMessage(&robot.Message{RobotURL: msg.URL, MsgType: robot.Image, Image: imageInfo})
		}
	}

	// markdown
	replaces := map[string]string{
		"{{APP_NAME}}":    pipelineInfo.AppName.String(),
		"{{OS}}":          pipelineInfo.OS.String(),
		"{{APP_VERSION}}": pipelineInfo.AppVersion,
	}
	content := msg.Markdown
	for k, v := range replaces {
		content = strings.ReplaceAll(content, k, v)
	}
	robot.Get().AddMessage(&robot.Message{RobotURL: msg.URL, MsgType: robot.Markdown, Markdown: &robot.MarkdownMessage{Content: content}})

	// mentioned_list
	if len(msg.MentionedList) > 0 {
		robot.Get().AddMessage(&robot.Message{RobotURL: msg.URL, MsgType: robot.Text, Text: &robot.TextMessage{MentionedList: msg.MentionedList}})
	}
}

func (r *releasePipeline) getImageMessage(image string) (*robot.ImageMessage, error) {
	imageMessage := &robot.ImageMessage{}

	response, err := netHttp.Get(image)
	if err != nil {
		return nil, err
	}
	defer response.Body.Close()

	imageData, err := io.ReadAll(response.Body)
	if err != nil {
		return nil, err
	}

	imageMessage.Base64 = base64.StdEncoding.EncodeToString(imageData)
	hash := md5.Sum(imageData)
	imageMessage.MD5 = hex.EncodeToString(hash[:])

	return imageMessage, nil
}

func (r *releasePipeline) next(stage *releasepipeline.Stage) (nextStage *releasepipeline.Stage, isLast bool) {
	stages := releasepipeline.TbStage.FetchWithPipeline(stage.PipelineID)
	if len(stages) == stage.Index {
		return nil, true
	}
	nextStage = releasepipeline.TbStage.GetNext(stage.PipelineID, stage.Index)
	if nextStage != nil {
		return nextStage, false
	}
	return
}
