package controller

import (
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"strings"
	"time"

	"gitlab.dailyyoga.com.cn/rdc/serverops/cache"
	"gitlab.dailyyoga.com.cn/rdc/serverops/library"
	"gitlab.dailyyoga.com.cn/server/go-artifact/http"
)

type yogaDelay struct{}

var YogaDelay yogaDelay

type topic struct {
	Name        string `json:"name"`
	CallbackURL string `json:"callback_url"`
	Timeout     int32  `json:"timeout"`
	CreateTime  int64  `json:"create_time"`
	JobTotal    int64  `json:"job_total,omitempty"`
}

func (t *topic) stringify() (string, error) {
	b, err := json.Marshal(t)
	return string(b), err
}

func (y *yogaDelay) ListTopic(t *http.Tools) {
	ctx := t.GetContext()
	res, err := cache.GetYogaNewRedis().HGetAll(ctx, library.YogaDelayTopicKey).Result()
	if err != nil {
		t.Result(-1, err.<PERSON><PERSON>r())
		return
	}

	ret := make([]*topic, 0)

	for _, v := range res {
		item := &topic{}
		v = strings.ReplaceAll(v, `\`, "")
		if err := json.Unmarshal([]byte(v), item); err != nil {
			continue
		}
		ret = append(ret, item)
	}

	// sort by time and name
	sort.Slice(ret, func(i, j int) bool {
		return ret[i].CreateTime > ret[j].CreateTime
	})

	t.Result(ret)
}

func (y *yogaDelay) TopicDetail(t *http.Tools) {
	ctx := t.GetContext()
	countJob := t.GetFormBoolD("count_job", false)
	topicName := t.GetFormStringD("topic", "")
	if topicName == "" {
		t.Result(-1, "empty topic")
		return
	}

	payload, err := cache.GetYogaNewRedis().HGet(ctx, library.YogaDelayTopicKey, topicName).Result()
	if err != nil {
		t.Result(-1, err.Error())
		return
	}
	item := &topic{}
	payload = strings.ReplaceAll(payload, `\`, "")
	if err := json.Unmarshal([]byte(payload), item); err != nil {
		t.Result(-1, err.Error())
		return
	}

	if item.Name != "" && item.CallbackURL != "" && countJob {
		item.JobTotal = y.getTopicJobTotal(ctx, topicName)
	}
	t.Result(item)
}

func (y *yogaDelay) DeleteTopic(t *http.Tools) {
	ctx := t.GetContext()
	payload := &topic{}
	t.ParseBodyObject(payload)
	if payload.Name == "" {
		t.Result(-1, "empty topic")
		return
	}

	if err := cache.GetYogaNewRedis().HDel(ctx, library.YogaDelayTopicKey, payload.Name).Err(); err != nil {
		t.Result(-1, err.Error())
		return
	}
	t.Result(library.OperationResponse{Result: true})
}

func (y *yogaDelay) getTopicJobTotal(ctx context.Context, topicName string) int64 {
	rd := cache.GetYogaNewRedis()
	bucketCnt := y.getBucketCount(ctx)
	topicJob := 0
	if bucketCnt > 0 {
		for i := 0; i < bucketCnt; i++ {
			setKey := fmt.Sprintf(library.YogaDelayBucketSetKey, i)

			var cursor uint64
			for {
				keys, newCursor := rd.ZScan(ctx, setKey, cursor, fmt.Sprintf("%s:*", topicName), 100).Val()
				topicJob += len(keys) / 2
				if newCursor == 0 {
					break
				}
				cursor = newCursor
			}
		}
	}
	return int64(topicJob)
}

func (y *yogaDelay) SaveTopic(t *http.Tools) {
	ctx := t.GetContext()
	payload := &topic{}
	t.ParseBodyObject(payload)
	if payload.Name == "" || payload.CallbackURL == "" {
		t.Result(-1, "name and callback_url required")
		return
	}
	if payload.CreateTime == 0 {
		payload.CreateTime = time.Now().Unix()
	}
	if payload.Timeout == 0 {
		payload.Timeout = 5
	}

	value, err := payload.stringify()
	if err != nil {
		t.Result(-1, err.Error())
		return
	}

	if err := cache.GetYogaNewRedis().HSet(ctx, library.YogaDelayTopicKey, payload.Name, value).Err(); err != nil {
		t.Result(-1, err.Error())
		return
	}
	t.Result(&library.OperationResponse{Result: true})
}

func (y *yogaDelay) BucketInfo(t *http.Tools) {
	ctx := t.GetContext()

	rd := cache.GetYogaNewRedis()

	// counter total job
	bucketCnt := y.getBucketCount(ctx)
	var jobNumber int64 = 0
	if bucketCnt > 0 {
		for i := 0; i < bucketCnt; i++ {
			setKey := fmt.Sprintf(library.YogaDelayBucketSetKey, i)
			jobNumber += rd.ZCard(ctx, setKey).Val()
		}
	}

	type bucketTotal struct {
		BucketCount int   `json:"bucket_count"`
		JobCount    int64 `json:"job_count"`
	}
	t.Result(&bucketTotal{BucketCount: bucketCnt, JobCount: jobNumber})
}

func (y *yogaDelay) getBucketCount(ctx context.Context) int {
	cnt, err := cache.GetYogaNewRedis().Get(ctx, library.YogaDelayBucketCountKey).Int()
	if err != nil {
		return 0
	}
	return cnt
}
