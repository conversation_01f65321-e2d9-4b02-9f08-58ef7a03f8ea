package controller

import (
	"bytes"
	"encoding/json"
	"io"
	httplib "net/http"

	"gitlab.dailyyoga.com.cn/server/go-artifact/http"
)

type argocd struct{}

var ArgoCD argocd

func (a *argocd) FetchToken(t *http.Tools) {
	type ArgoCDLoginRequest struct {
		Username string `json:"username"`
		Password string `json:"password"`
	}

	type ArgoCDLoginResponse struct {
		Token string `json:"token"`
	}

	loginReq := &ArgoCDLoginRequest{
		Username: "admin",
		Password: "ZmfhpKJ6oGGBAyL5",
	}
	loginReqBytes, err := json.Marshal(loginReq)
	if err != nil {
		t.Result(-1, "获取ArgoCD token失败: "+err.Error())
		return
	}

	resp, err := httplib.DefaultClient.Post("https://argocd.dailyyoga.com.cn/api/v1/session", "application/json", bytes.NewReader(loginReqBytes))
	if err != nil {
		t.Result(-1, "获取ArgoCD token失败: "+err.Error())
		return
	}
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		t.Result(-1, "获取ArgoCD token失败: "+err.Error())
		return
	}

	token := &ArgoCDLoginResponse{}
	err = json.Unmarshal(body, token)
	if err != nil {
		t.Result(-1, "获取ArgoCD token失败: "+err.Error())
		return
	}
	if token.Token == "" {
		t.Result(-1, "获取ArgoCD token失败: 返回的token为空")
		return
	}

	t.Result(token)
}
