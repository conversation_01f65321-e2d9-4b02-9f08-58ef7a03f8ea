package controller

import (
	db "gitlab.dailyyoga.com.cn/rdc/serverops/database/deployment"
	"gitlab.dailyyoga.com.cn/rdc/serverops/service/k8s"
	"gitlab.dailyyoga.com.cn/server/go-artifact/http"
)

type deployment struct{}

var Deployment deployment

func (d *deployment) Configs(t *http.Tools) {
	type config struct {
		DeployChannels []db.DeployChannel `json:"deploy_channels"`
		Kubernetes     struct {
			ClusterName []db.ClusterName         `json:"cluster_name"`
			Namespaces  []db.KubernetesNamespace `json:"namespaces"`
		} `json:"kubernetes"`
	}

	ret := &config{}

	ret.DeployChannels = []db.DeployChannel{
		db.DeployKubernetesChannel,
		db.DeployJenkinsChannel,
		db.DeployGitlabCIChannel,
	}

	ret.Kubernetes.ClusterName = []db.ClusterName{
		db.DevCluster, db.ProdCluster,
	}
	ret.Kubernetes.Namespaces = []db.KubernetesNamespace{
		db.DevNamespace, db.MirrorNamespace, db.ProductNamespace,
		db.DevInternalNamespace, db.InternalNamespace,
		db.DevChildrenNamespace, db.MirrorChildrenNamespace, db.ProductChildrenNamespace,
	}

	t.Result(ret)
}

func (d *deployment) ListKubernetesDeployments(t *http.Tools) {
	clusterName := t.GetFormStringD("cluster_name", "")
	namespace := t.GetFormStringD("namespace", "")
	if clusterName == "" || namespace == "" {
		t.Result(-1, "invalid params")
		return
	}

	deployments := k8s.New(
		db.ClusterName(clusterName), db.KubernetesNamespace(namespace)).GetDeploymentClient().Lists(t.GetContext())
	t.Result(deployments)
}
