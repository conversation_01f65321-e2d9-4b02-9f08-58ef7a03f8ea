package controller

import (
	"encoding/json"
	"strconv"

	"gitlab.dailyyoga.com.cn/rdc/serverops/database"
	"gitlab.dailyyoga.com.cn/rdc/serverops/database/channel"
	"gitlab.dailyyoga.com.cn/rdc/serverops/service/ali"
	"gitlab.dailyyoga.com.cn/server/go-artifact/http"
)

type androidSigned struct{}

var AndroidSigned androidSigned

type Metadata struct {
	Name  string `json:"name"`
	Value string `json:"value"`
}

type SigningConfig struct {
	StoreFilePath string `json:"storeFilePath"`
	StorePassword string `json:"storePassword"`
	KeyAlias      string `json:"keyAlias"`
	KeyPassword   string `json:"keyPassword"`
}

type ABIConfig struct {
	Arm64V8A   string `json:"arm64-v8a,omitempty"`
	ArmeabiV7A string `json:"armeabi-v7a,omitempty"`
	All        string `json:"all,omitempty"`
}

type PatternItem struct {
	Channel       string    `json:"channel"`
	AbiPatternMap ABIConfig `json:"abiPatternMap"`
}

type ApkInfo struct {
	Arch              string `json:"arch"`
	Name              string `json:"name"`
	DownloadURL       string `json:"downloadURL"`
	DownloadURLExpire int    `json:"downloadURLExpire"`
}

type SignedResponse struct {
	UseIJiaMi         bool          `json:"useIJiaMi"`
	MinSdkVersion     int64         `json:"minSdkVersion"`
	OutDirPath        string        `json:"outDirPath"`
	BuildToolsVersion string        `json:"buildToolsVersion"`
	SdkDir            string        `json:"sdkDir"`
	FlavorList        []Metadata    `json:"flavorList"`
	SigningConfig     SigningConfig `json:"signingConfig"`
	ManifestMetaData  Metadata      `json:"manifestMetaData"`
	Patterns          []PatternItem `json:"patterns"`
	ApkList           []ApkInfo     `json:"apkList"`
}

func (a *androidSigned) Conf(t *http.Tools) {
	flavor := t.GetFormStringD("flavor", "")
	version := t.GetFormStringD("version", "")
	if flavor == "" || version == "" {
		t.Result(-1, "版本号或渠道未指定")
		return
	}

	ret := &SignedResponse{
		FlavorList: make([]Metadata, 0),
		Patterns:   make([]PatternItem, 0),
		ApkList:    make([]ApkInfo, 0),
	}
	channelID := ""
	// list
	channelList := channel.TbApkChannel.Lists()
	for _, item := range channelList {
		if item.ChannelName == flavor {
			channelID = item.ChannelID
		}
		ret.FlavorList = append(ret.FlavorList, Metadata{
			Name: item.ChannelName, Value: item.ChannelID,
		})
	}
	if channelID == "" {
		t.Result(-1, "未知的渠道，在渠道列表中未找到")
		return
	}
	// 设置其他配置字段
	if err := a.setRes(ret); err != nil {
		t.Result(-1, err.Error())
		return
	}
	// 从 oss 上获取 original apk 地址
	originalApk, err := ali.OSS.SearchOriginalAPKInfo(flavor, channelID, version)
	if err != nil {
		t.Result(-1, err.Error())
		return
	}
	for arch, apk := range originalApk {
		ret.ApkList = append(ret.ApkList, ApkInfo{
			Arch: arch, Name: apk.Name, DownloadURL: apk.DownloadURL, DownloadURLExpire: apk.ExpireSec,
		})
	}
	t.Result(ret)
}

func (a *androidSigned) setRes(ret *SignedResponse) error {
	keysConf := database.TbConfigKeys.FetchAll()
	var err error
	if v, ok := keysConf["ANDROID_USE_IJIAMI"]; ok {
		ret.UseIJiaMi, err = strconv.ParseBool(v.Value)
		if err != nil {
			return err
		}
	}
	if v, ok := keysConf["ANDROID_MIN_SDK_VERSION"]; ok {
		ret.MinSdkVersion, err = strconv.ParseInt(v.Value, 10, 64)
		if err != nil {
			return err
		}
	}
	if v, ok := keysConf["ANDROID_OUT_DIR_PATH"]; ok {
		ret.OutDirPath = v.Value
	}
	if v, ok := keysConf["ANDROID_BUILD_TOOLS_VERSION"]; ok {
		ret.BuildToolsVersion = v.Value
	}
	if v, ok := keysConf["ANDROID_SDK_DIR"]; ok {
		ret.SdkDir = v.Value
	}
	if v, ok := keysConf["ANDROID_SIGNING_CONFIG"]; ok {
		err = json.Unmarshal([]byte(v.Value), &ret.SigningConfig)
		if err != nil {
			return err
		}
	}
	if v, ok := keysConf["ANDROID_MANIFEST_METADATA"]; ok {
		err = json.Unmarshal([]byte(v.Value), &ret.ManifestMetaData)
		if err != nil {
			return err
		}
	}
	if v, ok := keysConf["ANDROID_PATTERNS"]; ok {
		err = json.Unmarshal([]byte(v.Value), &ret.Patterns)
		if err != nil {
			return err
		}
	}
	return nil
}
