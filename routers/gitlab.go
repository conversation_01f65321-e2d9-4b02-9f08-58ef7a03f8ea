package routers

import (
	"gitlab.dailyyoga.com.cn/rdc/serverops/controller/gitlab"
	"gitlab.dailyyoga.com.cn/rdc/serverops/middlewares"
	"gitlab.dailyyoga.com.cn/server/go-artifact/http"
)

// gitlab routers
func gitlabRouters(s *http.Server) {
	s.<PERSON>le("POST", "/gitlab/hook/handler", gitlab.Webhook.Handle, middlewares.VerifyGitlabToken)
	s.HandleGroup("/gitlab", func(s *http.Server) {
		s.HandleGroup("/groups", func(s *http.Server) {
			s.<PERSON>le("GET", "/list", gitlab.Groups.Lists)
			s.<PERSON>le("GET", "/projects", gitlab.Projects.Lists)
		})
		s.<PERSON>le("GET", "/project/branches", gitlab.Projects.Branches)
	})
}
