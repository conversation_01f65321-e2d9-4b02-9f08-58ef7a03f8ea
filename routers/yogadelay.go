package routers

import (
	"gitlab.dailyyoga.com.cn/rdc/serverops/controller"
	"gitlab.dailyyoga.com.cn/rdc/serverops/middlewares"
	"gitlab.dailyyoga.com.cn/server/go-artifact/http"
)

// yoga-delay
func yogaDelayRouters(s *http.Server) {
	s.HandleGroup("/yoga-delay", func(s *http.Server) {
		s.<PERSON>("GET", "/bucket/stat", controller.YogaDelay.BucketInfo,
			middlewares.Authorization(roleReadonly))
		s.HandleGroup("/topic", func(s *http.Server) {
			s.Handle("GET", "/lists", controller.YogaDelay.ListTopic,
				middlewares.Authorization(roleReadonly))
			s.<PERSON>("POST", "/save", controller.YogaDelay.SaveTopic,
				middlewares.Authorization(roleAdmin))
			s.<PERSON>("GET", "/detail", controller.YogaDelay.TopicDetail,
				middlewares.Authorization(roleReadonly))
			s.<PERSON><PERSON>("POST", "/delete", controller.YogaDelay.DeleteTopic,
				middlewares.Authorization(roleAdmin))
		})
	})
}
