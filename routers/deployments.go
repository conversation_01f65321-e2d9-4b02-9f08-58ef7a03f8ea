package routers

import (
	"gitlab.dailyyoga.com.cn/rdc/serverops/controller"
	"gitlab.dailyyoga.com.cn/rdc/serverops/middlewares"
	"gitlab.dailyyoga.com.cn/server/go-artifact/http"
)

// deployment manager
func deploymentRouters(s *http.Server) {
	s.HandleGroup("/deployment", func(s *http.Server) {
		s.<PERSON><PERSON>("GET", "/configs", controller.Deployment.Configs)
		s.<PERSON>le("GET", "/k8s/lists", controller.Deployment.ListKubernetesDeployments)
		s.HandleGroup("/mapping", func(s *http.Server) {
			s.<PERSON>le("GET", "/lists", controller.Mapping.Lists)
			s.<PERSON>le("POST", "/set", controller.Mapping.Set)
			s.Handle("POST", "/delete", controller.Mapping.Delete)
		})
	}, middlewares.Authorization(roleAdmin))
}
