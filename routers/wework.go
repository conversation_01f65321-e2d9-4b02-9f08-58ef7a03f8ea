package routers

import (
	"gitlab.dailyyoga.com.cn/rdc/serverops/controller"
	"gitlab.dailyyoga.com.cn/rdc/serverops/middlewares"
	"gitlab.dailyyoga.com.cn/server/go-artifact/http"
)

// wework
func weworkRouters(s *http.Server) {
	s.HandleGroup("/wework", func(s *http.Server) {
		s.<PERSON>("GET", "/agent/sync/users", controller.WeWork.AgentUsers,
			middlewares.Authorization(roleAdmin))
		s.<PERSON><PERSON>("GET", "/callback", controller.WeWork.CallbackInvalid)
		s.<PERSON>le("POST", "/callback", controller.WeWork.CallbackHandle)
		s.<PERSON><PERSON>("GET", "/auth/userinfo", controller.WeWork.GetAuthUserInfo)
		s.<PERSON>("GET", "/user/infos", controller.WeWork.GetUseInfo)
	})
}
