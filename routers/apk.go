package routers

import (
	"gitlab.dailyyoga.com.cn/rdc/serverops/controller"
	"gitlab.dailyyoga.com.cn/rdc/serverops/middlewares"
	"gitlab.dailyyoga.com.cn/server/go-artifact/http"
)

// apk manager
func apkRouters(s *http.Server) {
	s.<PERSON>le("GET", "/app/version", controller.AppPackage.Version)

	s.HandleGroup("/apk/channel", func(s *http.Server) {
		s.Handle("GET", "/lists", controller.ApkChannel.Lists)
		s.Handle("POST", "/delete", controller.ApkChannel.Delete)
		s.Handle("POST", "/save", controller.ApkChannel.Save)
	}, middlewares.Authorization(roleDevelop))
	s.HandleGroup("/apk/release", func(s *http.Server) {
		s.Handle("GET", "/lists", controller.AndroidRelease.Lists)
		s.Handle("GET", "/file-download-url", controller.AndroidRelease.DownloadURL)
	})
	s.<PERSON>le<PERSON>roup("/apk/builder", func(s *http.Server) {
		s.Handle("POST", "/set", controller.ApkBuilder.SetCommon)
		s.Handle("POST", "/omnichannel", controller.ApkBuilder.SetOmnichannel)
		s.Handle("POST", "/canary", controller.ApkBuilder.SetCanary)
	})

	s.Handle("GET", "/android/signed/conf", controller.AndroidSigned.Conf)
	s.Handle("GET", "/ipa/:id/mainifest.plist", controller.AppPackage.Plist)
	s.Handle("GET", "/ipanew/:id/mainifest.plist", controller.AppPackage.PlistNew)

	s.HandleGroup("/package", func(s *http.Server) {
		s.Handle("GET", "/list", controller.AppPackage.List)
		s.Handle("GET", "/size", controller.AppPackage.SizeInfo)
		s.Handle("GET", "/bucket/list", controller.AppPackage.ListDirectory)
		s.Handle("POST", "/new", controller.AppPackage.ReportNew)
	})
}
