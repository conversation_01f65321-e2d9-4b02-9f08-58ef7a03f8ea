package routers

import (
	"gitlab.dailyyoga.com.cn/rdc/serverops/controller"
	"gitlab.dailyyoga.com.cn/server/go-artifact/http"
)

func tapdTasksRouters(s *http.Server) {
	// h2
	s.HandleGroup("/tapd", func(s *http.Server) {
		s.<PERSON><PERSON>("GET", "/iteration", controller.Iteration.GetItem)
		s.<PERSON>("GET", "/stories", controller.Story.FetchStoriesWithIteration)
		s.<PERSON>("GET", "/tasks", controller.Task.Lists)
		s.<PERSON>le("GET", "/owner", controller.Owner.Lists)
		s.Handle("GET", "/developer_sign", controller.Sign.Lists)
	})

	// h2o
	s.HandleGroup("/tapd-h2o", func(s *http.Server) {
		s.Handle("GET", "/tasks", controller.H2oTask.Lists)
		s.Handle("GET", "/owner", controller.H2oOwner.Lists)
		s.<PERSON>le("GET", "/developer_sign", controller.H2oSign.Lists)
	})
}
