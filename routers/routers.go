package routers

import (
	"gitlab.dailyyoga.com.cn/rdc/serverops/controller"
	"gitlab.dailyyoga.com.cn/server/go-artifact/http"
)

var routerFuncs = []func(s *http.Server){
	apkRouters, configKeysRouters, deploymentRouters, etcdRouters, gitlabRouters,
	indicatorRouters, jobRouters, miscRouters, releasePipelineRouters, tapdTasksRouters,
	userRouters, wafRouters, weworkRouters, yogaDelayRouters, domainCDNRouters,
}

func Initial(s *http.Server) {
	// login
	s.Handle("POST", "/login", controller.Login.Login, http.EnableResponseBodyRecord())

	for _, fn := range routerFuncs {
		fn(s)
	}
}
