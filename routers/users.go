package routers

import (
	"gitlab.dailyyoga.com.cn/rdc/serverops/controller"
	"gitlab.dailyyoga.com.cn/rdc/serverops/middlewares"
	"gitlab.dailyyoga.com.cn/server/go-artifact/http"
)

// user
func userRouters(s *http.Server) {
	s.HandleGroup("/users", func(s *http.Server) {
		s.Handle("GET", "/list", controller.Users.List)
		s.Handle("POST", "/new", controller.Users.New)
		s.Handle("POST", "/delete/:admin_id", controller.Users.Delete)
		s.Handle("POST", "/update/:admin_id", controller.Users.Update)
	}, middlewares.Authorization(roleAdmin))
}
