package routers

import (
	"gitlab.dailyyoga.com.cn/rdc/serverops/controller"
	"gitlab.dailyyoga.com.cn/rdc/serverops/middlewares"
	"gitlab.dailyyoga.com.cn/server/go-artifact/http"
)

// waf
func wafRouters(s *http.Server) {
	s.HandleGroup("/waf", func(s *http.Server) {
		s.HandleGroup("/risk-ip", func(s *http.Server) {
			s.<PERSON>le("GET", "/find", controller.RiskIP.Find)
			s.Handle("POST", "/delete", controller.RiskIP.Delete)
			s.Handle("GET", "/count", controller.RiskIP.Count)
			s.Handle("POST", "/set", controller.RiskIP.Set)
		})
	}, middlewares.Authorization(roleDevelop))
}
