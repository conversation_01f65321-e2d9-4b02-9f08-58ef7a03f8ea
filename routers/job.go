package routers

import (
	"gitlab.dailyyoga.com.cn/rdc/serverops/controller"
	"gitlab.dailyyoga.com.cn/rdc/serverops/controller/cronmanager"
	"gitlab.dailyyoga.com.cn/server/go-artifact/http"
)

// jobs manager
func jobRouters(s *http.Server) {
	// k8s serverless job create
	s.HandleGroup("/job", func(s *http.Server) {
		s.Handle("POST", "/set", controller.Job.Create)
	})

	// cronjob manager
	s.HandleGroup("/cm", func(s *http.Server) {
		s.Handle("GET", "/services", cronmanager.Cron.GetServiceInfo)
		s.Handle("GET", "/task/logs", cronmanager.Cron.GetTaskLogs)
	})
}
