package routers

import (
	"gitlab.dailyyoga.com.cn/rdc/serverops/controller"
	"gitlab.dailyyoga.com.cn/rdc/serverops/middlewares"
	"gitlab.dailyyoga.com.cn/server/go-artifact/http"
)

// etcd
func etcdRouters(s *http.Server) {
	s.HandleGroup("/etcd/:env", func(s *http.Server) {
		s.<PERSON>("GET", "/cluster/status", controller.Etcd.ClusterStatus,
			middlewares.Authorization(roleReadonly))
		s.<PERSON><PERSON>("GET", "/service/fetch", controller.Etcd.ServiceFetch,
			middlewares.Authorization(roleDevelop))
		s.HandleGroup("/kvs", func(s *http.Server) {
			s.Handle("POST", "/set", controller.Etcd.Set, middlewares.Authorization(roleAdmin))
			s.<PERSON>le("GET", "/get", controller.Etcd.Get, middlewares.Authorization(roleReadonly))
			s.<PERSON>le("POST", "/delete", controller.Etcd.Del, middlewares.Authorization(roleAdmin))
			s.<PERSON>("GET", "/range", controller.Etcd.Range, middlewares.Authorization(roleReadonly))
		})
	})
}
