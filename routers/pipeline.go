package routers

import (
	"gitlab.dailyyoga.com.cn/rdc/serverops/controller"
	"gitlab.dailyyoga.com.cn/rdc/serverops/middlewares"
	"gitlab.dailyyoga.com.cn/server/go-artifact/http"
)

// pipeline routers
func releasePipelineRouters(s *http.Server) {
	s.HandleGroup("/release/pipeline", func(s *http.Server) {
		s.<PERSON>le("GET", "/item", controller.ReleasePipeline.Get)
		s.Handle("GET", "/list", controller.ReleasePipeline.List)
		s.Handle("POST", "/confirm", controller.ReleasePipeline.Confirm)
		s.<PERSON>le("POST", "/terminate", controller.ReleasePipeline.Terminate)
		s.<PERSON>le("POST", "/cancel", controller.ReleasePipeline.Cancel,
			middlewares.Authorization(roleAdmin))
		s.Handle("POST", "/start", controller.AppPackage.StartPipeline,
			middlewares.Authorization(roleAdmin))
	})
}
