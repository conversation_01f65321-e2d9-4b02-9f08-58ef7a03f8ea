package routers

import (
	"gitlab.dailyyoga.com.cn/rdc/serverops/controller"
	"gitlab.dailyyoga.com.cn/rdc/serverops/middlewares"
	"gitlab.dailyyoga.com.cn/server/go-artifact/http"
)

// indicator
func indicatorRouters(s *http.Server) {
	s.HandleGroup("/indicator", func(s *http.Server) {
		s.HandleGroup("/slow-api", func(s *http.Server) {
			s.<PERSON><PERSON>("GET", "/lists", controller.SlowAPI.Lists)
			s.Handle("GET", "/summary", controller.SlowAPI.Summary)
		})
		s.HandleGroup("/crash-stuck", func(s *http.Server) {
			s.Handle("GET", "/lists", controller.CrashStuck.Lists)
			s.Handle("POST", "/delete", controller.CrashStuck.Delete)
			s.Handle("POST", "/save", controller.CrashStuck.Save)
		})
		s.HandleGroup("/page-load", func(s *http.Server) {
			s.<PERSON>("GET", "/tasks", controller.PageLoad.TaskLists)
			s.<PERSON>le("GET", "/tasks/metrics", controller.PageLoad.AllTaskMetrics)
			s.Handle("GET", "/metric", controller.PageLoad.QueryMetrics)
		})
	}, middlewares.Authorization(roleReadonly))
}
