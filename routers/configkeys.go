package routers

import (
	"gitlab.dailyyoga.com.cn/rdc/serverops/controller"
	"gitlab.dailyyoga.com.cn/rdc/serverops/middlewares"
	"gitlab.dailyyoga.com.cn/server/go-artifact/http"
)

// config keys
func configKeysRouters(s *http.Server) {
	s.HandleGroup("/config/keys", func(s *http.Server) {
		s.<PERSON>le("GET", "/lists", controller.ConfigKeys.Lists)
		s.Handle("POST", "/update", controller.ConfigKeys.Update)
	}, middlewares.Authorization(roleDevelop))

	s.HandleGroup("/config/key", func(s *http.Server) {
		s.<PERSON><PERSON>("GET", "/figma-mcp-token", controller.ConfigKeys.FigmaMcpToken)
	})
}
