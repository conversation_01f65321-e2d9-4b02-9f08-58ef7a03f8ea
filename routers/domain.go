package routers

import (
	"gitlab.dailyyoga.com.cn/rdc/serverops/controller"
	"gitlab.dailyyoga.com.cn/rdc/serverops/middlewares"
	"gitlab.dailyyoga.com.cn/server/go-artifact/http"
)

func domainCDNRouters(s *http.Server) {
	s.HandleGroup("/domain", func(s *http.Server) {
		s.HandleGroup("/cdn", func(s *http.Server) {
			s.<PERSON>("GET", "/list", controller.DomainService.GetCDNDomainList,
				middlewares.Authorization(roleDevelop))
			s.<PERSON>le("POST", "/update", controller.DomainService.UpdateCDNDomainRecord,
				middlewares.Authorization(roleAdmin))
		})
	})
}
